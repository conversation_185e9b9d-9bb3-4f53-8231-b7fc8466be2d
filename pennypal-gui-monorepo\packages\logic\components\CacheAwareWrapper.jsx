// CacheAwareWrapper.jsx - Universal wrapper for cache-aware components

import React, { useEffect, useState, useMemo } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';

/**
 * Universal cache-aware wrapper component
 * Handles cache checking, loading states, and data synchronization
 */
const CacheAwareWrapper = ({ 
  children, 
  cacheKeys = [], 
  fallbackActions = {},
  loadingComponent = null,
  errorComponent = null,
  onCacheUpdate = null,
  debugMode = false
}) => {
  const dispatch = useDispatch();
  const cache = useSelector(state => state.cache);
  const userId = getCurrentUserId();
  
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');

  // Check cache status for all required keys
  const checkCacheStatus = () => {
    if (!userId) {
      setHasError(true);
      setErrorMessage('User not authenticated');
      setIsLoading(false);
      return { allLoaded: false, anyLoading: false, hasErrors: true };
    }

    let allLoaded = true;
    let anyLoading = false;
    let hasErrors = false;
    const status = {};

    cacheKeys.forEach(key => {
      const loaded = cache?.[`${key}Loaded`] || false;
      const loading = cache?.[`${key}Loading`] || false;
      const error = cache?.[`${key}Error`] || null;
      const params = cache?.[`${key}Params`] || {};
      const userMatch = params.userId == userId;

      status[key] = { loaded, loading, error, userMatch };

      if (!loaded || !userMatch) allLoaded = false;
      if (loading) anyLoading = true;
      if (error) hasErrors = true;

      if (debugMode) {
        console.log(`Cache ${key}:`, { loaded, loading, error: !!error, userMatch });
      }
    });

    return { allLoaded, anyLoading, hasErrors, status };
  };

  // Dispatch fallback actions for uncached data
  const dispatchFallbackActions = (status) => {
    Object.keys(fallbackActions).forEach(key => {
      const cacheStatus = status[key];
      if (cacheStatus && (!cacheStatus.loaded || !cacheStatus.userMatch) && !cacheStatus.loading) {
        if (debugMode) {
          console.log(`🔄 Dispatching fallback action for ${key}`);
        }
        dispatch(fallbackActions[key]({ userId }));
      }
    });
  };

  // Main effect to monitor cache status
  useEffect(() => {
    const { allLoaded, anyLoading, hasErrors, status } = checkCacheStatus();

    if (debugMode) {
      console.log('Cache status check:', { allLoaded, anyLoading, hasErrors });
    }

    // Update loading state
    setIsLoading(anyLoading || (!allLoaded && cacheKeys.length > 0));

    // Update error state
    if (hasErrors) {
      setHasError(true);
      setErrorMessage('Failed to load some data');
    } else {
      setHasError(false);
      setErrorMessage('');
    }

    // Dispatch fallback actions if needed
    if (!anyLoading && !allLoaded) {
      dispatchFallbackActions(status);
    }

    // Call onCacheUpdate callback if provided
    if (onCacheUpdate && allLoaded) {
      onCacheUpdate(status);
    }

  }, [
    userId,
    ...cacheKeys.map(key => cache?.[`${key}Loaded`]),
    ...cacheKeys.map(key => cache?.[`${key}Loading`]),
    ...cacheKeys.map(key => cache?.[`${key}Error`])
  ]);

  // Show loading component if still loading
  if (isLoading && loadingComponent) {
    return loadingComponent;
  }

  // Show error component if there's an error
  if (hasError && errorComponent) {
    return React.cloneElement(errorComponent, { error: errorMessage });
  }

  // Render children with cache status as props
  return React.cloneElement(children, {
    cacheStatus: checkCacheStatus(),
    isLoading,
    hasError,
    errorMessage
  });
};

/**
 * Higher-order component for cache awareness
 */
export const withCacheAwareness = (
  WrappedComponent, 
  cacheConfig = {}
) => {
  return function CacheAwareComponent(props) {
    const {
      cacheKeys = [],
      fallbackActions = {},
      loadingComponent = <div>Loading...</div>,
      errorComponent = <div>Error loading data</div>,
      debugMode = false
    } = cacheConfig;

    return (
      <CacheAwareWrapper
        cacheKeys={cacheKeys}
        fallbackActions={fallbackActions}
        loadingComponent={loadingComponent}
        errorComponent={errorComponent}
        debugMode={debugMode}
        onCacheUpdate={(status) => {
          if (debugMode) {
            console.log(`Cache updated for ${WrappedComponent.name}:`, status);
          }
        }}
      >
        <WrappedComponent {...props} />
      </CacheAwareWrapper>
    );
  };
};

/**
 * Hook for cache awareness in functional components
 */
export const useCacheAwareness = (cacheKeys = [], debugMode = false) => {
  const cache = useSelector(state => state.cache);
  const userId = getCurrentUserId();
  const [isReady, setIsReady] = useState(false);

  // Memoize the cache status check to prevent infinite loops
  const cacheStatus = useMemo(() => {
    if (!userId || cacheKeys.length === 0) {
      return { allLoaded: true, anyLoading: false };
    }

    const allLoaded = cacheKeys.every(key => {
      const loaded = cache?.[`${key}Loaded`] || false;
      const params = cache?.[`${key}Params`] || {};
      const userMatch = params.userId == userId;
      return loaded && userMatch;
    });

    const anyLoading = cacheKeys.some(key => cache?.[`${key}Loading`] || false);

    return { allLoaded, anyLoading };
  }, [
    userId,
    cacheKeys,
    ...cacheKeys.map(key => cache?.[`${key}Loaded`]),
    ...cacheKeys.map(key => cache?.[`${key}Loading`]),
    ...cacheKeys.map(key => cache?.[`${key}Params`]?.userId)
  ]);

  useEffect(() => {
    const newIsReady = cacheStatus.allLoaded && !cacheStatus.anyLoading;

    if (newIsReady !== isReady) {
      setIsReady(newIsReady);

      if (debugMode) {
        console.log('Cache awareness update:', {
          cacheKeys,
          allLoaded: cacheStatus.allLoaded,
          anyLoading: cacheStatus.anyLoading,
          isReady: newIsReady
        });
      }
    }
  }, [cacheStatus.allLoaded, cacheStatus.anyLoading, isReady, debugMode, cacheKeys]);

  // Memoize the return object to prevent unnecessary re-renders
  return useMemo(() => ({
    isReady,
    cache,
    userId,
    getCacheData: (key) => cache?.[key],
    isCacheLoaded: (key) => cache?.[`${key}Loaded`] && cache?.[`${key}Params`]?.userId == userId,
    isCacheLoading: (key) => cache?.[`${key}Loading`] || false,
    getCacheError: (key) => cache?.[`${key}Error`] || null
  }), [isReady, cache, userId]);
};

export default CacheAwareWrapper;