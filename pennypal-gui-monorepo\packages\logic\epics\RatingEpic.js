// ratingEpics.js
import { ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { mergeMap, map, catchError, switchMap, filter, withLatestFrom } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig'; // Adjust path as needed
import { getCurrentUserId } from '../../web/src/utils/AuthUtil'; // Adjust path as needed
import {
  submitRatingRequest,
  submitRatingSuccess,
  submitRatingFailure,
  getUserRatingRequest,
  getUserRatingSuccess,
  getUserRatingFailure,
  getRatingStatsRequest,
  getRatingStatsSuccess,
  getRatingStatsFailure,
  updateRatingRequest,
  updateRatingSuccess,
  updateRatingFailure,
  deleteRatingRequest,
  deleteRatingSuccess,
  deleteRatingFailure,
  selectShouldFetchUserRating,
  selectShouldFetchStats,
} from '../redux/ratingSlice';

// Submit Rating Epic
export const submitRatingEpic = (action$) =>
  action$.pipe(
    ofType(submitRatingRequest.type),
    mergeMap((action) => {
      console.log('submitRatingEpic: Submitting rating with payload:', action.payload);
      
      return from(
        axiosInstance.post('/pennypal/api/ratings/submit', action.payload)
      ).pipe(
        map((response) => {
          console.log('submitRatingEpic: Rating submitted successfully:', response.data);
          return submitRatingSuccess(response.data.data);
        }),
        catchError((error) => {
          console.error('submitRatingEpic: Error submitting rating:', error);
          const errorMessage = error.response?.data?.message || 'Failed to submit rating';
          return of(submitRatingFailure(errorMessage));
        })
      );
    })
  );

// Get User Rating Epic (WITH CACHE CHECK)
export const getUserRatingEpic = (action$, state$) =>
  action$.pipe(
    ofType(getUserRatingRequest.type),
    withLatestFrom(state$),
    filter(([action, state]) => {
      const userId = action.payload || getCurrentUserId();
      const shouldFetch = selectShouldFetchUserRating(state, userId);
      console.log('getUserRatingEpic: Should fetch user rating?', shouldFetch, 'for user:', userId);
      return shouldFetch;
    }),
    switchMap(([action, state]) => {
      const userId = action.payload || getCurrentUserId();
      
      if (!userId) {
        console.error('getUserRatingEpic: User ID not found');
        return of(getUserRatingFailure('User ID not found'));
      }

      console.log('getUserRatingEpic: Fetching user rating for user:', userId);
      
      return from(
        axiosInstance.get(`/pennypal/api/ratings/user/${userId}`)
      ).pipe(
        map((response) => {
          console.log('getUserRatingEpic: User rating retrieved successfully:', response.data);
          return getUserRatingSuccess(response.data.data);
        }),
        catchError((error) => {
          console.error('getUserRatingEpic: Error getting user rating:', error);
          const errorMessage = error.response?.data?.message || 'Failed to get user rating';
          return of(getUserRatingFailure(errorMessage));
        })
      );
    })
  );

// Get Rating Stats Epic (WITH CACHE CHECK)
export const getRatingStatsEpic = (action$, state$) =>
  action$.pipe(
    ofType(getRatingStatsRequest.type),
    withLatestFrom(state$),
    filter(([action, state]) => {
      const userId = action.payload || getCurrentUserId();
      const shouldFetch = selectShouldFetchStats(state, userId);
      console.log('getRatingStatsEpic: Should fetch rating stats?', shouldFetch, 'for user:', userId);
      return shouldFetch;
    }),
    switchMap(([action, state]) => {
      const userId = action.payload || getCurrentUserId();
      
      if (!userId) {
        console.error('getRatingStatsEpic: User ID not found');
        return of(getRatingStatsFailure('User ID not found'));
      }

      console.log('getRatingStatsEpic: Fetching rating stats for user:', userId);
      
      return from(
        axiosInstance.get(`/pennypal/api/ratings/stats/${userId}`)
      ).pipe(
        map((response) => {
          console.log('getRatingStatsEpic: Rating stats retrieved successfully:', response.data);
          return getRatingStatsSuccess(response.data.data);
        }),
        catchError((error) => {
          console.error('getRatingStatsEpic: Error getting rating stats:', error);
          const errorMessage = error.response?.data?.message || 'Failed to get rating stats';
          return of(getRatingStatsFailure(errorMessage));
        })
      );
    })
  );

// Update Rating Epic
export const updateRatingEpic = (action$) =>
  action$.pipe(
    ofType(updateRatingRequest.type),
    mergeMap((action) => {
      console.log('updateRatingEpic: Updating rating with payload:', action.payload);
      
      return from(
        axiosInstance.put('/pennypal/api/ratings/update', action.payload)
      ).pipe(
        map((response) => {
          console.log('updateRatingEpic: Rating updated successfully:', response.data);
          return updateRatingSuccess(response.data.data);
        }),
        catchError((error) => {
          console.error('updateRatingEpic: Error updating rating:', error);
          const errorMessage = error.response?.data?.message || 'Failed to update rating';
          return of(updateRatingFailure(errorMessage));
        })
      );
    })
  );

// Delete Rating Epic
export const deleteRatingEpic = (action$) =>
  action$.pipe(
    ofType(deleteRatingRequest.type),
    switchMap((action) => {
      const userId = action.payload || getCurrentUserId();
      
      if (!userId) {
        console.error('deleteRatingEpic: User ID not found');
        return of(deleteRatingFailure('User ID not found'));
      }

      console.log('deleteRatingEpic: Deleting rating for user:', userId);
      
      return from(
        axiosInstance.delete(`/pennypal/api/ratings/user/${userId}`)
      ).pipe(
        map((response) => {
          console.log('deleteRatingEpic: Rating deleted successfully:', response.data);
          return deleteRatingSuccess(response.data.data);
        }),
        catchError((error) => {
          console.error('deleteRatingEpic: Error deleting rating:', error);
          const errorMessage = error.response?.data?.message || 'Failed to delete rating';
          return of(deleteRatingFailure(errorMessage));
        })
      );
    })
  );

// Combine all epics
export const ratingEpics = [
  submitRatingEpic,
  getUserRatingEpic,
  getRatingStatsEpic,
  updateRatingEpic,
  deleteRatingEpic,
];