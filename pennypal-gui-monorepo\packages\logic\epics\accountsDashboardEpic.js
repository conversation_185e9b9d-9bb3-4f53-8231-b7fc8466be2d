import { combineEpics, ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { mergeMap, map, catchError, tap, filter, takeUntil } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {  createReauthLinkToken,
  exchangeReauthToken,
  fetchLinkToken,
  fetchAccountDetails,
  exchangePublicToken,
  refreshAllAccounts,
  syncAccount,
  connectMx,
  connectStripe,
  connectFinicity,
  resetError,
  setIsLoading,
  fetchSankeyChartData,
  fetchSankeyChartDataSuccess,
  fetchSankeyChartDataFailure,
   fetchAccountBalanceMonthlyDeltas,
  fetchAccountBalanceMonthlyDeltasGrouped
} from '../redux/accountsDashboardSlice';
import {
  fetchAccountsRequest,
  fetchAccountsSuccess,
  fetchAccountsFailure,
} from '../redux/transactionSlice';

import { getCurrentUserId } from '../../web/src/utils/AuthUtil';

// Helper function for displaying notifications
const showNotification = (message, type = 'success') => {
  // This could be replaced with your actual notification system
  // For example, using react-toastify or another notification library
  // if (type === 'success') {
  //  alert(message);
  // } else {
  //  alert(`Error: ${message}`);
  // }
};

// Epic for handling link token fetch
const fetchLinkTokenEpic = (action$) =>
  action$.pipe(
    ofType(fetchLinkToken.pending.type),
  mergeMap(() => {
      const currentUserId = getCurrentUserId();
      
      console.log("fetchLinkTokenEpic - Using currentUserId from authUtil:", currentUserId);
      
      if (!currentUserId) {
        console.error("fetchLinkTokenEpic - No currentUserId available from authUtil!");
        return of(fetchLinkToken.rejected('No user ID available to generate link token'));
      }

      return from(
        axiosInstance.post('/pennypal/api/create_link_token', {
          userId: currentUserId
        }, { withCredentials: true })
      ).pipe(
        map((response) => fetchLinkToken.fulfilled(response.data)),
        catchError((error) => {
  console.error("Link token fetch error:", error.response || error);
          showNotification(error.response?.data?.message || 'Failed to generate link token', 'error');
          return of(fetchLinkToken.rejected(error.response?.data || 'Failed to generate link token'));
        })
      )
    })
  );

const fetchAccountDetailsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchAccountDetails.pending.type),
    mergeMap(() => {
      const currentUserId = getCurrentUserId();
      const cache = state$.value.cache;
      
      console.log("fetchAccountDetailsEpic - Using currentUserId from authUtil:", currentUserId);
      
      if (!currentUserId) {
        console.error("fetchAccountDetailsEpic - No currentUserId available from authUtil!");
        return of(fetchAccountDetails.rejected('No user ID available to fetch account details'));
      }

      // Check if data is available in cache first
      if (cache?.userAccountsLoaded && cache?.userAccounts?.length >= 0 &&
          cache?.userAccountsParams?.userId == currentUserId) {
        console.log('✅ Using cached user accounts data in accountsDashboardEpic');
        return of(fetchAccountDetails.fulfilled(cache.userAccounts));
      }

      // If not cached, dispatch cache action instead of direct API call
      console.log('🔄 User accounts data not cached, dispatching cache action');
      return from([
        { type: 'cache/fetchUserAccountsStart', payload: { userId: currentUserId } },
        // Return empty for now, the cache epic will handle the actual fetch
        fetchAccountDetails.fulfilled([])
      ]);
    })
  );
const fetchAccountBalanceMonthlyDeltasGroupedEpic = (action$) =>
  action$.pipe(
    ofType(fetchAccountBalanceMonthlyDeltasGrouped.pending.type),
    mergeMap((action) => {
      const { userId, year, month } = action.meta.arg;
      let finalUserId = userId || getCurrentUserId();

      if (!finalUserId) {
        return of(
          fetchAccountBalanceMonthlyDeltasGrouped.rejected(
            'No user ID available to fetch grouped balance deltas'
          )
        );
      }

      // Updated URL to match backend endpoint pattern
      const apiUrl = `/pennypal/api/v1/account/balances/delta/monthly/accountid/${finalUserId}/${year}/${month}`;
      
      console.log("Fetching grouped deltas from:", apiUrl);

      return from(axiosInstance.get(apiUrl)).pipe(
        map((response) => {
          console.log("Grouped balance deltas API response:", response.data);
          return fetchAccountBalanceMonthlyDeltasGrouped.fulfilled(response.data);
        }),
        catchError((error) => {
          console.error("Grouped balance deltas fetch error:", error.response || error);
          const errorMessage = error.response?.data?.message || error.response?.data || 'Failed to fetch grouped balance deltas';
          return of(fetchAccountBalanceMonthlyDeltasGrouped.rejected(errorMessage));
        })
      );
    })
  );
// Epic for handling public token exchange - Always using auth state userId
const exchangePublicTokenEpic = (action$, state$) =>
  action$.pipe(
    ofType(exchangePublicToken.pending.type),
    mergeMap((action) => {
      // Get the current user ID from the auth state
      //const userId = state$.value.auth?.user?.id;
     const currentUserId = getCurrentUserId();
      
       console.log("exchangePublicTokenEpic - Using currentUserId from authUtil:", currentUserId);
      
      if (!currentUserId) {
        console.error("exchangePublicTokenEpic - No currentUserId available from authUtil!");
        return of(exchangePublicToken.rejected('No user ID available to exchange public token'));
      }
      
      return from(axiosInstance.post('/pennypal/api/exchange_public_token', { 
        publicToken: action.meta.arg,
        userId: currentUserId
      })).pipe(
        mergeMap((response) => {
          // After successful token exchange, fetch account details
          console.log("Token exchange successful, fetching accounts");
          return [
            exchangePublicToken.fulfilled(response.data),
            fetchAccountDetails.pending()
          ];
        }),
        catchError((error) => {
          showNotification(error.response?.data?.message || 'Failed to exchange public token', 'error');
          return of(exchangePublicToken.rejected(error.response?.data || 'Failed to exchange public token'));
        })
      );
    })
  );



// Epic for handling sync account
const syncAccountEpic = (action$) =>
  action$.pipe(
    ofType(syncAccount.pending.type),
    mergeMap((action) => {
      const accountId = action.meta.arg;
      const currentUserId = getCurrentUserId();
      
      console.log("syncAccountEpic - Account ID:", accountId);
      console.log("syncAccountEpic - Using currentUserId from authUtil:", currentUserId);
      
      if (!accountId) {
        return of(syncAccount.rejected({
          accountId,
          error: 'No account ID provided for syncing'
        }));
      }
      
      if (!currentUserId) {
        console.error("syncAccountEpic - No currentUserId available from authUtil!");
        return of(syncAccount.rejected({
          accountId,
          error: 'No user ID available to sync account'
        }));
      }
      
      return from(axiosInstance.post(`/pennypal/api/accounts/${accountId}/synctransactions`, {
        userId: currentUserId
      })).pipe(
        mergeMap((response) => {
          let message = "Account synchronized successfully!";
          
          if (response.data.success) {
            // Check for transaction counts in order of preference
            const transactionsProcessed = 
              response.data.addedTransactions || 
              response.data.newTransactions || 
              response.data.data || 
              0;
            
            if (transactionsProcessed > 0) {
              message = `Synced ${transactionsProcessed} new transactions!`;
            }
            
            showNotification(message);
            
            // After successful sync, fetch updated account details
            return [
              syncAccount.fulfilled({
                accountId,
                data: response.data
              }),
              fetchAccountDetails.pending()
            ];
          }
          
          return of(syncAccount.fulfilled({
            accountId,
            data: response.data
          }));
        }),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || 'Failed to sync account';
          return of(syncAccount.rejected({
            accountId,
            error: errorMessage,
            hasError: true // Flag to show update button
          }));
        })
      );
    })
  );


// Updated to always use auth state userId
const refreshAllAccountsEpic = (action$, state$) =>
  action$.pipe(
    ofType(refreshAllAccounts.pending.type),
    mergeMap(() => {
      // Get currentUserId from authUtil
      const currentUserId = getCurrentUserId();
      
      console.log("refreshAllAccountsEpic - Using currentUserId from authUtil:", currentUserId);

      if (!currentUserId) {
        console.error("refreshAllAccountsEpic - No currentUserId available from authUtil!");
        const errorMessage = 'No user ID available to refresh accounts';
        return of(refreshAllAccounts.rejected(errorMessage));
      }

      return from(axiosInstance.post(`/pennypal/api/users/${currentUserId}/syncallaccounts`)).pipe(
        mergeMap((response) => {
          console.log("Refresh API response:", response.data);
          
          if (response.data.success) {
            showNotification('Account synchronization process started!');
            console.log("Refresh successful, fetching updated accounts");
            return [
              refreshAllAccounts.fulfilled(response.data),
              fetchAccountDetails.pending()
            ];
          } else {
            const errorMessage = 'Synchronization could not be started';
            showNotification(errorMessage, 'error');
            return of(refreshAllAccounts.rejected(errorMessage));
          }
        }),
        catchError((error) => {
          console.error("Refresh API error:", error);
          const message = error.response?.data?.error || error.response?.data?.message || 'Failed to start synchronization';
          showNotification(message, 'error');
          return of(refreshAllAccounts.rejected(message));
        })
      );
    })
  );

// Epic for handling MX connection
const connectMxEpic = (action$, state$) =>
  action$.pipe(
    ofType(connectMx.pending.type),
    mergeMap((action) => {
      // Get the current user ID from auth state
           //const userId = state$.value.auth?.user?.id;
           const userId = getCurrentUserId();
           console.log("connectMxEpic - Using userId:", userId);
      
            if (!userId) {
              console.error("connectMxEpic - No userId available in state!");
              return of(connectMx.rejected('No user ID available to connect MX'));
            }
          
      
      return from(axiosInstance.get('/pennypal/api/v1/mx/connect-url', { params: { userId } })).pipe(
        tap((response) => {
          const popup = window.open(response.data, "mx_connect", "width=600,height=600");
          const checkClosed = setInterval(() => {
            if (popup?.closed) {
              clearInterval(checkClosed);
              // Fetch accounts after connection
              axiosInstance.get('/pennypal/api/v1/mx/accounts', { params: { userId } })
                .then(() => axiosInstance.get('/pennypal/api/v1/mx/transactions', { params: { userId } })
                    .then(() => {
                      console.log('MX accounts fetched and transactions synced');
                    })
                    .catch(() => {
                      console.error('Failed to sync MX transactions');
                    })
                )
                .catch(() => {
                  console.error('Failed to fetch MX accounts');
                });
            }
          }, 500);
        }),
        map((response) => connectMx.fulfilled(response.data)),
        catchError((error) => {
          showNotification(error.response?.data?.message || 'Failed to initialize MX connection', 'error');
          return of(connectMx.rejected(error.response?.data || 'Failed to initialize MX connection'));
        })
      );
    })
  );

// Epic for handling Stripe connection
const connectStripeEpic = (action$, state$) =>
  action$.pipe(
    ofType(connectStripe.pending.type),
    mergeMap(() => {
      // Get the current user ID from auth state
           //const userId = state$.value.auth?.user?.id;
           const userId = getCurrentUserId();
      
      console.log("connectStripeEpic - Using userId:", userId);
      
      if (!userId) {
        console.error("connectStripeEpic - No userId available in state!");
        return of(connectStripe.rejected('No user ID available to connect Stripe'));
      }
      
      return from(axiosInstance.post('/pennypal/api/connect_stripe', { userId })).pipe(
        tap((response) => {
          window.open(response.data.connect_url, "stripe_connect", "width=600,height=600");
        }),
        map((response) => connectStripe.fulfilled(response.data)),
        catchError((error) => {
          showNotification(error.response?.data?.message || 'Failed to initialize Stripe connection', 'error');
          return of(connectStripe.rejected(error.response?.data || 'Failed to initialize Stripe connection'));
        })
      );
    })
  );

// Epic for handling Finicity connection
const connectFinicityEpic = (action$, state$) =>
  action$.pipe(
    ofType(connectFinicity.pending.type),
    mergeMap((action) => {
      const userId = action.meta.arg.userId;
      
      return from(axiosInstance.post('/pennypal/api/v1/finicity/connect-link', { userId })).pipe(
        tap((response) => {
          const popup = window.open(response.data, "finicity_connect", "width=600,height=600");
          const checkClosed = setInterval(() => {
            if (popup?.closed) {
              clearInterval(checkClosed);
              
              axiosInstance.post('/pennypal/api/v1/finicity/accounts', { userId })
                .then(() => axiosInstance.post(`/pennypal/api/v1/finicity/sync-transactions/${userId}`)
                  .then(() => {
                    console.log('Finicity accounts fetched and transactions synced');
                  })
                  .catch(() => {
                    console.error('Failed to sync Finicity transactions');
                  })
                )
                .catch(() => {
                  console.error('Failed to fetch Finicity accounts');
                });
            }
          }, 500);
        }),
        map((response) => connectFinicity.fulfilled(response.data)),
        catchError((error) => {
          showNotification(error.response?.data?.message || 'Failed to initialize Finicity connection', 'error');
          return of(connectFinicity.rejected(error.response?.data || 'Failed to initialize Finicity connection'));
        })
      )
    })
  );

    // Epic for handling Sankey chart data fetch
  const fetchSankeyChartDataEpic = (action$) =>
    action$.pipe(
      ofType(fetchSankeyChartData.type),
      mergeMap((action) => {
        const { userId, year, month, showSubcategories } = action.payload;
        const apiUrl = showSubcategories ? 
          `/pennypal/api/v1/budget/sankey-chart/with-subcategories?userId=${userId}&year=${year}&month=${month}` :
          `/pennypal/api/v1/budget/sankey-chart?userId=${userId}&year=${year}&month=${month}`;
      
        return from(axiosInstance.get(apiUrl)).pipe(
          map((response) => fetchSankeyChartDataSuccess(response.data)),
          catchError((error) => {
            const errorMessage = error.response?.data?.message || 'Failed to fetch sankey chart data';
            return of(fetchSankeyChartDataFailure(errorMessage));
          })
        );
      })
    );
  const fetchAccountsEpic = (action$, state$) =>
    action$.pipe(
      ofType(fetchAccountsRequest.type),
      mergeMap((action) => {
        const userId = action.payload.userId;
        const cache = state$.value.cache;

        // Check if data is available in cache first
        if (cache?.accountIdsLoaded && cache?.accountIds?.length >= 0 &&
            cache?.accountIdsParams?.userId == userId) {
          console.log('✅ Using cached account IDs data in accountsDashboardEpic');
          return of(fetchAccountsSuccess(cache.accountIds));
        }

        // If not cached, dispatch cache action instead of direct API call
        console.log('🔄 Account IDs data not cached, dispatching cache action');
        return from([
          { type: 'cache/fetchAccountIdsStart', payload: { userId } },
          // Return empty for now, the cache epic will handle the actual fetch
          fetchAccountsSuccess([])
        ]);
      })
    );
    const fetchAccountBalanceMonthlyDeltasEpic = (action$) =>
  action$.pipe(
    ofType(fetchAccountBalanceMonthlyDeltas.pending.type),
    mergeMap((action) => {
      const { userId, year, month } = action.meta.arg;
      let finalUserId = userId;
      
      if (!finalUserId) {
        finalUserId = getCurrentUserId();
        console.log("fetchAccountBalanceMonthlyDeltasEpic - Using currentUserId from authUtil:", finalUserId);
        
        if (!finalUserId) {
          console.error("fetchAccountBalanceMonthlyDeltasEpic - No userId available!");
          return of(fetchAccountBalanceMonthlyDeltas.rejected('No user ID available to fetch balance deltas'));
        }
      }
      alert(`🔍 Fetching grouped deltas for: ${finalUserId}, ${year}, ${month}`);

 console.log("Fetching grouped deltas from:", `/pennypal/api/v1/account/balances/delta/monthly/accountid/${finalUserId}/${year}/${month}`);
      return from(
        axiosInstance.get(`/pennypal/api/v1/account/balances/delta/monthly/${finalUserId}/${year}/${month}`)
      ).pipe(
        map((response) => {
          console.log("Balance deltas API response:", response.data);
          return fetchAccountBalanceMonthlyDeltas.fulfilled(response.data);
        }),
        catchError((error) => {
          console.error("Balance deltas fetch error:", error.response || error);
          const errorMessage = error.response?.data?.message || error.response?.data || 'Failed to fetch balance deltas';
          return of(fetchAccountBalanceMonthlyDeltas.rejected(errorMessage));
        })
      );
    })
  );

// Epic for fetching grouped account balance monthly deltas

// Epic to listen for cache updates and update account details state
const updateAccountDetailsFromCacheEpic = (action$, state$) =>
  action$.pipe(
    ofType('cache/fetchUserAccountsSuccess'),
    mergeMap((action) => {
      console.log('✅ User accounts cache updated, updating account details state');
      return of(fetchAccountDetails.fulfilled(action.payload));
    })
  );
const createReauthLinkTokenEpic = (action$) =>
  action$.pipe(
    ofType(createReauthLinkToken.pending.type),
    mergeMap((action) => {
      const accountId = action.meta.arg;
      
      console.log("Creating re-auth link token for account ID:", accountId);
      
      if (!accountId) {
        return of(createReauthLinkToken.rejected({
          accountId,
          error: 'No account ID provided for re-authentication'
        }));
      }
      
      return from(
        axiosInstance.post('/pennypal/api/plaid/reauth/create-link-token', {
          accountId: accountId
        })
      ).pipe(
        map((response) => {
          console.log("Re-auth link token created successfully:", response.data);
          return createReauthLinkToken.fulfilled({
            accountId,
            linkToken: response.data.link_token,
            data: response.data
          });
        }),
        catchError((error) => {
          console.error("Error creating re-auth link token:", error.response || error);
          const errorMessage = error.response?.data?.error || 'Failed to create re-authentication token';
          return of(createReauthLinkToken.rejected({
            accountId,
            error: errorMessage
          }));
        })
      );
    })
  );

// Epic for exchanging re-authentication public token
const exchangeReauthTokenEpic = (action$) =>
  action$.pipe(
    ofType(exchangeReauthToken.pending.type),
    mergeMap((action) => {
      const { publicToken, accountId } = action.meta.arg;
      
      console.log("Exchanging re-auth public token for account ID:", accountId);
      
      if (!publicToken || !accountId) {
        return of(exchangeReauthToken.rejected({
          accountId,
          error: 'Public token and account ID are required'
        }));
      }
      
      return from(
        axiosInstance.post('/pennypal/api/plaid/reauth/exchange-token', {
          public_token: publicToken,
          accountId: accountId
        })
      ).pipe(
        mergeMap((response) => {
          console.log("Re-auth token exchange successful:", response.data);
          
          // After successful re-auth, fetch updated account details
          return [
            exchangeReauthToken.fulfilled({
              accountId,
              data: response.data
            }),
            fetchAccountDetails.pending() // Refresh account data
          ];
        }),
        catchError((error) => {
          console.error("Error exchanging re-auth token:", error.response || error);
          const errorMessage = error.response?.data?.error || 'Failed to complete re-authentication';
          return of(exchangeReauthToken.rejected({
            accountId,
            error: errorMessage
          }));
        })
      );
    })
  );
// Combine all epics
export const accountsDashboardEpic = combineEpics(
  fetchLinkTokenEpic,
  fetchAccountDetailsEpic,
  exchangePublicTokenEpic,
  refreshAllAccountsEpic,
  syncAccountEpic,
  connectMxEpic,
  connectStripeEpic,
  connectFinicityEpic,
  fetchAccountsEpic,
  fetchSankeyChartDataEpic,
   fetchAccountBalanceMonthlyDeltasEpic,
  fetchAccountBalanceMonthlyDeltasGroupedEpic,
  updateAccountDetailsFromCacheEpic,exchangeReauthTokenEpic,createReauthLinkTokenEpic
);