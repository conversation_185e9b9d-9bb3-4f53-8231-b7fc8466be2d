import { ofType, combineEpics } from 'redux-observable';
import { of } from 'rxjs';
import { mergeMap } from 'rxjs/operators';
import { signOut } from '../redux/authSlice';
import { clearCache } from '../redux/cacheSlice';

// Epic to clear cache when user signs out
export const signOutCacheCleanupEpic = (action$) =>
  action$.pipe(
    ofType(signOut.type),
    mergeMap((action) => {
      console.log('🗑️ User signed out, clearing all cache data');
      
      // Clear all cache when user logs out
      return of(clearCache());
    })
  );

// Combined auth epic
export const authEpic = combineEpics(
  signOutCacheCleanupEpic
);

export default authEpic;