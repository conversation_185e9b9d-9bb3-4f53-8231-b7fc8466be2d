import { ofType } from 'redux-observable';
import { of } from 'rxjs';
import { mergeMap, map, withLatestFrom } from 'rxjs/operators';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';
import {
  fetchBudgetDashboardData,
  fetchBudgetDashboardDataSuccess,
  fetchBudgetDashboardDataFailure
} from '../redux/budgetDashboardSlice';
import {
  fetchBudgetSummaryStart,
  fetchBudgetSummarySuccess
} from '../redux/cacheSlice';

// Updated action creator - only needs year and month
export const fetchBudgetDashboard = (year, month) => ({
  type: fetchBudgetDashboardData.type,
  payload: { year, month }
});

export const getBudgetDashboardEpic = (action$, state$) => {
  return action$.pipe(
    ofType(fetchBudgetDashboardData.type),
    withLatestFrom(state$),
    mergeMap(([action, state]) => {
      const userId = getCurrentUserId(); // Get userId from token
      const { year, month } = action.payload;
      const cache = state?.cache;
      
      // Check if user is authenticated
      if (!userId) {
        return of(fetchBudgetDashboardDataFailure({
          error: 'User not authenticated'
        }));
      }
      
      // Check if we can use cached data
      if (cache?.budgetSummaryLoaded && cache?.budgetSummary &&
          cache?.budgetSummaryParams?.userId === userId &&
          cache?.budgetSummaryParams?.year === year &&
          cache?.budgetSummaryParams?.month === month) {
        return of(fetchBudgetDashboardDataSuccess({ data: cache.budgetSummary }));
      }

      // Dispatch cache action to fetch and cache the data
      return of(fetchBudgetSummaryStart({ userId, year, month }));
    })
  );
};

// Epic to listen for cache success and update dashboard state
export const budgetSummaryToDashboardEpic = (action$) =>
  action$.pipe(
    ofType(fetchBudgetSummarySuccess.type),
    map((action) => {
      return fetchBudgetDashboardDataSuccess({ data: action.payload });
    })
  );

export default [getBudgetDashboardEpic, budgetSummaryToDashboardEpic];