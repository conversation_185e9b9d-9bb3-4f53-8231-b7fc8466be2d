import { ofType, combineEpics } from 'redux-observable';
import { of, from } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  queryRequest,
  querySuccess,
  queryFailure,
} from '../redux/chatbotSlice';
import { invalidateChatbotCache } from '../redux/cacheSlice';



// Epic to handle chatbot queries
export const chatbotQueryEpic = (action$) =>
  action$.pipe(
    ofType(queryRequest.type),
    switchMap((action) => {
      console.log('🚀 Starting chatbot query...');
      const { userId, userQuery } = action.payload || {};

      if (!userId || !userQuery) {
        console.error('❌ User ID or query not found for chatbot query API');
        return of(queryFailure('User ID or query not found'));
      }

      return from(axiosInstance.post('/pennypal/api/v1/chatbot/query', {
        user_id: userId,
        user_query: userQuery,
      })).pipe(
        switchMap((response) => {
          console.log('✅ Chatbot query completed successfully');
          // Invalidate cache - this will automatically trigger refetch via cacheInvalidationEpic
          return of(
            querySuccess(response.data),
            invalidateChatbotCache()
          );
        }),
        catchError((error) => {
          console.error('❌ Failed to process chatbot query:', error);
          const errorMessage = error.response?.data?.message || error.message || 'Failed to process chatbot query';
          return of(queryFailure(errorMessage));
        })
      );
    })
  );

// Combined chatbot epic
export const chatbotEpic = combineEpics(
  chatbotQueryEpic
);

export default chatbotEpic;