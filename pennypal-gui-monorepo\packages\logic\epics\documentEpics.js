import { ofType } from 'redux-observable';
import { startWith } from 'rxjs/operators';
import { of, from } from 'rxjs';
import { switchMap, catchError, map, tap, mergeMap } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchDocumentsRequest,
  fetchDocumentsSuccess,
  fetchDocumentsFailure,
  uploadDocumentRequest,
  uploadDocumentSuccess,
  uploadDocumentFailure,
  deleteDocumentRequest,
  deleteDocumentSuccess,
  deleteDocumentFailure,
  generateQrDataRequest,
  generateQrDataSuccess,
  generateQrDataFailure,
  setIsProcessing,
  setPreviewData,
  setPreviewStep,
  fetchDocumentDetailsRequest,
  fetchDocumentDetailsSuccess,
  fetchDocumentDetailsFailure,
  addTransactionRequest,
  addTransactionSuccess,
  addTransactionFailure,
} from '../redux/documentSlice';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';


// Fetch documents epic
export const fetchDocumentsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchDocumentsRequest.type),
    switchMap(() => {
      try {
        const userId = getCurrentUserId();
        const cache = state$.value?.cache;

        // Check if user receipts are already cached
        if (cache?.userReceiptsLoaded && cache?.userReceipts?.length >= 0 && cache?.userReceiptsParams?.userId === userId) {
          console.log('✅ Using cached user receipts in document epic');
          return of(fetchDocumentsSuccess(cache.userReceipts));
        }
        console.log('🔄 User receipts not cached, fetching documents');
        return from(axiosInstance.get(`/pennypal/api/receipts/user/${userId}`)).pipe(
          map((response) => fetchDocumentsSuccess(response.data)),
          catchError((error) =>
            of(fetchDocumentsFailure(error.response?.data || 'Failed to fetch documents'))
          )
        );
      } catch (error) {
        console.error('Error checking cache state in document epic:', error);
        // Make API call on error
        const userId = getCurrentUserId();
        return from(axiosInstance.get(`/pennypal/api/receipts/user/${userId}`)).pipe(
          map((response) => fetchDocumentsSuccess(response.data)),
          catchError((error) =>
            of(fetchDocumentsFailure(error.response?.data || 'Failed to fetch documents'))
          )
        );
      }
    })
  );

// Upload document epic
export const uploadDocumentEpic = (action$) =>
  action$.pipe(
    ofType(uploadDocumentRequest.type),
    switchMap((action) => {
      const formData = action.payload;
      const docType = formData.get('docType');
      
      return from(axiosInstance.post('/pennypal/api/receipts/uploadReceipt', formData, {
        headers: {'Content-Type': 'multipart/form-data'},
        timeout: 90000
      })).pipe(
        mergeMap(response => {
          console.log("API Response:", response.data);
          
          const file = formData.get('file');
          const previewData = {
            originalFile: URL.createObjectURL(file),
            azureResponse: {
              ...response.data,
              fullAnalysis: response.data.fullAnalysis,
              prettyJson: response.data.prettyJson
            },
            docName: formData.get('docName'),
            docType: docType,
            size: formData.get('size'),
            category: formData.get('category'),
            qrData: formData.get('qrData') || ''
          };

          return of(
            setPreviewData(previewData),
            setPreviewStep('preview'),
            setIsProcessing(false)
          );
        }),
        startWith(setIsProcessing(true)),
        catchError(error => {
          console.error("API Error:", error);
          return of(
            uploadDocumentFailure(error.message),
            setIsProcessing(false)
          );
        })
      );
    })
  );

  export const confirmUploadEpic = (action$) =>
    action$.pipe(
      ofType('documents/confirmUpload'),
      switchMap((action) => {
        if (!action.payload?.previewData) {
          return of(
            uploadDocumentFailure('Invalid document data'),
            setIsProcessing(false)
          );
        }
  
        const { previewData } = action.payload;
        const azureResponse = previewData.azureResponse || {};
        const userId = getCurrentUserId();

        // Prepare the data structure that matches your backend expectations
        const savePayload = {
          userId,
          docType: previewData.docType,
          docName: previewData.docName,
          category: previewData.category,
          qrData: previewData.qrData,
          size: previewData.size,
          savedFilePath: azureResponse.savedFilePath,
          rawJson: azureResponse.rawJson,

          // Azure extracted fields - these should be at the root level
          MerchantName: azureResponse.MerchantName,
          MerchantAddress: azureResponse.MerchantAddress,
          MerchantPhoneNumber: azureResponse.MerchantPhoneNumber,
          TransactionDate: azureResponse.TransactionDate,
          TransactionTime: azureResponse.TransactionTime,
          Subtotal: azureResponse.Subtotal,
          Tax: azureResponse.Tax,
          Total: azureResponse.Total,
          Items: azureResponse.Items
        };
  
        return from(axiosInstance.post('/pennypal/api/receipts/saveReceipt', savePayload, {
          timeout: 60000
        })).pipe(
          map(saveResponse => {
            // Parse amounts correctly
            const parseAmount = (value) => value ? parseFloat(String(value).replace(/[^0-9.-]/g, '')) : 0.00;
            console.log('uploadDocumentSuccess payload:', {
              amount: parseAmount(saveResponse.data?.transTotal ?? azureResponse.Total)
            });
            
            return uploadDocumentSuccess({
              id: saveResponse.data?.id,
              docName: previewData.docName,
              type: previewData.docType,
              date: new Date().toISOString(),
              category: previewData.category,
              size: previewData.size,
              filePath: azureResponse.savedFilePath,
              scannedFilePath: azureResponse.scannedCopyPath, // scanned
              qrData: previewData.qrData,
               amount: parseAmount(saveResponse.data?.transTotal ?? azureResponse.Total),
              merchantName: azureResponse.MerchantName || 'Unknown',
              merchantAddress: azureResponse.MerchantAddress || 'Unknown',
              merchantPhone: azureResponse.MerchantPhoneNumber || 'Unknown',
              subtotal: parseAmount(azureResponse.Subtotal),
              tax: parseAmount(azureResponse.Tax),
              transactionDate: azureResponse.TransactionDate,
              transactionTime: azureResponse.TransactionTime,
              
            });
          }),
          startWith(setIsProcessing(true)),
          catchError(error => {
            console.error('Save error:', error.response?.data || error.message);
            return of(
              uploadDocumentFailure(error.response?.data?.message || 'Save request failed'),
              setIsProcessing(false),
              setPreviewStep('upload')
            );
          })
        );
      })
    );
   
   export const fetchDocumentDetailsEpic = (action$) =>
  action$.pipe(
    ofType(fetchDocumentDetailsRequest.type),
    switchMap((action) => {
      const { documentId } = action.payload;
      
      return from(axiosInstance.get(`/pennypal/api/receipts/getDocumentDetails/${documentId}`)).pipe(
        map((response) => {
          const documentData = response.data;
          
          // Common fields
          const baseDocument = {
            id: documentData.id,
            type: documentData.docType,
            name: documentData.docName || (documentData.docType === 'receipt' ? 'Receipt' : 'ID Document'),
            category: documentData.category,
            uploadDate: documentData.uploadDate,
            savedFilePath: documentData.savedFilePath,
            scannedCopyPath: documentData.scannedCopyPath,
            date: documentData.transactionDate || documentData.uploadDate,
            amount: documentData.amount || 0 

          };

          if (documentData.docType === 'receipt') {
            return fetchDocumentDetailsSuccess({
              ...baseDocument,
              // Receipt specific fields
              merchantName: documentData.merchantName,
              merchantAddress: documentData.merchantAddress,
              merchantPhno: documentData.merchantPhno,
              transactionDate: documentData.transactionDate,
              transactionSubtotal: documentData.transactionSubtotal !== null && documentData.transactionSubtotal !== undefined
              ? parseFloat(documentData.transactionSubtotal)
              : null,
              transTax: documentData.transTax !== null && documentData.transTax !== undefined ? parseFloat(documentData.transTax) : null,
              transTotal: documentData.transTotal !== null && documentData.transTotal !== undefined ? parseFloat(documentData.transTotal) : null,
              transReturnby: documentData.transReturnby,
              items: Array.isArray(documentData.items) ? documentData.items : [],
              amount: documentData.amount || documentData.transTotal || 0 ,// Fallback to transTotal if amount not present
              filePath: documentData.savedFilePath,  // Make sure this matches
              scannedCopyPath: documentData.scannedCopyPath 
            });
          } else if (documentData.docType === 'id') {
            const extractedFields = documentData.extractedFields || {};
            console.log(documentData.extractedFields);

            return fetchDocumentDetailsSuccess({
              ...baseDocument,
              rawAnalysis: documentData.rawAnalysis || null,
              analysisError: documentData.analysisError || null,
              extractedFields,
              amount: 0
            });
          } else if (documentData.docType === 'invoice') {
            const extractedFields = documentData.extractedFields || {};
            console.log(documentData.extractedFields);
            return fetchDocumentDetailsSuccess({
              ...baseDocument,
              rawAnalysis: documentData.rawAnalysis || null,
              analysisError: documentData.analysisError || null,
              extractedFields,

              amount: 0
            });
          }else if (documentData.docType === 'tax') {
            const extractedFields = documentData.extractedFields || {};
            console.log(documentData.extractedFields);
            return fetchDocumentDetailsSuccess({
              ...baseDocument,
              rawAnalysis: documentData.rawAnalysis || null,
              analysisError: documentData.analysisError || null,
              extractedFields,

              amount: 0
            });
          }else if (documentData.docType === 'contract') {
            const extractedFields = documentData.extractedFields || {};
            console.log(documentData.extractedFields);
            return fetchDocumentDetailsSuccess({
              ...baseDocument,
              rawAnalysis: documentData.rawAnalysis || null,
              analysisError: documentData.analysisError || null,
              extractedFields,

              amount: 0
            });
          }
          
          return fetchDocumentDetailsSuccess({
            ...baseDocument,
            amount: 0
          });
        }),
        catchError((error) => of(fetchDocumentDetailsFailure(error.response?.data || 'Failed to fetch document details')))
      );
    })
  );
 
    
// Delete document epic
export const deleteDocumentEpic = (action$) =>
  action$.pipe(
    ofType(deleteDocumentRequest.type),
    switchMap((action) =>
      from(axiosInstance.delete(`/pennypal/api/receipts/delete/${action.payload}`)).pipe(
        // Add logging for debugging
        tap(response => console.log('Delete response:', response)),
        map(() => deleteDocumentSuccess(action.payload)),
        catchError((error) => {
          console.error('Delete error:', error);
          return of(deleteDocumentFailure(error.response?.data || 'Failed to delete document'))
        })
      )
    )
  );

// Generate QR data epic
export const generateQrDataEpic = (action$) =>
  action$.pipe(
    ofType(generateQrDataRequest.type),
    switchMap(() => {
      // Simulate API call or complex generation
      return new Promise((resolve) => {
        setTimeout(() => {
          const randomId = `ID-${Math.floor(100000 + Math.random() * 900000)}`;
          resolve(generateQrDataSuccess(randomId));
        }, 500);
      });
    }),
    catchError((error) => of(generateQrDataFailure('Failed to generate QR code')))
  );

  