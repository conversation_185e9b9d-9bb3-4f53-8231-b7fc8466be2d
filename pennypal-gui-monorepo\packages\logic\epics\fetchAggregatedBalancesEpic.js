import { ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { mergeMap, map, catchError, filter, withLatestFrom, timeout } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchAggregatedBalancesStart,
  fetchAggregatedBalancesSuccess,
  fetchAggregatedBalancesFailure,
  selectShouldFetch,
} from '../redux/accountBalanceSlice';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';

/* ─────────────────────────────────────────
   FETCH AGGREGATED BALANCES BY ACCOUNT ID (WITH CACHE CHECK AND TIMEOUT FIX)
───────────────────────────────────────── */
export const fetchAggregatedBalancesEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchAggregatedBalancesStart.type),
    withLatestFrom(state$),
    filter(([action, state]) => {
      const { userId, duration, interval } = action.payload;
      // Only proceed if we should actually fetch (based on cache state)
      const shouldFetch = selectShouldFetch(userId, duration, interval)(state);
      console.log('fetchAggregatedBalancesEpic: Should fetch?', shouldFetch, 'for:', `${userId}_${duration}_${interval}`);
      return shouldFetch;
    }),
    mergeMap(([action, state]) => {
      const { userId, duration, interval } = action.payload;
      
      if (!userId) {
        console.error('fetchAggregatedBalancesEpic: User ID not found');
        return of(fetchAggregatedBalancesFailure('User ID not found'));
      }

      console.log('fetchAggregatedBalancesEpic: Fetching aggregated balances for:', {
        userId,
        duration,
        interval
      });

      // Create axios request with extended timeout
      const apiCall = axiosInstance.get(
        `/pennypal/api/v1/account/balances/agg/accountid/${userId}/${duration}/${interval}`,
        {
          timeout: 60000, // 60 seconds timeout
          headers: {
            'Content-Type': 'application/json',
            // Add any other required headers
          }
        }
      );

      return from(apiCall).pipe(
        // Add RxJS timeout as backup (65 seconds to allow axios timeout to fire first)
        timeout(65000),
        map((response) => {
          console.log('fetchAggregatedBalancesEpic: Fetch response:', response.data);
          
          // Process the data to handle cases where no data is coming (add 0 logic)
          let processedData = response.data || [];
          
          // If no data is returned, create dummy data points with 0 balance
          if (processedData.length === 0) {
            const today = new Date();
            const dummyData = [];
            
            // Create data points based on duration and interval
            const numPoints = Math.min(Math.ceil((duration * 30) / interval), 10); // Cap at 10 points
            
            for (let i = numPoints - 1; i >= 0; i--) {
              const date = new Date(today);
              date.setDate(today.getDate() - (i * interval));
              
              dummyData.push({
                userId: parseInt(userId),
                accountId: null,
                groupEndDate: date.toISOString().split('T')[0],
                aggregatedBalance: 0,
                accountName: 'No Data',
                accountCategory: null,
                accountType: null,
                accountSubtype: null,
                accountMask: null,
                institutionId: null
              });
            }
            processedData = dummyData;
            console.log('fetchAggregatedBalancesEpic: Created dummy data:', dummyData);
          }
          
          return fetchAggregatedBalancesSuccess({
            userId: parseInt(userId),
            duration: parseInt(duration),
            interval: parseInt(interval),
            data: processedData
          });
        }),
        catchError((error) => {
          console.error('fetchAggregatedBalancesEpic: Fetch error:', error);
          
          let errorMessage = 'Failed to fetch aggregated balances';
          
          if (error.code === 'ECONNABORTED' || error.name === 'TimeoutError') {
            errorMessage = 'Request timed out. Please try again.';
          } else if (error.response) {
            // Server responded with error status
            errorMessage = error.response?.data?.message ||
                          error.response?.data ||
                          `Server error: ${error.response.status}`;
          } else if (error.request) {
            // Request was made but no response received
            errorMessage = 'Network error. Please check your connection.';
          } else {
            // Something else happened
            errorMessage = error.message || 'An unexpected error occurred';
          }
          
          return of(fetchAggregatedBalancesFailure(errorMessage));
        })
      );
    })
  );

/* ─────────────────────────────────────────
   EXPORT EPICS
───────────────────────────────────────── */
export default [fetchAggregatedBalancesEpic];