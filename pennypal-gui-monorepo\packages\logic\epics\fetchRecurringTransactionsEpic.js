import { ofType } from 'redux-observable';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { from, of } from 'rxjs';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchRecurringTransactionsStart,
  fetchRecurringTransactionsSuccess,
  fetchRecurringTransactionsFailed
} from '../redux/recurringTransactionsSlice';
import {getCurrentUserId } from '../../web/src/utils/AuthUtil'

export const fetchRecurringTransactionsEpic = (action$, state$) => action$.pipe(
  ofType(fetchRecurringTransactionsStart.type),
  mergeMap(() => {
    const cache = state$.value?.cache;

    // Check if we can use cached data
    if (cache?.recurringTransactionsLoaded && cache?.futureRecurringTransactionsLoaded &&
        cache?.recurringTransactions?.length > 0 && cache?.futureRecurringTransactions?.length > 0) {
      console.log('✅ Using cached recurring transactions data');

      // Transform cached data to match expected format
      const current = cache.recurringTransactions.map(item => ({
        id: item.transactionId,
        merchant: item.description,
        amount: item.transactionAmount,
        category: item.category,
        frequency: item.recurringFrequency,
        date: item.transactionDate,
        account: item.accountMask
      }));

      const future = cache.futureRecurringTransactions.map(item => ({
        id: 'Future',
        merchant: item.description,
        amount: item.transactionAmount,
        category: item.category,
        frequency: item.recurringFrequency,
        date: item.nextDate,
        account: item.accountMask
      }));

      const combinedData = [...current, ...future];
      return of(fetchRecurringTransactionsSuccess(combinedData));
    }
    
    const userId = getCurrentUserId(); // Get userId from token
    
    // Check if user is authenticated
    if (!userId) {
      console.error('User not authenticated for recurring transactions');
      return of(fetchRecurringTransactionsFailed('User not authenticated'));
    }

    // Fetch historical transactions with dynamic userId
    const fetchCurrent = from(axiosInstance.get(`/pennypal/api/v1/transaction/recurring/fetch2/${userId}`))
      .pipe(
        map(response => {
          // Transform response data
          return response.data.map(item => ({
            id: item.transactionId,
            merchant: item.description,
            amount: item.transactionAmount,
            category: item.category,
            frequency: item.recurringFrequency,
            date: item.transactionDate,
            account: item.accountMask
          }));
        })
      );

    // Fetch future transactions with dynamic userId
    const fetchFuture = from(axiosInstance.get(`/pennypal/api/v1/transaction/recurring/fetch_future/${userId}`))
      .pipe(
        map(response => {
          // Transform response data
          return response.data.map(item => ({
            id: 'Future',
            merchant: item.description,
            amount: item.transactionAmount,
            category: item.category,
            frequency: item.recurringFrequency,
            date: item.nextDate,
            account: item.accountMask
          }));
        })
      );

    // Combine both responses
    return from(Promise.all([fetchCurrent.toPromise(), fetchFuture.toPromise()]))
      .pipe(
        map(([current, future]) => {
          const combinedData = [...current, ...future];
          console.log(`Fetched ${current.length} current and ${future.length} future recurring transactions for user ${userId}`);
          return fetchRecurringTransactionsSuccess(combinedData);
        }),
        catchError(error => {
          console.error('Error fetching recurring transactions for user', userId, ':', error);
          return of(fetchRecurringTransactionsFailed('Failed to load recurring transactions'));
        })
      );
  })
);

// Root epic
export const recurringTransactionsEpics = [
  fetchRecurringTransactionsEpic
];