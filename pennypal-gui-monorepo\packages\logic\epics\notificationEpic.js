import { combineEpics, ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { catchError, mergeMap, map } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchNotificationsSuccess as cacheFetchNotificationsSuccess
} from '../redux/cacheSlice';

import {
  fetchNotifications,
  fetchNotificationsSuccess,
  fetchNotificationsFailure,
  fetchNotificationRules,
  fetchNotificationRulesSuccess,
  fetchNotificationRulesFailure,
  markNotificationAsRead,
  markNotificationAsReadSuccess,
  markNotificationAsReadFailure,
  clearAllNotifications,
  clearAllNotificationsSuccess,
  clearAllNotificationsFailure,
  saveNotificationRule,
  saveNotificationRuleSuccess,
  saveNotificationRuleFailure,
  deleteNotification,
  deleteNotificationSuccess,
  deleteNotificationFailure
} from '../redux/notificationSlice';

// Epic for fetching notifications with cache checking
export const fetchNotificationsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchNotifications.type),
    mergeMap((action) => {
      const { userId } = action.payload;

      // Check cache first
      const state = state$.value;
      const cache = state?.cache;

      console.log('🔍 NotificationEpic: Checking notification cache state:', {
        notificationsLoaded: cache?.notificationsLoaded,
        cachedUserId: cache?.notificationsParams?.userId,
        requestUserId: userId,
        cacheMatch: cache?.notificationsParams?.userId === userId
      });

      if (cache?.notificationsLoaded &&
          cache?.notificationsParams?.userId === userId) {
        console.log('✅ NotificationEpic: Notifications already cached, returning cached data');
        return of(fetchNotificationsSuccess(cache.notifications));
      }

      console.log('🚀 NotificationEpic: Fetching notifications from API for user:', userId);
      return from(axiosInstance.get(`pennypal/api/v1/notifications/fetch/${userId}`)).pipe(
        mergeMap((response) => {
          console.log('✅ NotificationEpic: Notifications fetched successfully:', response.data?.length || 0, 'items');
          console.log('✅ NotificationEpic: Dispatching cache success with userId:', userId);
          // Dispatch both notification slice and cache slice actions
          // Include userId in cache action payload so params are set correctly
          return from([
            fetchNotificationsSuccess(response.data),
            cacheFetchNotificationsSuccess({ data: response.data, userId })
          ]);
        }),
        catchError((error) => {
          console.error('❌ NotificationEpic: Failed to fetch notifications:', error);
          return of(fetchNotificationsFailure(error.message));
        })
      );
    })
  );

// Epic for fetching notification rules
export const fetchNotificationRulesEpic = (action$) =>
  action$.pipe(
    ofType(fetchNotificationRules.type),
    mergeMap((action) =>
      from(axiosInstance.get(`pennypal/api/v1/user-notification-rules/user/${action.payload.userId}`)).pipe(
        map((response) => fetchNotificationRulesSuccess(response.data)),
        catchError((error) =>
          of(fetchNotificationRulesFailure(error.message))
        )
      )
    )
  );

// Epic for marking a notification as read
export const markNotificationAsReadEpic = (action$) =>
  action$.pipe(
    ofType(markNotificationAsRead.type),
    mergeMap((action) =>
      from(
        axiosInstance.put(`pennypal/api/v1/notifications/mark-read/${action.payload.notificationId}`)
      ).pipe(
        map(() => markNotificationAsReadSuccess({ notificationId: action.payload.notificationId })),
        catchError((error) =>
          of(markNotificationAsReadFailure(error.message))
        )
      )
    )
  );

// Epic for clearing all notifications
export const clearAllNotificationsEpic = (action$) =>
  action$.pipe(
    ofType(clearAllNotifications.type),
    mergeMap((action) =>
      from(
        axiosInstance.delete(`pennypal/api/v1/notifications/delete/${action.payload.userId}`)
      ).pipe(
        map(() => clearAllNotificationsSuccess()),
        catchError((error) =>
          of(clearAllNotificationsFailure(error.message))
        )
      )
    )
  );

// Epic for saving a notification rule
export const saveNotificationRuleEpic = (action$) =>
  action$.pipe(
    ofType(saveNotificationRule.type),
    mergeMap((action) => {
      // Changes to handle multiple rule saves at once
      const rules = Array.isArray(action.payload) ? action.payload : [action.payload];

      return from(
        Promise.all(
          rules.map((rule) =>
            axiosInstance.put('pennypal/api/v1/user-notification-rules/edit', rule).then((response) => ({
              ruleId: rule.ruleId,
              data: response.data
            }))
          )
        )
      ).pipe(
        map((responses) =>
          saveNotificationRuleSuccess(
            responses.map((response) => response.data)
          )
        ),
        catchError((error) => {
          return of(saveNotificationRuleFailure(error.message));
        })
      );
    })
  );

// Epic for deleting a notification
export const deleteNotificationEpic = (action$) =>
  action$.pipe(
    ofType(deleteNotification.type),
    mergeMap((action) =>
      from(
        axiosInstance.delete(`pennypal/api/v1/notifications/delete_one/${action.payload.notificationId}`)
      ).pipe(
        map(() => deleteNotificationSuccess({ notificationId: action.payload.notificationId })),
        catchError((error) =>
          of(deleteNotificationFailure(error.message))
        )
      )
    )
  );

export const rootNotificationsEpic = combineEpics(
  fetchNotificationsEpic,
  fetchNotificationRulesEpic,
  markNotificationAsReadEpic,
  clearAllNotificationsEpic,
  saveNotificationRuleEpic,
  deleteNotificationEpic
);