import { ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { mergeMap, map, catchError, filter, withLatestFrom } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchUserStart,
  fetchUserSuccess,
  fetchUserFailure,
  updateUserStart,
  updateUserSuccess,
  updateUserFailure,
  selectShouldFetch,
} from '../redux/profileSlice';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';

/* ─────────────────────────────────────────
   FETCH CURRENT USER (WITH CACHE CHECK)
───────────────────────────────────────── */
export const fetchUserEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchUserStart.type),
    withLatestFrom(state$),
    filter(([action, state]) => {
      // Only proceed if we should actually fetch (based on cache state)
      const shouldFetch = selectShouldFetch(state);
      console.log('fetchUserEpic: Should fetch?', shouldFetch);
      return shouldFetch;
    }),
    mergeMap(([action, state]) => {
      const userId = getCurrentUserId();
      if (!userId) {
        console.error('fetchUserEpic: User ID not found');
        return of(fetchUserFailure('User ID not found'));
      }

      console.log('fetchUserEpic: Fetching user data for userId:', userId);
      
      // GET /api/v1/user/profile/{userId}
      return from(
        axiosInstance.get(`/pennypal/api/v1/user/profile/${userId}`)
      ).pipe(
        map((response) => {
          console.log('fetchUserEpic: Fetch user response:', response.data);
          return fetchUserSuccess(response.data);
        }),
        catchError((error) => {
          console.error('fetchUserEpic: Fetch user error:', error);
          const errorMessage = error.response?.data?.message ||
                               error.response?.data ||
                               error.message ||
                               'Failed to fetch user';
          return of(fetchUserFailure(errorMessage));
        })
      );
    })
  );

/* ─────────────────────────────────────────
   UPDATE CURRENT USER
───────────────────────────────────────── */
export const updateUserEpic = (action$) =>
  action$.pipe(
    ofType(updateUserStart.type),
    mergeMap(({ payload: userDto }) => {
      const userId = getCurrentUserId();
      if (!userId) {
        console.error('updateUserEpic: User ID not found');
        return of(updateUserFailure('User ID not found'));
      }

      // Ensure the userDto has the correct structure
      const updatePayload = {
        ...userDto,
        id: userId // This will be overridden by the controller anyway
      };

      console.log('updateUserEpic: Updating user with payload:', updatePayload);

      // PUT /api/v1/user/profile/{id}
      return from(
        axiosInstance.put(`/pennypal/api/v1/user/profile/${userId}`, updatePayload)
      ).pipe(
        map((response) => {
          console.log('updateUserEpic: Update user response:', response.data);
          return updateUserSuccess(response.data);
        }),
        catchError((error) => {
          console.error('updateUserEpic: Update user error:', error);
          const errorMessage = error.response?.data?.message ||
                               error.response?.data ||
                               error.message ||
                               'Failed to update user';
          return of(updateUserFailure(errorMessage));
        })
      );
    })
  );

/* ─────────────────────────────────────────
   EXPORT EPICS
───────────────────────────────────────── */
export default [fetchUserEpic, updateUserEpic];