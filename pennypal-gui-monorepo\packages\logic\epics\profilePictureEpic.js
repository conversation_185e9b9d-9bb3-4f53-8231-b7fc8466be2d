import { ofType } from 'redux-observable';
import { map, catchError, switchMap } from 'rxjs/operators';
import { of, from } from 'rxjs';
import {
  uploadProfilePictureStart,
  uploadProfilePictureSuccess,
  uploadProfilePictureFailure,
  fetchProfilePictureStart,
  fetchProfilePictureSuccess,
  fetchProfilePictureFailure,
  deleteProfilePictureStart,
  deleteProfilePictureSuccess,
  deleteProfilePictureFailure,
} from '../redux/profilePictureSlice';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';
import { axiosInstance } from '../api/axiosConfig'; // Adjust path if needed

// Upload profile picture epic (multipart form-data)
export const uploadProfilePictureEpic = (action$) =>
  action$.pipe(
    ofType(uploadProfilePictureStart.type),
    switchMap((action) => {
      const userId = getCurrentUserId();
      const { file } = action.payload;

      if (!userId || userId === '0') {
        return of(uploadProfilePictureFailure('User not authenticated'));
      }

      const formData = new FormData();
      formData.append('file', file);

      return from(
        axiosInstance.post(`/api/users/${userId}/profile-picture/upload`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        })
      ).pipe(
        map((response) => uploadProfilePictureSuccess(response.data.data)),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || 'Failed to upload profile picture';
          return of(uploadProfilePictureFailure(errorMessage));
        })
      );
    })
  );

// Upload profile picture from base64 data
export const uploadProfilePictureFromDataEpic = (action$) =>
  action$.pipe(
    ofType('UPLOAD_PROFILE_PICTURE_FROM_DATA_START'),
    switchMap((action) => {
      const userId = getCurrentUserId();
      const { base64Data, fileName, contentType } = action.payload;

      if (!userId || userId === '0') {
        return of(uploadProfilePictureFailure('User not authenticated'));
      }

      const requestBody = {
        base64Data,
        fileName,
        contentType,
      };

      return from(
        axiosInstance.post(`/api/users/${userId}/profile-picture/upload-data`, requestBody)
      ).pipe(
        map((response) => uploadProfilePictureSuccess(response.data.data)),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || 'Failed to upload profile picture';
          return of(uploadProfilePictureFailure(errorMessage));
        })
      );
    })
  );

// Fetch profile picture info
export const fetchProfilePictureEpic = (action$) =>
  action$.pipe(
    ofType(fetchProfilePictureStart.type),
    switchMap((action) => {
      const userId = action.payload?.userId || getCurrentUserId();

      if (!userId || userId === '0') {
        return of(fetchProfilePictureFailure('User not authenticated'));
      }

      return from(
        axiosInstance.get(`/api/users/${userId}/profile-picture/info`)
      ).pipe(
        map((response) => fetchProfilePictureSuccess(response.data.data)),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || 'Failed to fetch profile picture';
          return of(fetchProfilePictureFailure(errorMessage));
        })
      );
    })
  );

// Delete profile picture
export const deleteProfilePictureEpic = (action$) =>
  action$.pipe(
    ofType(deleteProfilePictureStart.type),
    switchMap(() => {
      const userId = getCurrentUserId();

      if (!userId || userId === '0') {
        return of(deleteProfilePictureFailure('User not authenticated'));
      }

      return from(
        axiosInstance.delete(`/api/users/${userId}/profile-picture`)
      ).pipe(
        map((response) => deleteProfilePictureSuccess(response.data)),
        catchError((error) => {
          const errorMessage = error.response?.data?.message || 'Failed to delete profile picture';
          return of(deleteProfilePictureFailure(errorMessage));
        })
      );
    })
  );

// Combine all epics
export const profilePictureEpics = [
  uploadProfilePictureEpic,
  uploadProfilePictureFromDataEpic,
  fetchProfilePictureEpic,
  deleteProfilePictureEpic,
];
