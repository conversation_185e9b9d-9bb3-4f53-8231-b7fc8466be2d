import { ofType } from 'redux-observable';
import { map, catchError, switchMap } from 'rxjs/operators';
import { of, from } from 'rxjs';
import {
  uploadProfilePictureStart,
  uploadProfilePictureSuccess,
  uploadProfilePictureFailure,
  updateProfilePictureStart,
  updateProfilePictureSuccess,
  updateProfilePictureFailure,
  fetchProfilePictureStart,
  fetchProfilePictureSuccess,
  fetchProfilePictureFailure,
  deleteProfilePictureStart,
  deleteProfilePictureSuccess,
  deleteProfilePictureFailure,
} from '../redux/profilePictureSlice';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';
import { axiosInstance } from '../api/axiosConfig';

// Helper function to validate file
const validateFile = (file) => {
  if (!file) {
    return { valid: false, error: 'No file selected' };
  }

  // File type validation - check both MIME type and extension
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const fileExtension = file.name.toLowerCase().split('.').pop();
  const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
  
  if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
    return { 
      valid: false, 
      error: 'Invalid file type. Please select an image file (JPG, PNG, GIF, WebP)' 
    };
  }

  // File size validation (5MB limit)
  const maxSizeInBytes = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSizeInBytes) {
    return { 
      valid: false, 
      error: 'File size too large. Maximum size is 5MB' 
    };
  }

  // Check for very small files that might be corrupted
  if (file.size < 100) {
    return {
      valid: false,
      error: 'File appears to be corrupted or too small'
    };
  }

  return { valid: true };
};

// Helper function to create FormData
const createFormDataFromFile = (file) => {
  const formData = new FormData();
  formData.append('file', file);
  return formData;
};

// Upload profile picture epic
export const uploadProfilePictureEpic = (action$) =>
  action$.pipe(
    ofType(uploadProfilePictureStart.type),
    switchMap((action) => {
      const userId = getCurrentUserId();
      
      // Get file from payload metadata (not from the action itself to avoid serialization issues)
      const fileMetadata = action.payload?.fileMetadata;
      const file = action.meta?.file; // Use meta for non-serializable data

      if (!userId || userId === '0') {
        return of(uploadProfilePictureFailure('User not authenticated'));
      }

      if (!file) {
        return of(uploadProfilePictureFailure('No file provided'));
      }

      // Validate file
      const validation = validateFile(file);
      if (!validation.valid) {
        return of(uploadProfilePictureFailure(validation.error));
      }

      const formData = createFormDataFromFile(file);

      console.log('Uploading file:', {
        name: file.name,
        type: file.type,
        size: file.size,
        userId: userId
      });

      // Fixed API endpoint path
      return from(
        axiosInstance.post(`/api/v1/profile-pictures/upload/${userId}`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 30000, // 30 seconds timeout for file uploads
        })
      ).pipe(
        map((response) => {
          console.log('Upload response:', response.data);
          if (response.data && response.data.success) {
            return uploadProfilePictureSuccess(response.data.data);
          } else {
            return uploadProfilePictureFailure(response.data?.message || 'Upload failed');
          }
        }),
        catchError((error) => {
          console.error('Upload error:', error);
          let errorMessage = 'Failed to upload profile picture';
          
          if (error.response) {
            const status = error.response.status;
            const data = error.response.data;
            
            console.error('Error response:', {
              status,
              data,
              statusText: error.response.statusText
            });
            
            switch (status) {
              case 400:
                errorMessage = data?.message || 'Bad request - check file format and size';
                break;
              case 401:
                errorMessage = 'Authentication required';
                break;
              case 404:
                errorMessage = 'User not found';
                break;
              case 413:
                errorMessage = 'File too large (max 5MB allowed)';
                break;
              case 415:
                errorMessage = 'Unsupported file type';
                break;
              case 500:
                errorMessage = 'Server error - please try again later';
                break;
              default:
                errorMessage = data?.message || `Upload failed with status ${status}`;
            }
          } else if (error.request) {
            errorMessage = 'Network error - please check your connection';
          } else if (error.code === 'ECONNABORTED') {
            errorMessage = 'Upload timeout - file may be too large';
          } else {
            errorMessage = error.message || 'Upload failed';
          }
          
          return of(uploadProfilePictureFailure(errorMessage));
        })
      );
    })
  );

// Update profile picture epic
export const updateProfilePictureEpic = (action$) =>
  action$.pipe(
    ofType(updateProfilePictureStart.type),
    switchMap((action) => {
      const userId = getCurrentUserId();
      const file = action.meta?.file;

      if (!userId || userId === '0') {
        return of(updateProfilePictureFailure('User not authenticated'));
      }

      if (!file) {
        return of(updateProfilePictureFailure('No file provided'));
      }

      // Validate file
      const validation = validateFile(file);
      if (!validation.valid) {
        return of(updateProfilePictureFailure(validation.error));
      }

      const formData = createFormDataFromFile(file);

      console.log('Updating file:', {
        name: file.name,
        type: file.type,
        size: file.size,
        userId: userId
      });

      return from(
        axiosInstance.put(`/api/v1/profile-pictures/update/${userId}`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: 30000,
        })
      ).pipe(
        map((response) => {
          console.log('Update response:', response.data);
          if (response.data && response.data.success) {
            return updateProfilePictureSuccess(response.data.data);
          } else {
            return updateProfilePictureFailure(response.data?.message || 'Update failed');
          }
        }),
        catchError((error) => {
          console.error('Update error:', error);
          let errorMessage = 'Failed to update profile picture';
          
          if (error.response) {
            const status = error.response.status;
            const data = error.response.data;
            
            switch (status) {
              case 400:
                errorMessage = data?.message || 'Bad request - check file format and size';
                break;
              case 401:
                errorMessage = 'Authentication required';
                break;
              case 404:
                errorMessage = 'User or profile picture not found';
                break;
              case 413:
                errorMessage = 'File too large (max 5MB allowed)';
                break;
              case 415:
                errorMessage = 'Unsupported file type';
                break;
              case 500:
                errorMessage = 'Server error - please try again later';
                break;
              default:
                errorMessage = data?.message || `Update failed with status ${status}`;
            }
          } else if (error.request) {
            errorMessage = 'Network error - please check your connection';
          } else if (error.code === 'ECONNABORTED') {
            errorMessage = 'Update timeout - file may be too large';
          } else {
            errorMessage = error.message || 'Update failed';
          }
          
          return of(updateProfilePictureFailure(errorMessage));
        })
      );
    })
  );

// Fetch profile picture info epic
export const fetchProfilePictureEpic = (action$) =>
  action$.pipe(
    ofType(fetchProfilePictureStart.type),
    switchMap((action) => {
      const userId = action.payload?.userId || getCurrentUserId();

      if (!userId || userId === '0') {
        return of(fetchProfilePictureFailure('User not authenticated'));
      }

      console.log('Fetching profile picture for user:', userId);

      return from(
        axiosInstance.get(`/api/v1/profile-pictures/${userId}`)
      ).pipe(
        map((response) => {
          console.log('Fetch response:', response.data);
          if (response.data && response.data.success) {
            return fetchProfilePictureSuccess(response.data.data);
          } else {
            return fetchProfilePictureFailure(response.data?.message || 'Fetch failed');
          }
        }),
        catchError((error) => {
          console.error('Fetch error:', error);
          
          if (error.response && error.response.status === 404) {
            // This is normal if user has no profile picture
            console.log('No profile picture found for user');
            return of(fetchProfilePictureSuccess(null));
          }
          
          let errorMessage = 'Failed to fetch profile picture';
          if (error.response) {
            const data = error.response.data;
            errorMessage = data?.message || `Fetch failed with status ${error.response.status}`;
          } else if (error.request) {
            errorMessage = 'Network error - please check your connection';
          } else {
            errorMessage = error.message || 'Fetch failed';
          }
          
          return of(fetchProfilePictureFailure(errorMessage));
        })
      );
    })
  );

// Delete profile picture epic
export const deleteProfilePictureEpic = (action$) =>
  action$.pipe(
    ofType(deleteProfilePictureStart.type),
    switchMap(() => {
      const userId = getCurrentUserId();

      if (!userId || userId === '0') {
        return of(deleteProfilePictureFailure('User not authenticated'));
      }

      console.log('Deleting profile picture for user:', userId);

      return from(
        axiosInstance.delete(`/api/v1/profile-pictures/${userId}`)
      ).pipe(
        map((response) => {
          console.log('Delete response:', response.data);
          if (response.data && response.data.success) {
            return deleteProfilePictureSuccess(response.data.message || 'Profile picture deleted successfully');
          } else {
            return deleteProfilePictureFailure(response.data?.message || 'Delete failed');
          }
        }),
        catchError((error) => {
          console.error('Delete error:', error);
          let errorMessage = 'Failed to delete profile picture';
          
          if (error.response) {
            const status = error.response.status;
            const data = error.response.data;
            
            switch (status) {
              case 404:
                errorMessage = 'Profile picture not found';
                break;
              case 401:
                errorMessage = 'Authentication required';
                break;
              case 500:
                errorMessage = 'Server error - please try again later';
                break;
              default:
                errorMessage = data?.message || `Delete failed with status ${status}`;
            }
          } else if (error.request) {
            errorMessage = 'Network error - please check your connection';
          } else {
            errorMessage = error.message || 'Delete failed';
          }
          
          return of(deleteProfilePictureFailure(errorMessage));
        })
      );
    })
  );

// Combine all epics
export const profilePictureEpics = [
  uploadProfilePictureEpic,
  updateProfilePictureEpic,
  fetchProfilePictureEpic,
  deleteProfilePictureEpic,
];