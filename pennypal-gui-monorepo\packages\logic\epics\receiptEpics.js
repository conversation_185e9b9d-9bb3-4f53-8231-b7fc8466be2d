import { ofType } from 'redux-observable';
import { of, from } from 'rxjs';
import { switchMap, catchError, map } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  uploadReceiptRequest,
  uploadReceiptSuccess,
  uploadReceiptFailure,
  saveReceiptRequest,
  saveReceiptSuccess,
  saveReceiptFailure,
  fetchReceiptTransactionIdsRequest,
  fetchReceiptTransactionIdsSuccess,
  fetchReceiptTransactionIdsFailure,
  fetchReceiptDetailsRequest,
  fetchReceiptDetailsSuccess,
  fetchReceiptDetailsFailure,
  addTransactionRequest,
  // addTransactionSuccess,
  // addTransactionFailure,
} from '../redux/receiptSlice';

// Upload receipt epic
export const uploadReceiptEpic = (action$) =>
  action$.pipe(
    ofType(uploadReceiptRequest.type),
    switchMap((action) => {
      const formData = action.payload;
      return from(
        axiosInstance.post('/pennypal/api/receipts/upload', formData, {
          headers: { 'Content-Type': 'multipart/form-data' },
          timeout: 50000,
        })
      ).pipe(
        map((response) => {
          console.log("API Response:", response.data);
          
          // Handle array response by taking first element
          const data = Array.isArray(response.data) ? response.data[0] : response.data;
          
          // Helper function to clean currency values
          const cleanCurrency = (value) => {
            if (!value || value === 'N/A') return 0;
            // Remove parentheses and $ signs, then parse
            const cleaned = String(value).replace(/[()$]/g, '');
            return parseFloat(cleaned) || 0;
          };

          // Improved data transformation
          const transformedData = {
              docType: data.docType || 'receipt',
  docName: data.docName || 'Uploaded Receipt',
   category: data.category || 'Uncategorized',
  qrData: data.qrData || '',
            MerchantName: data.MerchantName || 'Unknown Merchant',
            MerchantAddress: data.MerchantAddress || '',
            MerchantPhoneNumber: data.MerchantPhoneNumber || '',
            TransactionDate: data.TransactionDate || '',
            TransactionTime: data.TransactionTime || '',
            Items: Array.isArray(data.Items) ? 
              data.Items.map(item => ({
                Name: item.Name || '',
                Price: cleanCurrency(item.Price),
                TotalPrice: cleanCurrency(item.TotalPrice),
                Category: item.Category || 'Uncategorized'
              })) : [],
            Subtotal: cleanCurrency(data.Subtotal),
            Tax: cleanCurrency(data.Tax),
            Total: cleanCurrency(data.Total),
            matchingTransactions: Array.isArray(data.matchingTransactions) ? 
              data.matchingTransactions : [],
            transactionId: data.transactionId || 
              (data.matchingTransactions?.[0]?.transactionId || 'No matching transaction found'),
            savedFilePath: data.savedFilePath || data.filePath || '/uploads/default.jpg' // Add this line

            };
          
          console.log('TRANSFORMED DATA:', transformedData);
          return uploadReceiptSuccess(transformedData);
        }),
        catchError((error) => {
          console.error('Upload error:', error);
          return of(uploadReceiptFailure(error.response?.data || error.message));
        })
      );
    })
  );
// Save receipt epic
export const saveReceiptEpic = (action$) =>
  action$.pipe(
    ofType(saveReceiptRequest.type),
    switchMap((action) => {
      const { jsonResponse, selectedTransaction } = action.payload;
      console.log('saveReceiptEpic payload:', { jsonResponse, selectedTransaction });

      const matchingTransactions = Array.isArray(jsonResponse.matchingTransactions)
        ? jsonResponse.matchingTransactions
        : [];

      // Prepare receipt data
      const receiptData = {
        docType: jsonResponse.docType || 'receipt',
        docName: jsonResponse.docName || 'Uploaded Receipt',
        category: jsonResponse.category || 'Uncategorized',
        qrData: jsonResponse.qrData || '',
        MerchantName: jsonResponse.merchantName || jsonResponse.MerchantName || 'Unknown',
        MerchantAddress: jsonResponse.merchantAddress || jsonResponse.MerchantAddress || 'Unknown',
        MerchantPhoneNumber: jsonResponse.merchantPhoneNumber || jsonResponse.MerchantPhoneNumber || 'Unknown',
        TransactionDate: jsonResponse.transactionDate || jsonResponse.TransactionDate || '',
        TransactionTime: jsonResponse.transactionTime || jsonResponse.TransactionTime || '',
        Subtotal: String(jsonResponse.subtotal || jsonResponse.Subtotal || '0.00'),
        Tax: String(jsonResponse.tax || jsonResponse.Tax || '0.00'),
        Total: String(jsonResponse.total || jsonResponse.Total || '0.00'),
        savedFilePath: jsonResponse.savedFilePath || '',
        scannedCopyPath: jsonResponse.scannedCopyPath || '',
        Items: jsonResponse.Items || [],
        transactionId: selectedTransaction ? String(selectedTransaction) : null,
        userId: jsonResponse.userId || 1,
      };

      // CONDITION 1: No transaction selected
      if (!selectedTransaction) {
        return saveReceiptDirectly(receiptData);
      }

      // CONDITION 2: New transaction (temp ID)
      if (String(selectedTransaction).startsWith('temp-')) {
        const selectedTransactionObj = matchingTransactions.find(
          (tx) => tx && String(tx.transactionId) === String(selectedTransaction)
        );

        if (!selectedTransactionObj) {
          console.error('Transaction not found for ID:', selectedTransaction);
          return of(saveReceiptFailure('Selected transaction not found'));
        }

        return saveNewTransactionWithReceipt(selectedTransactionObj, receiptData);
      }

      // CONDITION 3: Existing transaction
      return saveReceiptWithTransaction(receiptData, selectedTransaction);
    })
  );

// Helper functions
const saveReceiptDirectly = (receiptData) => {
  return from(
    axiosInstance.post('/pennypal/api/receipts/saveReceipt', receiptData, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 60000,
    })
  ).pipe(
    map((response) => saveReceiptSuccess(response.data)),
    catchError(handleSaveReceiptError)
  );
};

const saveNewTransactionWithReceipt = (transactionObj, receiptData) => {
  const transactionPayload = {
    transactionId: null, // Let backend assign ID
    transactionDate: transactionObj.transactionDate,
    description: transactionObj.description,
    category: transactionObj.category,
    categoryId: transactionObj.categoryId || 5,
    transactionAmount: transactionObj.transactionAmount,
    budgetId: transactionObj.budgetId || 1,
    userId: transactionObj.userId || 1,
    accountId: transactionObj.accountId || 28,
  };

  return from(
    axiosInstance.post('/pennypal/api/v1/transaction/add', transactionPayload, {
      headers: { 'Content-Type': 'application/json' },
    })
  ).pipe(
    switchMap((transactionResponse) => {
      const transactionId = transactionResponse?.data?.transactionId;
      if (!transactionId) {
        return of(saveReceiptFailure('Transaction saved but no ID returned'));
      }

      // Update receipt with real transaction ID
      const receiptWithTransaction = {
        ...receiptData,
        transactionId: String(transactionId),
      };

      return from(
        axiosInstance.post('/pennypal/api/receipts/saveReceipt', receiptWithTransaction, {
          headers: { 'Content-Type': 'application/json' },
          timeout: 50000,
        })
      ).pipe(
        map((response) => saveReceiptSuccess(response.data)),
        catchError(handleSaveReceiptError)
      );
    }),
    catchError((error) => {
      console.error('Error saving transaction:', error);
      return of(saveReceiptFailure('Failed to save transaction'));
    })
  );
};

const saveReceiptWithTransaction = (receiptData, transactionId) => {
  const receiptWithTransaction = {
    ...receiptData,
    transactionId: String(transactionId),
  };

  return from(
    axiosInstance.post('/pennypal/api/receipts/saveReceipt', receiptWithTransaction, {
      headers: { 'Content-Type': 'application/json' },
      timeout: 50000,
    })
  ).pipe(
    map((response) => saveReceiptSuccess(response.data)),
    catchError(handleSaveReceiptError)
  );
};

const handleSaveReceiptError = (error) => {
  console.error('Error saving receipt:', error);
  return of(saveReceiptFailure(error.response?.data?.message || 'Failed to save receipt'));
};// Fetch receipt transaction IDs epic
export const fetchReceiptTransactionIdsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchReceiptTransactionIdsRequest.type),
    switchMap(() => {
      try {
        const cache = state$.value?.cache;
        // Check if receipt transaction IDs are already cached
        if (cache?.receiptTransactionIdsLoaded && cache?.receiptTransactionIds?.length >= 0) {
          console.log('✅ Using cached receipt transaction IDs in receipt epic');
          return of(fetchReceiptTransactionIdsSuccess(cache.receiptTransactionIds));
        }
        console.log('🔄 Receipt transaction IDs not cached, making API call');

        // Make API call if not cached
        return from(axiosInstance.get('/pennypal/api/receipts/getReceiptTransactionIds')).pipe(
          map((response) => fetchReceiptTransactionIdsSuccess(response.data)),
          catchError((error) =>
            of(
              fetchReceiptTransactionIdsFailure(
                error.response?.data || 'Failed to fetch receipt transaction IDs'
              )
            )
          )
        );
      } catch (error) {
        console.error('Error checking cache state in receipt transaction IDs epic:', error);
        // Make API call on error
        return from(axiosInstance.get('/pennypal/api/receipts/getReceiptTransactionIds')).pipe(
          map((response) => fetchReceiptTransactionIdsSuccess(response.data)),
          catchError((error) =>
            of(
              fetchReceiptTransactionIdsFailure(
                error.response?.data || 'Failed to fetch receipt transaction IDs'
              )
            )
          )
        );
      }
    })
  );

// Fetch receipt details epic
export const fetchReceiptDetailsEpic = (action$) =>
  action$.pipe(
    ofType(fetchReceiptDetailsRequest.type),
    switchMap((action) =>
      from(axiosInstance.get(`/pennypal/api/receipts/getReceiptDetails/${action.payload}`)).pipe(
        map((response) => fetchReceiptDetailsSuccess(response.data)),
        catchError((error) =>
          of(fetchReceiptDetailsFailure(error.response?.data || 'Failed to fetch receipt details'))
        )
      )
    )
  );


  

// Add transaction epic

export const receiptEpics = [
  uploadReceiptEpic,
  saveReceiptEpic,
  fetchReceiptTransactionIdsEpic,
  fetchReceiptDetailsEpic,
  // addTransactionEpic
 
];

export default receiptEpics;