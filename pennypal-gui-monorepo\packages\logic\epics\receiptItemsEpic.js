import { ofType } from 'redux-observable';
import { of, from } from 'rxjs';
import { switchMap, catchError } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchReceiptItemsRequest,
  fetchReceiptItemsSuccess,
  fetchReceiptItemsFailure,
  fetchItemSummarySuccess,
  fetchItemSummaryFailure,
  fetchItemSummaryRequest
} from '../redux/receiptItemsSlice';

export const fetchReceiptItemsEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchReceiptItemsRequest.type),
    switchMap((action) => {
      try {
        const { receiptId } = action.payload || {};
        const cache = state$.value?.cache;

        // Only use cache for "all" items request (not specific receipt)
        if (!receiptId && cache?.receiptItemsLoaded && cache?.receiptItems?.length >= 0) {
          console.log('✅ Using cached receipt items in receipt items epic');
          return of(fetchReceiptItemsSuccess(cache.receiptItems));
        }

        const url = receiptId
          ? `/pennypal/api/v1/receipt-items/by-receipt/${receiptId}`
          : `/pennypal/api/v1/receipt-items/all`;

        console.log('🔄 Receipt items not cached or specific receipt requested, fetching from:', url);

        return from(axiosInstance.get(url)).pipe(
          switchMap((response) => of(fetchReceiptItemsSuccess(response.data))),
          catchError((error) =>
            of(fetchReceiptItemsFailure(error.response?.data || 'Failed to fetch receipt items'))
          )
        );
      } catch (error) {
        console.error('Error checking cache state in receipt items epic:', error);
        // Make API call on error
        const { receiptId } = action.payload || {};
        const url = receiptId
          ? `/pennypal/api/v1/receipt-items/by-receipt/${receiptId}`
          : `/pennypal/api/v1/receipt-items/all`;

        return from(axiosInstance.get(url)).pipe(
          switchMap((response) => of(fetchReceiptItemsSuccess(response.data))),
          catchError((error) =>
            of(fetchReceiptItemsFailure(error.response?.data || 'Failed to fetch receipt items'))
          )
        );
      }
    })
  );
export const fetchItemSummaryEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchItemSummaryRequest.type),
    switchMap(() => {
      try {
        const cache = state$.value?.cache;
        // Check if receipt summary is already cached
        if (cache?.receiptSummaryLoaded && cache?.receiptSummary) {
          console.log('✅ Using cached receipt summary in receipt items epic');
          return of(fetchItemSummarySuccess(cache.receiptSummary));
        }
        console.log('🔄 Receipt summary not cached, making API call');

        // Make API call if not cached
        return from(axiosInstance.get('/pennypal/api/v1/receipt/summary')).pipe(
          switchMap((response) => of(fetchItemSummarySuccess(response.data))),
          catchError((error) =>
            of(fetchItemSummaryFailure(error.response?.data || 'Failed to fetch item summary'))
          )
        );
      } catch (error) {
        console.error('Error checking cache state in receipt summary epic:', error);
        // Make API call on error
        return from(axiosInstance.get('/pennypal/api/v1/receipt/summary')).pipe(
          switchMap((response) => of(fetchItemSummarySuccess(response.data))),
          catchError((error) =>
            of(fetchItemSummaryFailure(error.response?.data || 'Failed to fetch item summary'))
          )
        );
      }
    })
  );