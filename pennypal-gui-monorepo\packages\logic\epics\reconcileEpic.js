import { ofType } from 'redux-observable';
import { of, from } from 'rxjs';
import { switchMap, catchError, timeout } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchReconcileRequest,
  fetchReconcileSuccess,
  fetchReconcileFailure,
  fetchReconciledRequest,
  fetchReconciledSuccess,
  fetchReconciledFailure,
  manualReconcileRequest,
  manualReconcileSuccess,
  manualReconcileFailure,
} from '../redux/reconcileSlice';

export const fetchReconcileEpic = (action$, state$) =>
  action$.pipe(
    ofType(fetchReconcileRequest.type),
    switchMap(() => {
      const showReconciled = state$.value.reconcile.showReconciled || false;
      const params = { reconcileFlag: showReconciled ? 'yes' : 'D' };
      return from(axiosInstance.get('/pennypal/api/v1/reconcile/all', { params })).pipe(
        switchMap((response) => of(fetchReconcileSuccess(response.data))),
        catchError((error) => {
          console.error('Fetch reconcile error:', error);
          return of(fetchReconcileFailure(error.response?.data || 'Failed to fetch reconciled transactions'));
        })
      );
    })
  );

export const manualReconcileEpic = (action$) =>
  action$.pipe(
    ofType(manualReconcileRequest.type),
    switchMap((action) => {
      console.log('Sending manual-reconcile request with IDs:', action.payload);
      return from(axiosInstance.post('/pennypal/api/v1/transaction/manual-reconcile', action.payload)).pipe(
        timeout(60000),
        switchMap((response) => {
          console.log('Manual reconcile response:', response.data);
          return of(manualReconcileSuccess());
        }),
        catchError((error) => {
          console.error('Manual reconcile error:', {
            message: error.response?.data || error.message,
            status: error.response?.status,
            data: error.response?.data
          });
          return of(manualReconcileFailure(error.response?.data?.message || error.response?.data || 'Failed to manually reconcile transactions'));
        })
      );
    })
  );

  export const fetchReconciledEpic = (action$) =>
  action$.pipe(
    ofType(fetchReconciledRequest.type),
    switchMap(() =>
      from(axiosInstance.get('/pennypal/api/v1/reconcile/reconciled')).pipe(
        switchMap((response) => of(fetchReconciledSuccess(response.data))),
        catchError((error) =>
          of(fetchReconciledFailure(error.response?.data || 'Failed to fetch reconciled transactions'))
        )
      )
    )
  );