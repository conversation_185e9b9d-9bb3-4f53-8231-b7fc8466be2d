
// spendingDashboardEpics.js
import { combineEpics, ofType } from 'redux-observable';
import { from, of } from 'rxjs';
import { mergeMap, catchError, map, startWith } from 'rxjs/operators';
import { axiosInstance } from '../api/axiosConfig';
import {
  fetchExpensesStart,
  fetchMonthlyExpensesSuccess,
  fetchYearlyExpensesSuccess,
  fetchExpensesFailure,
} from '../redux/spendingDashboardSlice';
import { fetchBudgetDataStart } from '../redux/cacheSlice';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';

// Updated action creators - no longer need userId parameter
export const fetchMonthlyExpenses = () => ({
  type: 'spendingDashboard/fetchMonthlyExpenses',
});

export const fetchYearlyExpenses = () => ({
  type: 'spendingDashboard/fetchYearlyExpenses',
});

// Epic for fetching monthly expenses
const fetchMonthlyExpensesEpic = (action$, state$) =>
  action$.pipe(
    ofType('spendingDashboard/fetchMonthlyExpenses'),
    mergeMap(() => {
      const userId = getCurrentUserId(); // Get userId from token
      const cache = state$.value?.cache;
      
      // Check if user is authenticated
      if (!userId) {
        return of(fetchExpensesFailure('User not authenticated'));
      }

      // Check if we can use cached data
      if (cache?.budgetDataLoaded && cache?.budgetData?.length >= 0 &&
          cache?.budgetDataParams?.userId == userId) {
        console.log('✅ Using cached budget data for monthly expenses');
        return of(fetchMonthlyExpensesSuccess(cache.budgetData));
      }

      // If not cached, dispatch cache action instead of direct API call
      console.log('� Budget data not cached, dispatching cache action');
      return from([
        fetchBudgetDataStart({ userId }),
        // Return loading state while cache is being populated
        fetchExpensesStart()
      ]);
    })
  );

// Epic to listen for budget data cache updates and update spending dashboard
const updateSpendingFromBudgetCacheEpic = (action$) =>
  action$.pipe(
    ofType('cache/fetchBudgetDataSuccess'),
    mergeMap((action) => {
      console.log('✅ Budget data cache updated, updating spending dashboard');
      return of(fetchMonthlyExpensesSuccess(action.payload));
    })
  );

// Epic for fetching yearly expenses
const fetchYearlyExpensesEpic = (action$) =>
  action$.pipe(
    ofType('spendingDashboard/fetchYearlyExpenses'),
    mergeMap((action) => {
      const userId = getCurrentUserId(); // Get userId from token
      
      // Check if user is authenticated
      if (!userId) {
        return of(fetchExpensesFailure('User not authenticated'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/budget/user/${userId}/year`)).pipe(
        map((response) => {
          console.log('📡 Yearly API response:', response.data);
          return fetchYearlyExpensesSuccess(response.data);
        }),
        catchError((error) => {
          console.error('Error fetching yearly expenses:', error);
          return of(fetchExpensesFailure(error.response?.data || 'Failed to fetch yearly expenses'));
        }),
        startWith(fetchExpensesStart())
      );
    })
  );

// Combine all spending dashboard related epics
export const spendingDashboardEpics = combineEpics(
  fetchMonthlyExpensesEpic,
  fetchYearlyExpensesEpic,
  updateSpendingFromBudgetCacheEpic
);