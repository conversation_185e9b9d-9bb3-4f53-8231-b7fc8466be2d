import { ofType } from 'redux-observable';
import { of, fromEvent, EMPTY } from 'rxjs';
import { switchMap, takeUntil, tap, filter, map } from 'rxjs/operators';
import {
  invalidateRecurringTransactionCache,
  invalidateAccountSyncCache,
  invalidateNotificationsCache
} from '../redux/cacheSlice';
import sseService from '../services/sseService';

// Action types for SSE
export const SSE_CONNECT = 'sse/CONNECT';
export const SSE_DISCONNECT = 'sse/DISCONNECT';
export const SSE_CONNECTION_STATUS_CHANGED = 'sse/CONNECTION_STATUS_CHANGED';
export const SSE_EVENT_RECEIVED = 'sse/EVENT_RECEIVED';

// Action creators
export const sseConnect = () => ({ type: SSE_CONNECT });
export const sseDisconnect = () => ({ type: SSE_DISCONNECT });
export const sseConnectionStatusChanged = (status) => ({ 
  type: SSE_CONNECTION_STATUS_CHANGED, 
  payload: { status } 
});
export const sseEventReceived = (eventType, data) => ({ 
  type: SSE_EVENT_RECEIVED, 
  payload: { eventType, data } 
});

/**
 * Epic to handle SSE connection lifecycle
 */
export const sseConnectionEpic = (action$, state$) =>
  action$.pipe(
    ofType(SSE_CONNECT),
    tap(() => {
      console.log('SSE Epic: Received SSE_CONNECT action');
    }),
    switchMap(() => {
      console.log('SSE Epic: Connecting to SSE...');

      // Connect to SSE service
      sseService.connect().then(() => {
        console.log('SSE Epic: Connection attempt completed');
      }).catch((error) => {
        console.error('SSE Epic: Connection failed:', error);
      });

      // Set up event listeners that dispatch Redux actions
      const connectionListener = (status) => {
        // We can't dispatch directly here, but we can use a different approach
        console.log('SSE connection status changed:', status);
      };

      const cacheInvalidationListener = (data) => {
        // Dispatch cache invalidation action directly
        console.log('SSE cache invalidation received:', data);
        const supportedTypes = [
          'recurring-transactions',
          'account-sync',
          'notifications-recurring-rules',
          'notifications-budget-rules',
          'notifications'
        ];

        if (supportedTypes.includes(data.type)) {
          // Dispatch window event for epic to handle
          console.log('SSE Epic: Dispatching window event for cache invalidation:', data.type);
          window.dispatchEvent(new CustomEvent('sse-cache-invalidation', { detail: data }));
        } else {
          console.log('SSE Epic: Unknown cache invalidation type:', data.type);
        }
      };

      sseService.addConnectionListener(connectionListener);
      sseService.addEventListener('cache-invalidation', cacheInvalidationListener);

      // Return empty observable and clean up on disconnect
      return EMPTY.pipe(
        takeUntil(action$.pipe(ofType(SSE_DISCONNECT)))
      );
    })
  );

/**
 * Epic to handle SSE disconnection
 */
export const sseDisconnectionEpic = (action$) =>
  action$.pipe(
    ofType(SSE_DISCONNECT),
    tap(() => {
      console.log('SSE Epic: Disconnecting from SSE...');
      sseService.disconnect();
    }),
    switchMap(() => EMPTY)
  );

/**
 * Epic to handle incoming SSE events and trigger appropriate actions
 */
export const sseEventHandlerEpic = (action$, state$) =>
  action$.pipe(
    ofType(SSE_EVENT_RECEIVED),
    switchMap((action) => {
      const { eventType, data } = action.payload;
      console.log(`SSE Epic: Handling event type: ${eventType}`, data);

      switch (eventType) {
        case 'cache-invalidation':
          return handleCacheInvalidationEvent(data, state$);
        
        case 'test':
          console.log('SSE Epic: Received test event:', data);
          return EMPTY;
        
        default:
          console.log(`SSE Epic: Unknown event type: ${eventType}`);
          return EMPTY;
      }
    })
  );

/**
 * Handle cache invalidation events
 */
const handleCacheInvalidationEvent = (data, state$) => {
  const { type, userId } = data;
  
  // Get current user ID from state
  const currentState = state$.value;
  const currentUserId = currentState.auth?.user?.id;
  
  // Only process events for the current user
  if (userId !== currentUserId) {
    console.log(`SSE Epic: Ignoring cache invalidation for different user (${userId} vs ${currentUserId})`);
    return EMPTY;
  }

  console.log(`SSE Epic: Processing cache invalidation for user ${userId}, type: ${type}`);

  switch (type) {
    case 'recurring-transactions':
      console.log('SSE Epic: Triggering recurring transaction cache invalidation');
      return of(invalidateRecurringTransactionCache());
    
    default:
      console.log(`SSE Epic: Unknown cache invalidation type: ${type}`);
      return EMPTY;
  }
};

/**
 * Epic to listen for window events from SSE service
 */
export const sseWindowEventEpic = (action$, state$) => {
  if (typeof window === 'undefined') return EMPTY;

  return fromEvent(window, 'sse-cache-invalidation').pipe(
    map(event => {
      const data = event.detail;
      console.log('SSE Epic: Received cache invalidation window event:', data);
      console.log('SSE Epic: Event type:', typeof event, 'Detail type:', typeof data);

      // Get current user ID from state
      const currentState = state$.value;
      const currentUserId = currentState.auth?.user?.id;

      // Only process events for the current user
      if (data.userId !== currentUserId) {
        console.log(`SSE Epic: Ignoring cache invalidation for different user (${data.userId} vs ${currentUserId})`);
        return { type: 'SSE_IGNORED_EVENT' };
      }

      if (data.type === 'recurring-transactions') {
        console.log('SSE Epic: Triggering recurring transaction cache invalidation');
        return invalidateRecurringTransactionCache();
      }

      if (data.type === 'account-sync') {
        console.log('SSE Epic: Triggering account sync cache invalidation');
        console.log('SSE Epic: Cache types to invalidate:', data.cacheTypes);
        console.log('SSE Epic: About to dispatch invalidateAccountSyncCache action');
        const action = invalidateAccountSyncCache();
        console.log('SSE Epic: Created action:', action);
        return action;
      }

      if (data.type === 'notifications-recurring-rules' ||
          data.type === 'notifications-budget-rules' ||
          data.type === 'notifications') {
        console.log('SSE Epic: Triggering notification cache invalidation');
        console.log('SSE Epic: Notification source:', data.source);
        console.log('SSE Epic: About to dispatch invalidateNotificationsCache action');
        const action = invalidateNotificationsCache();
        console.log('SSE Epic: Created action:', action);
        return action;
      }

      return { type: 'SSE_UNKNOWN_EVENT' };
    })
  );
};

/**
 * Epic to automatically connect SSE when user authenticates
 */
export const autoConnectSseEpic = (action$, state$) =>
  action$.pipe(
    ofType('auth/signInSuccess', 'auth/updateAuthFromStorage', 'auth/googleSignInSuccess', 'auth/otpVerifySuccess'),
    tap((action) => {
      console.log('SSE Epic: Received auth action:', action.type);
      const state = state$.value;
      console.log('SSE Epic: Auth state:', {
        isUserAuthenticated: state.auth?.isUserAuthenticated,
        userId: state.auth?.user?.id
      });
    }),
    filter(() => {
      const state = state$.value;
      const isAuthenticated = state.auth?.isUserAuthenticated === true;
      console.log('SSE Epic: Filter check - isAuthenticated:', isAuthenticated);
      return isAuthenticated;
    }),
    switchMap(() => {
      console.log('SSE Epic: User authenticated, auto-connecting SSE');
      return of(sseConnect());
    })
  );

/**
 * Epic to automatically disconnect SSE when user logs out
 */
export const autoDisconnectSseEpic = (action$, state$) =>
  action$.pipe(
    ofType('auth/signOut', 'auth/signInFailure'),
    switchMap(() => {
      console.log('SSE Epic: User logged out, auto-disconnecting SSE');
      return of(sseDisconnect());
    })
  );

// Combine all SSE epics
export const sseEpics = [
  sseConnectionEpic,
  sseDisconnectionEpic,
  sseEventHandlerEpic,
  sseWindowEventEpic,
  autoConnectSseEpic,
  autoDisconnectSseEpic
];