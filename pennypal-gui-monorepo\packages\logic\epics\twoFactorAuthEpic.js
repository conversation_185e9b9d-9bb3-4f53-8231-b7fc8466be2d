import { ofType } from 'redux-observable';
import { catchError, map, mergeMap } from 'rxjs/operators';
import { from, of } from 'rxjs';
import { axiosInstance } from '../api/axiosConfig';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';

import {
  // Setup actions
  setupTwoFactorAuthRequest,
  setupTwoFactorAuthSuccess,
  
  // Enable/Disable actions
  enableTwoFactorAuthRequest,
  enableTwoFactorAuthSuccess,
  disableTwoFactorAuthRequest,
  disableTwoFactorAuthSuccess,
  
  // Verify actions
  verifyTwoFactorCodeRequest,
  verifyTwoFactorCodeSuccess,
  
  // Status actions
  getTwoFactorStatusRequest,
  getTwoFactorStatusSuccess,
  
  // Common failure action
  twoFactorAuthActionFailure
} from '../redux/twoFactorAuthSlice';

// Epic for setting up two-factor authentication
export const setupTwoFactorAuthEpic = (action$) =>
  action$.pipe(
    ofType(setupTwoFactorAuthRequest.type),
    mergeMap((action) => {
      const userId = getCurrentUserId();
      
      if (!userId || userId === '0') {
        console.error('User not authenticated for 2FA setup');
        return of(twoFactorAuthActionFailure('User not authenticated'));
      }

      return from(axiosInstance.post(`/pennypal/api/v1/auth/2fa/setup?userId=${userId}`)).pipe(
        map((response) => {
          console.log('Setup 2FA API Response:', response.data);
          if (response.data.success) {
            return setupTwoFactorAuthSuccess({
              data: response.data.data,
              message: response.data.message
            });
          } else {
            return twoFactorAuthActionFailure(response.data.message || 'Setup failed');
          }
        }),
        catchError((error) => {
          console.error('Setup 2FA failed:', error);
          const errorMsg = error.response?.data?.message || error.message || 'Failed to setup 2FA';
          return of(twoFactorAuthActionFailure(errorMsg));
        })
      );
    })
  );

// Epic for enabling two-factor authentication
export const enableTwoFactorAuthEpic = (action$) =>
  action$.pipe(
    ofType(enableTwoFactorAuthRequest.type),
    mergeMap((action) => {
      const userId = getCurrentUserId();
      const { verificationCode } = action.payload || {};
      
      if (!userId || userId === '0') {
        console.error('User not authenticated for enabling 2FA');
        return of(twoFactorAuthActionFailure('User not authenticated'));
      }

      if (!verificationCode) {
        console.error('Verification code is required for enabling 2FA');
        return of(twoFactorAuthActionFailure('Verification code is required'));
      }

      return from(axiosInstance.post(`/pennypal/api/v1/auth/2fa/enable?userId=${userId}&verificationCode=${verificationCode}`)).pipe(
        map((response) => {
          console.log('Enable 2FA API Response:', response.data);
          if (response.data.success) {
            return enableTwoFactorAuthSuccess({
              message: response.data.message
            });
          } else {
            return twoFactorAuthActionFailure(response.data.message || 'Failed to enable 2FA');
          }
        }),
        catchError((error) => {
          console.error('Enable 2FA failed:', error);
          const errorMsg = error.response?.data?.message || error.message || 'Failed to enable 2FA';
          return of(twoFactorAuthActionFailure(errorMsg));
        })
      );
    })
  );

// Epic for disabling two-factor authentication
export const disableTwoFactorAuthEpic = (action$) =>
  action$.pipe(
    ofType(disableTwoFactorAuthRequest.type),
    mergeMap((action) => {
      const userId = getCurrentUserId();
      const { verificationCode } = action.payload || {};
      
      if (!userId || userId === '0') {
        console.error('User not authenticated for disabling 2FA');
        return of(twoFactorAuthActionFailure('User not authenticated'));
      }

      if (!verificationCode) {
        console.error('Verification code is required for disabling 2FA');
        return of(twoFactorAuthActionFailure('Verification code is required'));
      }

      return from(axiosInstance.post(`/pennypal/api/v1/auth/2fa/disable?userId=${userId}&verificationCode=${verificationCode}`)).pipe(
        map((response) => {
          console.log('Disable 2FA API Response:', response.data);
          if (response.data.success) {
            return disableTwoFactorAuthSuccess({
              message: response.data.message
            });
          } else {
            return twoFactorAuthActionFailure(response.data.message || 'Failed to disable 2FA');
          }
        }),
        catchError((error) => {
          console.error('Disable 2FA failed:', error);
          const errorMsg = error.response?.data?.message || error.message || 'Failed to disable 2FA';
          return of(twoFactorAuthActionFailure(errorMsg));
        })
      );
    })
  );

// Epic for verifying two-factor code - FIXED
export const verifyTwoFactorCodeEpic = (action$) =>
  action$.pipe(
    ofType(verifyTwoFactorCodeRequest.type),
    mergeMap((action) => {
      const userId = getCurrentUserId();
      // Fixed: Changed from 'code' to 'verificationCode' to match the payload
      const { verificationCode } = action.payload || {};
      
      if (!userId || userId === '0') {
        console.error('User not authenticated for verifying 2FA code');
        return of(twoFactorAuthActionFailure('User not authenticated'));
      }

      if (!verificationCode) {
        console.error('Verification code is required for verifying 2FA');
        return of(twoFactorAuthActionFailure('Verification code is required'));
      }

      // Fixed: Use 'verificationCode' instead of 'code'
      return from(axiosInstance.post(`/pennypal/api/v1/auth/2fa/verify?userId=${userId}&code=${verificationCode}`)).pipe(
        map((response) => {
          console.log('Verify 2FA code API Response:', response.data);
          return verifyTwoFactorCodeSuccess({
            verified: response.data.success,
            message: response.data.message
          });
        }),
        catchError((error) => {
          console.error('Verify 2FA code failed:', error);
          const errorMsg = error.response?.data?.message || error.message || 'Failed to verify 2FA code';
          return of(twoFactorAuthActionFailure(errorMsg));
        })
      );
    })
  );

// Epic for getting two-factor authentication status
export const getTwoFactorStatusEpic = (action$) =>
  action$.pipe(
    ofType(getTwoFactorStatusRequest.type),
    mergeMap((action) => {
      const userId = getCurrentUserId();
      
      if (!userId || userId === '0') {
        console.error('User not authenticated for getting 2FA status');
        return of(twoFactorAuthActionFailure('User not authenticated'));
      }

      return from(axiosInstance.get(`/pennypal/api/v1/auth/2fa/status?userId=${userId}`)).pipe(
        map((response) => {
          console.log('Get 2FA status API Response:', response.data);
          if (response.data.success) {
            return getTwoFactorStatusSuccess({
              data: response.data.data,
              message: response.data.message
            });
          } else {
            return twoFactorAuthActionFailure(response.data.message || 'Failed to get 2FA status');
          }
        }),
        catchError((error) => {
          console.error('Get 2FA status failed:', error);
          const errorMsg = error.response?.data?.message || error.message || 'Failed to get 2FA status';
          return of(twoFactorAuthActionFailure(errorMsg));
        })
      );
    })
  );

// Combine all two-factor authentication epics
export const twoFactorAuthEpics = [
  setupTwoFactorAuthEpic,
  enableTwoFactorAuthEpic,
  disableTwoFactorAuthEpic,
  verifyTwoFactorCodeEpic,
  getTwoFactorStatusEpic,
];

export default twoFactorAuthEpics;