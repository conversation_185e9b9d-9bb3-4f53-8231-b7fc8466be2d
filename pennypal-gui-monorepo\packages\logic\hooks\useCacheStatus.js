// useCacheStatus.js - Lightweight cache status hook without infinite loops

import { useSelector } from 'react-redux';
import { getCurrentUserId } from '../../web/src/utils/AuthUtil';

/**
 * Lightweight hook to check cache status without causing infinite loops
 * Use this instead of useCacheAwareness for simple cache checking
 */
export const useCacheStatus = (cacheKey) => {
  const cache = useSelector(state => state.cache);
  const userId = getCurrentUserId();

  if (!cacheKey || !userId) {
    return {
      isLoaded: false,
      isLoading: false,
      hasError: false,
      data: null,
      error: null
    };
  }

  const isLoaded = cache?.[`${cacheKey}Loaded`] && 
                   cache?.[`${cacheKey}Params`]?.userId == userId;
  const isLoading = cache?.[`${cacheKey}Loading`] || false;
  const hasError = !!cache?.[`${cacheKey}Error`];
  const data = cache?.[cacheKey];
  const error = cache?.[`${cacheKey}Error`];

  return {
    isLoaded,
    isLoading,
    hasError,
    data,
    error,
    cache,
    userId
  };
};

/**
 * Hook to check multiple cache keys
 */
export const useMultipleCacheStatus = (cacheKeys = []) => {
  const cache = useSelector(state => state.cache);
  const userId = getCurrentUserId();

  const statuses = cacheKeys.reduce((acc, key) => {
    acc[key] = {
      isLoaded: cache?.[`${key}Loaded`] && cache?.[`${key}Params`]?.userId == userId,
      isLoading: cache?.[`${key}Loading`] || false,
      hasError: !!cache?.[`${key}Error`],
      data: cache?.[key],
      error: cache?.[`${key}Error`]
    };
    return acc;
  }, {});

  const allLoaded = cacheKeys.every(key => statuses[key].isLoaded);
  const anyLoading = cacheKeys.some(key => statuses[key].isLoading);
  const hasErrors = cacheKeys.some(key => statuses[key].hasError);

  return {
    statuses,
    allLoaded,
    anyLoading,
    hasErrors,
    cache,
    userId
  };
};

/**
 * Simple cache checker functions
 */
export const isCacheLoaded = (cache, cacheKey, userId) => {
  return cache?.[`${cacheKey}Loaded`] && cache?.[`${cacheKey}Params`]?.userId == userId;
};

export const isCacheLoading = (cache, cacheKey) => {
  return cache?.[`${cacheKey}Loading`] || false;
};

export const getCacheData = (cache, cacheKey) => {
  return cache?.[cacheKey];
};

export const getCacheError = (cache, cacheKey) => {
  return cache?.[`${cacheKey}Error`];
};

export default useCacheStatus;