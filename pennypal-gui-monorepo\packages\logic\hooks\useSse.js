import { useEffect, useState, useCallback, useRef } from 'react';
import { useSelector } from 'react-redux';
import sseService from '../services/sseService';

/**
 * React hook for SSE (Server-Sent Events) integration
 * Automatically manages connection based on authentication status
 */
export const useSse = () => {
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [lastEvent, setLastEvent] = useState(null);
  const isAuthenticated = useSelector(state => state.auth.isUserAuthenticated);
  const eventListenersRef = useRef(new Map());

  // Handle connection status changes
  const handleConnectionStatus = useCallback((status) => {
    setConnectionStatus(status);
    console.log('SSE connection status changed:', status);
  }, []);

  // Connect/disconnect based on authentication
  useEffect(() => {
    if (isAuthenticated) {
      console.log('User authenticated, connecting to SSE...');
      sseService.addConnectionListener(handleConnectionStatus);
      sseService.connect();
    } else {
      console.log('User not authenticated, disconnecting from SSE...');
      sseService.disconnect();
      sseService.removeConnectionListener(handleConnectionStatus);
      setConnectionStatus('disconnected');
    }

    return () => {
      sseService.removeConnectionListener(handleConnectionStatus);
    };
  }, [isAuthenticated, handleConnectionStatus]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Remove all event listeners added through this hook
      eventListenersRef.current.forEach((listeners, eventType) => {
        listeners.forEach(listener => {
          sseService.removeEventListener(eventType, listener);
        });
      });
      eventListenersRef.current.clear();
    };
  }, []);

  /**
   * Add event listener for specific SSE event types
   */
  const addEventListener = useCallback((eventType, listener) => {
    // Keep track of listeners for cleanup
    if (!eventListenersRef.current.has(eventType)) {
      eventListenersRef.current.set(eventType, []);
    }
    eventListenersRef.current.get(eventType).push(listener);

    // Add to SSE service
    sseService.addEventListener(eventType, listener);

    // Return cleanup function
    return () => {
      sseService.removeEventListener(eventType, listener);
      const listeners = eventListenersRef.current.get(eventType);
      if (listeners) {
        const index = listeners.indexOf(listener);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      }
    };
  }, []);

  /**
   * Send test event (for development/testing)
   */
  const sendTestEvent = useCallback(async (message) => {
    try {
      return await sseService.sendTestEvent(message);
    } catch (error) {
      console.error('Failed to send test event:', error);
      throw error;
    }
  }, []);

  /**
   * Get detailed connection status
   */
  const getDetailedStatus = useCallback(() => {
    return sseService.getConnectionStatus();
  }, []);

  /**
   * Manually reconnect
   */
  const reconnect = useCallback(() => {
    if (isAuthenticated) {
      sseService.disconnect();
      setTimeout(() => sseService.connect(), 1000);
    }
  }, [isAuthenticated]);

  return {
    connectionStatus,
    lastEvent,
    addEventListener,
    sendTestEvent,
    getDetailedStatus,
    reconnect,
    isConnected: connectionStatus === 'connected'
  };
};

/**
 * Hook specifically for cache invalidation events
 */
export const useCacheInvalidationSse = (onCacheInvalidation) => {
  const { addEventListener, isConnected } = useSse();

  useEffect(() => {
    if (!onCacheInvalidation) return;

    const cleanup = addEventListener('cache-invalidation', (data) => {
      console.log('Received cache invalidation event:', data);
      onCacheInvalidation(data);
    });

    return cleanup;
  }, [addEventListener, onCacheInvalidation]);

  return { isConnected };
};

/**
 * Hook for recurring transaction cache invalidation specifically
 */
export const useRecurringTransactionCacheInvalidation = (onInvalidation) => {
  const { addEventListener, isConnected } = useSse();

  useEffect(() => {
    if (!onInvalidation) return;

    const cleanup = addEventListener('cache-invalidation', (data) => {
      if (data.type === 'recurring-transactions') {
        console.log('Received recurring transaction cache invalidation:', data);
        onInvalidation(data);
      }
    });

    return cleanup;
  }, [addEventListener, onInvalidation]);

  return { isConnected };
};

/**
 * Hook for account sync cache invalidation specifically
 */
export const useAccountSyncCacheInvalidation = (onInvalidation) => {
  const { addEventListener, isConnected } = useSse();

  useEffect(() => {
    if (!onInvalidation) return;

    const cleanup = addEventListener('cache-invalidation', (data) => {
      if (data.type === 'account-sync') {
        console.log('Received account sync cache invalidation:', data);
        console.log('Cache types to invalidate:', data.cacheTypes);
        onInvalidation(data);
      }
    });

    return cleanup;
  }, [addEventListener, onInvalidation]);

  return { isConnected };
};

export default useSse;