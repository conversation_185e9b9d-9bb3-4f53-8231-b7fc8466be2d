import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  aggregatedBalances: {},
  
  // fetch state
  fetchLoading: false,
  fetchError: null,
  dataLoaded: false,
  
  // Add cache tracking to prevent multiple API calls
  lastFetchTime: {},
  cacheExpiration: 5 * 60 * 1000, // 5 minutes cache
};

const accountBalanceSlice = createSlice({
  name: 'accountBalance',
  initialState,
  reducers: {
    /* ─────────────── FETCH AGGREGATED BALANCES ─────────────── */
    fetchAggregatedBalancesStart(state, action) {
      const { userId, duration, interval } = action.payload;
      const cacheKey = `${userId}_${duration}_${interval}`;
      
      // Only set loading if we don't already have data or it's expired
      const now = new Date().getTime();
      const lastFetch = state.lastFetchTime[cacheKey];
      const isExpired = !lastFetch || (now - lastFetch > state.cacheExpiration);
      
      if (!state.aggregatedBalances[cacheKey] || isExpired) {
        state.fetchLoading = true;
        state.fetchError = null;
        console.log('accountBalanceSlice: Starting fetch aggregated balances request for:', cacheKey);
      }
    },
    
    fetchAggregatedBalancesSuccess(state, action) {
      const { userId, duration, interval, data } = action.payload;
      const cacheKey = `${userId}_${duration}_${interval}`;
      
      state.aggregatedBalances[cacheKey] = data;
      state.fetchLoading = false;
      state.fetchError = null;
      state.dataLoaded = true;
      state.lastFetchTime[cacheKey] = new Date().getTime();
      console.log('accountBalanceSlice: Aggregated balances fetched successfully for:', cacheKey);
    },
    
    fetchAggregatedBalancesFailure(state, action) {
      state.fetchLoading = false;
      state.fetchError = action.payload;
      state.dataLoaded = true; // Set to true even on failure to prevent retry loops
      console.log('accountBalanceSlice: Aggregated balances fetch failed:', action.payload);
    },
    
    /* ─────────────── UTILITY ACTIONS ───────────── */
    clearFetchError(state) {
      state.fetchError = null;
    },
    
    // Force refresh - clears cache for specific key
    forceRefreshAggregatedBalances(state, action) {
      const { userId, duration, interval } = action.payload;
      const cacheKey = `${userId}_${duration}_${interval}`;
      
      delete state.aggregatedBalances[cacheKey];
      delete state.lastFetchTime[cacheKey];
      state.fetchError = null;
      console.log('accountBalanceSlice: Force refresh initiated for:', cacheKey);
    },
    
    clearAccountBalance(state) {
      return initialState;
    },
    
    // Reset all loading states
    resetLoadingStates(state) {
      state.fetchLoading = false;
    },
    
    // Check if data is stale and needs refresh for specific cache key
    checkDataFreshness(state, action) {
      const { userId, duration, interval } = action.payload;
      const cacheKey = `${userId}_${duration}_${interval}`;
      const now = new Date().getTime();
      const lastFetch = state.lastFetchTime[cacheKey];
      const isExpired = !lastFetch || (now - lastFetch > state.cacheExpiration);
      
      if (isExpired && state.aggregatedBalances[cacheKey]) {
        delete state.aggregatedBalances[cacheKey];
        delete state.lastFetchTime[cacheKey];
        console.log('accountBalanceSlice: Data expired for:', cacheKey);
      }
    }
  },
});

export const {
  fetchAggregatedBalancesStart,
  fetchAggregatedBalancesSuccess,
  fetchAggregatedBalancesFailure,
  clearFetchError,
  forceRefreshAggregatedBalances,
  clearAccountBalance,
  resetLoadingStates,
  checkDataFreshness,
} = accountBalanceSlice.actions;

// Selectors
export const selectAggregatedBalances = (userId, duration, interval) => (state) => {
  const cacheKey = `${userId}_${duration}_${interval}`;
  return state.accountBalance.aggregatedBalances[cacheKey] || [];
};

export const selectFetchLoading = (state) => state.accountBalance.fetchLoading;
export const selectFetchError = (state) => state.accountBalance.fetchError;
export const selectDataLoaded = (state) => state.accountBalance.dataLoaded;

// Helper selector to check if data is fresh for specific parameters
export const selectIsDataFresh = (userId, duration, interval) => (state) => {
  const cacheKey = `${userId}_${duration}_${interval}`;
  const now = new Date().getTime();
  const lastFetch = state.accountBalance.lastFetchTime[cacheKey];
  const cacheExpiration = state.accountBalance.cacheExpiration;
  
  return lastFetch && (now - lastFetch < cacheExpiration);
};

// Helper selector to determine if we should fetch for specific parameters
export const selectShouldFetch = (userId, duration, interval) => (state) => {
  const cacheKey = `${userId}_${duration}_${interval}`;
  const hasData = !!state.accountBalance.aggregatedBalances[cacheKey];
  const isDataFresh = selectIsDataFresh(userId, duration, interval)(state);
  const isLoading = state.accountBalance.fetchLoading;
  
  return !hasData || !isDataFresh || (!hasData && !isLoading);
};

export default accountBalanceSlice.reducer;