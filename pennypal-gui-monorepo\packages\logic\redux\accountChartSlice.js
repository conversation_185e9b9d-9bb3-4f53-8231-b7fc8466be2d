import { createSlice, createAction } from '@reduxjs/toolkit';

// Actions to be used by epics
export const fetchAccountData = createAction('accountChart/fetchAccountData');
export const fetchAccountDataSuccess = createAction('accountChart/fetchAccountDataSuccess');
export const fetchAccountDataFailure = createAction('accountChart/fetchAccountDataFailure');

// Static mock data - only shown when user has no accounts at all
const getStaticMockData = () => {
  return [
    { name: 'Jan \'24', balance: 2500, group_end_date: '2024-01-01', dateSortValue: *************, isMockData: true },
    { name: 'Feb \'24', balance: 2750, group_end_date: '2024-02-01', dateSortValue: *************, isMockData: true },
    { name: 'Mar \'24', balance: 3100, group_end_date: '2024-03-01', dateSortValue: *************, isMockData: true },
    { name: 'Apr \'24', balance: 3350, group_end_date: '2024-04-01', dateSortValue: *************, isMockData: true },
    { name: 'May \'24', balance: 3800, group_end_date: '2024-05-01', dateSortValue: *************, isMockData: true },
    { name: 'Jun \'24', balance: 4200, group_end_date: '2024-06-01', dateSortValue: *************, isMockData: true },
    { name: 'Jul \'24', balance: 4100, group_end_date: '2024-07-01', dateSortValue: *************, isMockData: true },
    { name: 'Aug \'24', balance: 4400, group_end_date: '2024-08-01', dateSortValue: *************, isMockData: true },
    { name: 'Sep \'24', balance: 4650, group_end_date: '2024-09-01', dateSortValue: 1725148800000, isMockData: true },
    { name: 'Oct \'24', balance: 4900, group_end_date: '2024-10-01', dateSortValue: *************, isMockData: true },
    { name: 'Nov \'24', balance: 5200, group_end_date: '2024-11-01', dateSortValue: *************, isMockData: true },
    { name: 'Dec \'24', balance: 5500, group_end_date: '2024-12-01', dateSortValue: *************, isMockData: true }
  ];
};

const initialState = {
  chartData: [],
  selectedChartView: 'area',
  selectedChartType: 'cash',
  selectedTimePeriod: 'yearly',
  loading: false,
  error: null,
  initialized: false,
  hasNoAccounts: false, // New flag to track if user has no accounts at all
  hasNoDataForCurrentType: false, // New flag to track if user has no data for current account type
  currentType: 'cash', // <--- add this
  currentPeriod: 'yearly', // <--- add this
  
  // UI enhancement flags
  uiState: {
    showDataQualityBadge: false,
    chartTransition: 'idle', // 'loading', 'transitioning', 'idle'
    lastUpdateTimestamp: null,
    pendingChartType: null, // Track pending chart type changes
    pendingTimePeriod: null // Track pending time period changes
  }
};

const accountChartSlice = createSlice({
  name: 'accountChart',
  initialState,
  reducers: {
    setChartView: (state, action) => {
      state.selectedChartView = action.payload;
      state.uiState.chartTransition = 'transitioning';
      
      // Clear transition after a brief moment (handled by component)
      // setTimeout(() => {
      //   state.uiState.chartTransition = 'idle';
      // }, 300);
    },
    
    setChartType: (state, action) => {
      const newChartType = action.payload;
      
      // Don't do anything if it's the same chart type
      if (state.selectedChartType === newChartType) {
        return;
      }
      
      state.selectedChartType = newChartType;
      state.currentType = newChartType; // <--- update immediately
      state.uiState.pendingChartType = newChartType;
      
      // Only trigger loading state if we're already initialized
      if (state.initialized) {
        state.loading = true;
        state.error = null;
        state.uiState.chartTransition = 'loading';
      }
    },
    
    setTimePeriod: (state, action) => {
      const newTimePeriod = action.payload;
      
      // Don't do anything if it's the same time period
      if (state.selectedTimePeriod === newTimePeriod) {
        return;
      }
      
      state.selectedTimePeriod = newTimePeriod;
      state.currentPeriod = newTimePeriod; // <--- update immediately
      state.uiState.pendingTimePeriod = newTimePeriod;
      
      // Only set loading to true when changing time period after initial load
      if (state.initialized) {
        state.loading = true;
        state.error = null;
        state.uiState.chartTransition = 'loading';
      }
    },
    
    // Action to set when user has no accounts at all
    setHasNoAccounts: (state, action) => {
      state.hasNoAccounts = action.payload;
      
      if (action.payload) {
        state.chartData = getStaticMockData();
        state.hasNoDataForCurrentType = false;
        state.uiState.showDataQualityBadge = true;
      }
    },
    
    // New action to update UI transition states
    setChartTransition: (state, action) => {
      state.uiState.chartTransition = action.payload;
    },
    
    // Action to clear UI indicators
    clearUIIndicators: (state) => {
      state.uiState.showDataQualityBadge = false;
      state.uiState.chartTransition = 'idle';
    },
    
    // Action to reset error state
    clearError: (state) => {
      state.error = null;
    },
    
    // Action to reset the entire state (useful for logout)
    resetState: () => initialState
  },
  
  extraReducers: (builder) => {
    builder
     
      .addCase(fetchAccountData, (state, action) => {
  state.loading = true;
  state.error = null;
  state.uiState.chartTransition = 'loading';
  state.chartData = []; // <-- Clear chart data when loading new data


        
        // Store the requested parameters for validation
        const payload = action.payload || {};
        state.uiState.pendingChartType = payload.chartType || state.selectedChartType;
        state.uiState.pendingTimePeriod = payload.timePeriod || state.selectedTimePeriod;
      })
      
      .addCase(fetchAccountDataSuccess, (state, action) => {
        const receivedData = action.payload || [];
        
        state.loading = false;
        state.error = null;
        state.initialized = true;
        
        // Clear pending states
        state.uiState.pendingChartType = null;
        state.uiState.pendingTimePeriod = null;
        
        // Determine data state based on received data
        if (receivedData.length === 0) {
          // No data for current type/period combination
          state.hasNoDataForCurrentType = true;
          state.hasNoAccounts = false; // We got a successful response, so user has some accounts
          state.chartData = getStaticMockData();
        } else {
          // We have data
          state.hasNoDataForCurrentType = false;
          state.hasNoAccounts = false;
          state.chartData = receivedData;
        }
        
        // Update UI state
        state.uiState = {
          ...state.uiState,
          chartTransition: 'idle',
          showDataQualityBadge: state.hasNoDataForCurrentType,
          lastUpdateTimestamp: Date.now()
        };
      })
      
      .addCase(fetchAccountDataFailure, (state, action) => {
        const error = action.payload || {};
        
        state.loading = false;
        state.error = error;
        state.initialized = true;
        
        // Clear pending states
        state.uiState.pendingChartType = null;
        state.uiState.pendingTimePeriod = null;
        
        // Handle different error scenarios
        if (error.status === 401) {
          // Authentication error - user might not be logged in
          state.hasNoAccounts = true;
          state.hasNoDataForCurrentType = false;
          state.chartData = getStaticMockData();
        } else if (error.status === 404) {
          // Not found - user has accounts but none for this type
          state.hasNoAccounts = false;
          state.hasNoDataForCurrentType = true;
          state.chartData = [];
        } else {
          state.hasNoAccounts = false;
          state.hasNoDataForCurrentType = false;
          // Don't clear chartData on generic errors
        }
        
        // Update UI state
        state.uiState = {
          ...state.uiState,
          chartTransition: 'idle',
          showDataQualityBadge: state.hasNoAccounts || state.hasNoDataForCurrentType,
          lastUpdateTimestamp: Date.now()
        };
      });
  }
});

// Export slice actions
export const { 
  setChartView, 
  setChartType, 
  setTimePeriod, 
  setHasNoAccounts,
  setChartTransition, 
  clearUIIndicators,
  clearError,
  resetState
} = accountChartSlice.actions;

// Selectors with memoization helpers
export const selectChartData = (state) => state.accountChart.chartData;
export const selectSelectedChartView = (state) => state.accountChart.selectedChartView;
export const selectSelectedChartType = (state) => state.accountChart.selectedChartType;
export const selectSelectedTimePeriod = (state) => state.accountChart.selectedTimePeriod;
export const selectLoading = (state) => state.accountChart.loading;
export const selectError = (state) => state.accountChart.error;
export const selectInitialized = (state) => state.accountChart.initialized;

// Data state selectors
export const selectHasNoAccounts = (state) => state.accountChart.hasNoAccounts;
export const selectHasNoDataForCurrentType = (state) => state.accountChart.hasNoDataForCurrentType;

// UI state selectors
export const selectUIState = (state) => state.accountChart.uiState;
export const selectChartTransition = (state) => state.accountChart.uiState.chartTransition;
export const selectShowDataQualityBadge = (state) => state.accountChart.uiState.showDataQualityBadge;

// Computed selectors
export const selectIsDataEmpty = (state) => {
  const data = selectChartData(state);
  return !data || data.length === 0;
};

export const selectIsMockData = (state) => {
  const data = selectChartData(state);
  return data && data.length > 0 && data[0]?.isMockData === true;
};

export const selectShouldShowEmptyState = (state) => {
  return selectHasNoDataForCurrentType(state) && !selectLoading(state);
};

export const selectShouldShowMockDataBanner = (state) => {
  return selectHasNoAccounts(state) && selectIsMockData(state);
};

// Legacy selectors for backward compatibility
export const selectTimePeriod = selectSelectedTimePeriod;
export const selectChartType = selectSelectedChartType;

// Add selectors for currentType and currentPeriod
export const selectCurrentType = (state) => state.accountChart.currentType;
export const selectCurrentPeriod = (state) => state.accountChart.currentPeriod;

// Helper to format Y axis values as $10k, $1M, $1B, $1T, etc.
export function formatYAxisValue(value) {
  if (value >= 1_000_000_000_000) return `$${(value / 1_000_000_000_000).toLocaleString(undefined, { maximumFractionDigits: 0 })}T`;
  if (value >= 1_000_000_000) return `$${(value / 1_000_000_000).toLocaleString(undefined, { maximumFractionDigits: 0 })}B`;
  if (value >= 1_000_000) return `$${(value / 1_000_000).toLocaleString(undefined, { maximumFractionDigits: 0 })}M`;
  if (value >= 1_000) return `$${(value / 1_000).toLocaleString(undefined, { maximumFractionDigits: 0 })}k`;
  return `$${value}`;
}

// Helper to format numbers with comma separators (e.g., 1000000 => 1,000,000)
export function formatAmountWithCommas(value) {
  if (typeof value !== 'number') return value;

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
}


// Export reducer
export default accountChartSlice.reducer;