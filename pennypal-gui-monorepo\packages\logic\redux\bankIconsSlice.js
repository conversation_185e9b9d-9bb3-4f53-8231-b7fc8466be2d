import { createSlice } from '@reduxjs/toolkit';
import { axiosInstance } from '../api/axiosConfig';

// Common bank name mappings that match your database entries
const COMMON_BANK_MAPPINGS = {
  'bank of america': 'boa_icon',
  'chase': 'chase_icon',
  'wells fargo': 'wells_fargo_icon',
  'citi': 'citi_icon',
  'citibank': 'citi_icon',
  'capital one': 'capital_one_icon',
  'american express': 'amex_icon',
  'venmo': 'venmo_icon',
  'paypal': 'paypal_icon',
  'zelle': 'zelle_icon',
  'united airlines': 'united_icon',
  'starbucks': 'starbucks_icon',
  'mcdonalds': 'mcdonalds_icon',
  'uber': 'uber_icon',
  'default': 'defbank_icon'
};

const initialState = {
  icons: [],
  loading: false,
  error: null,
  iconsLoaded: false,
  iconMap: {}, // For quick icon lookup by name
  tickerMap: {}, // NEW: For quick icon lookup by ticker symbol
};

const bankIconsSlice = createSlice({
  name: 'bankIcons',
  initialState,
  reducers: {
    fetchIconsStart(state) {
      state.loading = true;
      state.error = null;
    },
    fetchIconsSuccess(state, action) {
      state.icons = action.payload;
      state.loading = false;
      state.iconsLoaded = true;
      
      // Build a lookup map for faster icon retrieval by icon name
      const iconMap = {};
      // NEW: Build a lookup map for faster icon retrieval by ticker symbol
      const tickerMap = {};
      
      action.payload.forEach(icon => {
        // Add to icon name map
        iconMap[icon.iconName.toLowerCase()] = icon.svgContent;
        
        // NEW: Add to ticker symbol map if ticker exists
        if (icon.tickerSymbol && icon.tickerSymbol.trim()) {
          tickerMap[icon.tickerSymbol.toUpperCase().trim()] = icon.svgContent;
          console.log(`Added ticker mapping: ${icon.tickerSymbol.toUpperCase().trim()} -> ${icon.iconName}`);
        }
      });
      
      // Add mapped names to the icon lookup
      Object.entries(COMMON_BANK_MAPPINGS).forEach(([bankName, iconName]) => {
        if (iconMap[iconName.toLowerCase()]) {
          iconMap[bankName.toLowerCase()] = iconMap[iconName.toLowerCase()];
        }
      });
      
      state.iconMap = iconMap;
      state.tickerMap = tickerMap; // NEW: Store ticker map
      
      console.log('Icon maps built:', {
        iconMapSize: Object.keys(iconMap).length,
        tickerMapSize: Object.keys(tickerMap).length,
        tickerMappings: tickerMap
      });
    },
    fetchIconsFailure(state, action) {
      state.loading = false;
      state.error = action.payload;
      state.iconsLoaded = true;
      
      // Clear maps on failure
      state.iconMap = {};
      state.tickerMap = {}; // NEW: Clear ticker map
    }
  }
});

export const {
  fetchIconsStart,
  fetchIconsSuccess,
  fetchIconsFailure
} = bankIconsSlice.actions;

// Thunk action to fetch all icons
export const fetchAllIcons = () => async (dispatch, getState) => {
  const { iconsLoaded, loading } = getState().bankIcons;
  
  // Don't fetch if we're already loading or have loaded
  if (iconsLoaded || loading) {
    return Promise.resolve();
  }
  
  try {
    dispatch(fetchIconsStart());
    const response = await axiosInstance.get('pennypal/api/icons/list');
    console.log("Icons API response:", response.data);
    dispatch(fetchIconsSuccess(response.data));
    return response.data;
  } catch (error) {
    console.error("Failed to load icons:", error);
    dispatch(fetchIconsFailure(error.message));
    return [];
  }
};

export default bankIconsSlice.reducer;
