// src/redux/slices/goalSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  goals: [],
  selectedGoal: null,
  loading: false,
  error: null,
  monthlyGoals: [],
  goalSummary: null,
  successMessage: '',
  isAddingGoal: false,
  isAddingContribution: false,
  isUpdatingGoal: false,
  goalAccountBalance: null,
  accountGoalSummary: null
};

const goalSlice = createSlice({
  name: 'goals',
  initialState,
  reducers: {
    // Get all goals
    fetchGoalsRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchGoalsSuccess: (state, action) => {
      state.goals = action.payload;
      state.loading = false;
    },
    fetchGoalsFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Get single goal
    fetchGoalRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchGoalSuccess: (state, action) => {
      state.selectedGoal = action.payload;
      state.loading = false;
      
      // Also update the goal in the goals array if it exists
      const goalIndex = state.goals.findIndex(goal => goal.id === action.payload.id);
      if (goalIndex !== -1) {
        state.goals[goalIndex] = action.payload;
      }
    },
    fetchGoalFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Fetch goal account balance
    fetchGoalAccountBalanceRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchGoalAccountBalanceSuccess: (state, action) => {
      state.goalAccountBalance = action.payload;
      state.loading = false;
    },
    fetchGoalAccountBalanceFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Fetch account goal summary
    fetchAccountGoalSummaryRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchAccountGoalSummarySuccess: (state, action) => {
      state.accountGoalSummary = action.payload;
      state.loading = false;
    },
    fetchAccountGoalSummaryFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Create goal - Enhanced for account selection and amount-based contributions
    createGoalRequest: (state, action) => {
      state.isAddingGoal = true;
      state.error = null;
      
      // Log if initial contributions are being sent
      if (action.payload?.initialContributions?.length > 0) {
        console.log('Creating goal with initial contributions:', action.payload.initialContributions);
      }
    },
    createGoalSuccess: (state, action) => {
      state.goals = [...state.goals, action.payload];
      state.isAddingGoal = false;
      
      // Enhanced success message based on contributions
      const hasInitialContributions = action.payload.accounts && action.payload.accounts.length > 0;
      state.successMessage = hasInitialContributions 
        ? 'Goal created successfully with initial contributions!' 
        : 'Goal created successfully!';
      
      // Log success with contributions info
      if (hasInitialContributions) {
        const totalContributed = action.payload.currentAmount || 0;
        console.log(`Goal created with total contributions: $${totalContributed}`);
      }
    },
    createGoalFailure: (state, action) => {
      state.isAddingGoal = false;
      state.error = action.payload;
    },

    // Update goal - Enhanced for account selection and amount-based contributions
    updateGoalRequest: (state, action) => {
      state.isUpdatingGoal = true;
      state.error = null;
      
      // Log if contributions are being updated
      const { goalData } = action.payload;
      if (goalData?.initialContributions?.length > 0) {
        console.log('Updating goal with contributions:', goalData.initialContributions);
      }
    },
    updateGoalSuccess: (state, action) => {
      // Update goal in goals array
      state.goals = state.goals.map(goal => 
        goal.id === action.payload.id ? action.payload : goal
      );
      
      // Update selected goal if it's the current one
      if (state.selectedGoal && state.selectedGoal.id === action.payload.id) {
        state.selectedGoal = action.payload;
      }
      
      state.isUpdatingGoal = false;
      
      // Enhanced success message
      const hasAccountContributions = action.payload.accounts && action.payload.accounts.length > 0;
      state.successMessage = hasAccountContributions 
        ? 'Goal updated successfully with account contributions!' 
        : 'Goal updated successfully!';
      
      // Log success with contributions info
      console.log(`Goal updated successfully. Current amount: $${action.payload.currentAmount || 0}`);
    },
    updateGoalFailure: (state, action) => {
      state.isUpdatingGoal = false;
      state.error = action.payload;
    },

    // Contribute to goal - Simple account selection and amount
    contributeToGoalRequest: (state, action) => {
      state.isAddingContribution = true;
      state.error = null;
      
      // Log contribution details
      const { goalId, accountId, amount } = action.payload;
      console.log(`Adding contribution: $${amount} from account ${accountId} to goal ${goalId}`);
    },
    contributeToGoalSuccess: (state, action) => {
      // Update goal in goals array
      state.goals = state.goals.map(goal => 
        goal.id === action.payload.id ? action.payload : goal
      );
      
      // Update selected goal if it's the current one
      if (state.selectedGoal && state.selectedGoal.id === action.payload.id) {
        state.selectedGoal = action.payload;
      }
      
      state.isAddingContribution = false;
      state.successMessage = 'Contribution added successfully!';
      
      // Log success
      console.log(`Contribution successful. New goal amount: $${action.payload.currentAmount || 0}`);
    },
    contributeToGoalFailure: (state, action) => {
      state.isAddingContribution = false;
      state.error = action.payload;
    },

    // Get goals for month
    fetchMonthlyGoalsRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchMonthlyGoalsSuccess: (state, action) => {
      state.monthlyGoals = action.payload;
      state.loading = false;
    },
    fetchMonthlyGoalsFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Get goals summary
    fetchGoalSummaryRequest: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchGoalSummarySuccess: (state, action) => {
      state.goalSummary = action.payload;
      state.loading = false;
    },
    fetchGoalSummaryFailure: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },

    // Clear messages and errors
    clearMessages: (state) => {
      state.successMessage = '';
      state.error = null;
    },
    
    // Set selected goal
    setSelectedGoal: (state, action) => {
      state.selectedGoal = action.payload;
    },
    
    // Clear selected goal
    clearSelectedGoal: (state) => {
      state.selectedGoal = null;
    },

    // Reset all loading states - useful for cleanup
    resetLoadingStates: (state) => {
      state.loading = false;
      state.isAddingGoal = false;
      state.isAddingContribution = false;
      state.isUpdatingGoal = false;
    },

    // Helper action to refresh goals after successful operations
    refreshGoalsAfterOperation: (state, action) => {
      // This can be used to trigger a refresh of goals list
      state.loading = true;
    }
  }
});

export const {
  fetchGoalsRequest,
  fetchGoalsSuccess,
  fetchGoalsFailure,
  fetchGoalRequest,
  fetchGoalSuccess,
  fetchGoalFailure,
  createGoalRequest,
  createGoalSuccess,
  createGoalFailure,
  updateGoalRequest,
  updateGoalSuccess,
  updateGoalFailure,
  contributeToGoalRequest,
  contributeToGoalSuccess,
  contributeToGoalFailure,
  fetchMonthlyGoalsRequest,
  fetchMonthlyGoalsSuccess,
  fetchMonthlyGoalsFailure,
  fetchGoalSummaryRequest,
  fetchGoalSummarySuccess,
  fetchGoalSummaryFailure,
  fetchGoalAccountBalanceRequest,
  fetchGoalAccountBalanceSuccess,
  fetchGoalAccountBalanceFailure,
  fetchAccountGoalSummaryRequest,
  fetchAccountGoalSummarySuccess,
  fetchAccountGoalSummaryFailure,
  clearMessages,
  setSelectedGoal,
  clearSelectedGoal,
  resetLoadingStates,
  refreshGoalsAfterOperation
} = goalSlice.actions;

export default goalSlice.reducer;