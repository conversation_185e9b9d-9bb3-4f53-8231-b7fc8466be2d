import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  profilePicture: null,
  profilePictureUrl: null,
  uploadLoading: false,
  uploadError: null,
  uploadSuccess: false,
  updateLoading: false,
  updateError: null,
  updateSuccess: false,
  fetchLoading: false,
  fetchError: null,
  deleteLoading: false,
  deleteError: null,
  deleteSuccess: false,
};

const profilePictureSlice = createSlice({
  name: 'profilePicture',
  initialState,
  reducers: {
    // Upload actions
    uploadProfilePictureStart: (state) => {
      state.uploadLoading = true;
      state.uploadError = null;
      state.uploadSuccess = false;
    },
    uploadProfilePictureSuccess: (state, action) => {
      state.uploadLoading = false;
      state.uploadError = null;
      state.uploadSuccess = true;
      state.profilePicture = action.payload;
      // Based on your DTO structure: profilePictureUrl, contentType, filename, size
      state.profilePictureUrl = action.payload?.profilePictureUrl || null;
    },
    uploadProfilePictureFailure: (state, action) => {
      state.uploadLoading = false;
      state.uploadError = action.payload;
      state.uploadSuccess = false;
    },

    // Update actions
    updateProfilePictureStart: (state) => {
      state.updateLoading = true;
      state.updateError = null;
      state.updateSuccess = false;
    },
    updateProfilePictureSuccess: (state, action) => {
      state.updateLoading = false;
      state.updateError = null;
      state.updateSuccess = true;
      state.profilePicture = action.payload;
      state.profilePictureUrl = action.payload?.profilePictureUrl || null;
    },
    updateProfilePictureFailure: (state, action) => {
      state.updateLoading = false;
      state.updateError = action.payload;
      state.updateSuccess = false;
    },

    // Fetch actions
    fetchProfilePictureStart: (state) => {
      state.fetchLoading = true;
      state.fetchError = null;
    },
    fetchProfilePictureSuccess: (state, action) => {
      state.fetchLoading = false;
      state.fetchError = null;
      state.profilePicture = action.payload;
      state.profilePictureUrl = action.payload?.profilePictureUrl || null;
    },
    fetchProfilePictureFailure: (state, action) => {
      state.fetchLoading = false;
      state.fetchError = action.payload;
    },

    // Delete actions
    deleteProfilePictureStart: (state) => {
      state.deleteLoading = true;
      state.deleteError = null;
      state.deleteSuccess = false;
    },
    deleteProfilePictureSuccess: (state) => {
      state.deleteLoading = false;
      state.deleteError = null;
      state.deleteSuccess = true;
      state.profilePicture = null;
      state.profilePictureUrl = null;
    },
    deleteProfilePictureFailure: (state, action) => {
      state.deleteLoading = false;
      state.deleteError = action.payload;
      state.deleteSuccess = false;
    },

    // Clear actions
    clearUploadSuccess: (state) => {
      state.uploadSuccess = false;
    },
    clearUpdateSuccess: (state) => {
      state.updateSuccess = false;
    },
    clearDeleteSuccess: (state) => {
      state.deleteSuccess = false;
    },
    clearErrors: (state) => {
      state.uploadError = null;
      state.updateError = null;
      state.fetchError = null;
      state.deleteError = null;
    },
    clearAllStates: (state) => {
      Object.assign(state, initialState);
    },
  },
});

export const {
  uploadProfilePictureStart,
  uploadProfilePictureSuccess,
  uploadProfilePictureFailure,
  updateProfilePictureStart,
  updateProfilePictureSuccess,
  updateProfilePictureFailure,
  fetchProfilePictureStart,
  fetchProfilePictureSuccess,
  fetchProfilePictureFailure,
  deleteProfilePictureStart,
  deleteProfilePictureSuccess,
  deleteProfilePictureFailure,
  clearUploadSuccess,
  clearUpdateSuccess,
  clearDeleteSuccess,
  clearErrors,
  clearAllStates,
} = profilePictureSlice.actions;

// Selectors
export const selectProfilePicture = (state) => state.profilePicture.profilePicture;
export const selectProfilePictureUrl = (state) => state.profilePicture.profilePictureUrl;

// Upload selectors
export const selectUploadLoading = (state) => state.profilePicture.uploadLoading;
export const selectUploadError = (state) => state.profilePicture.uploadError;
export const selectUploadSuccess = (state) => state.profilePicture.uploadSuccess;

// Update selectors
export const selectUpdateLoading = (state) => state.profilePicture.updateLoading;
export const selectUpdateError = (state) => state.profilePicture.updateError;
export const selectUpdateSuccess = (state) => state.profilePicture.updateSuccess;

// Fetch selectors
export const selectFetchLoading = (state) => state.profilePicture.fetchLoading;
export const selectFetchError = (state) => state.profilePicture.fetchError;

// Delete selectors
export const selectDeleteLoading = (state) => state.profilePicture.deleteLoading;
export const selectDeleteError = (state) => state.profilePicture.deleteError;
export const selectDeleteSuccess = (state) => state.profilePicture.deleteSuccess;

// Combined loading selector
export const selectAnyLoading = (state) => 
  state.profilePicture.uploadLoading || 
  state.profilePicture.updateLoading || 
  state.profilePicture.fetchLoading || 
  state.profilePicture.deleteLoading;

export default profilePictureSlice.reducer;