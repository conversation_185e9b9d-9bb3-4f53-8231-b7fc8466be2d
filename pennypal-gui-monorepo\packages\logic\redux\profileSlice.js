import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  user: null,
     
  // fetch state
  fetchLoading: false,
  fetchError: null,
  userLoaded: false, // IMPORTANT: Track if user data has been loaded
     
  // update state
  updateLoading: false,
  updateError: null,
  updateSuccess: false,
  
  // Add cache tracking to prevent multiple API calls
  lastFetchTime: null,
  cacheExpiration: 5 * 60 * 1000, // 5 minutes cache
};

const profileSlice = createSlice({
  name: 'profile',
  initialState,
  reducers: {
    /* ─────────────── FETCH USER ─────────────── */
    fetchUserStart(state) {
      // Only set loading if we don't already have data or it's expired
      const now = new Date().getTime();
      const isExpired = !state.lastFetchTime || (now - state.lastFetchTime > state.cacheExpiration);
      
      if (!state.userLoaded || isExpired) {
        state.fetchLoading = true;
        state.fetchError = null;
        console.log('profileSlice: Starting fetch user request');
      }
    },
    fetchUserSuccess(state, action) {
      state.user = action.payload;
      state.fetchLoading = false;
      state.fetchError = null;
      state.userLoaded = true;
      state.lastFetchTime = new Date().getTime();
      console.log('profileSlice: User fetched successfully');
    },
    fetchUserFailure(state, action) {
      state.fetchLoading = false;
      state.fetchError = action.payload;
      state.userLoaded = true; // Set to true even on failure to prevent retry loops
      console.log('profileSlice: User fetch failed:', action.payload);
    },
     
    /* ─────────────── UPDATE USER ────────────── */
    updateUserStart(state) {
      state.updateLoading = true;
      state.updateError = null;
      state.updateSuccess = false;
      console.log('profileSlice: Starting update user request');
    },
    updateUserSuccess(state, action) {
      state.user = action.payload;
      state.updateLoading = false;
      state.updateError = null;
      state.updateSuccess = true;
      state.lastFetchTime = new Date().getTime(); // Update cache time
      console.log('profileSlice: User updated successfully');
    },
    updateUserFailure(state, action) {
      state.updateLoading = false;
      state.updateError = action.payload;
      state.updateSuccess = false;
      console.log('profileSlice: User update failed:', action.payload);
    },
     
    /* ─────────────── UTILITY ACTIONS ───────────── */
    clearFetchError(state) {
      state.fetchError = null;
    },
    clearUpdateError(state) {
      state.updateError = null;
    },
    clearUpdateSuccess(state) {
      state.updateSuccess = false;
    },
    
    // Force refresh - clears cache and sets userLoaded to false
    forceRefreshUser(state) {
      state.userLoaded = false;
      state.lastFetchTime = null;
      state.fetchError = null;
      console.log('profileSlice: Force refresh initiated');
    },
    
    clearProfile(state) {
      return initialState;
    },
         
    // Reset all loading states
    resetLoadingStates(state) {
      state.fetchLoading = false;
      state.updateLoading = false;
    },
    
    // Check if data is stale and needs refresh
    checkDataFreshness(state) {
      const now = new Date().getTime();
      const isExpired = !state.lastFetchTime || (now - state.lastFetchTime > state.cacheExpiration);
      
      if (isExpired && state.userLoaded) {
        state.userLoaded = false;
        state.lastFetchTime = null;
        console.log('profileSlice: Data expired, marking for refresh');
      }
    }
  },
});

export const {
  fetchUserStart,
  fetchUserSuccess,
  fetchUserFailure,
  updateUserStart,
  updateUserSuccess,
  updateUserFailure,
  clearFetchError,
  clearUpdateError,
  clearUpdateSuccess,
  forceRefreshUser,
  clearProfile,
  resetLoadingStates,
  checkDataFreshness,
} = profileSlice.actions;

// Selectors with additional cache checking
export const selectUser = (state) => state.profile.user;
export const selectFetchLoading = (state) => state.profile.fetchLoading;
export const selectFetchError = (state) => state.profile.fetchError;
export const selectUserLoaded = (state) => state.profile.userLoaded;
export const selectUpdateLoading = (state) => state.profile.updateLoading;
export const selectUpdateError = (state) => state.profile.updateError;
export const selectUpdateSuccess = (state) => state.profile.updateSuccess;

// Helper selector to check if data is fresh
export const selectIsDataFresh = (state) => {
  const now = new Date().getTime();
  const lastFetch = state.profile.lastFetchTime;
  const cacheExpiration = state.profile.cacheExpiration;
  
  return lastFetch && (now - lastFetch < cacheExpiration);
};

// Helper selector to determine if we should fetch
export const selectShouldFetch = (state) => {
  return !state.profile.userLoaded || 
         !selectIsDataFresh(state) || 
         (!state.profile.user && !state.profile.fetchLoading);
};

export default profileSlice.reducer;