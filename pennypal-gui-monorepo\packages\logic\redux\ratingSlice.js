// ratingSlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  // Rating submission state
  isSubmitting: false,
  submitError: null,
  submitSuccess: false,
  
  // User rating state
  userRating: null,
  isLoadingUserRating: false,
  userRatingError: null,
  userRatingLoaded: false, // ADDED: Track if user rating has been loaded
  userRatingLastFetch: null, // ADDED: Track last fetch time
  
  // Rating stats state
  ratingStats: null,
  isLoadingStats: false,
  statsError: null,
  statsLoaded: false, // ADDED: Track if stats have been loaded
  statsLastFetch: null, // ADDED: Track last fetch time
  
  // Update rating state
  isUpdating: false,
  updateError: null,
  updateSuccess: false,
  
  // Delete rating state
  isDeleting: false,
  deleteError: null,
  deleteSuccess: false,
  
  // Cache settings
  cacheExpiration: 5 * 60 * 1000, // 5 minutes cache
  currentUserId: null, // ADDED: Track current user to detect user changes
};

const ratingSlice = createSlice({
  name: 'rating',
  initialState,
  reducers: {
    // Submit Rating Actions
    submitRatingRequest: (state) => {
      state.isSubmitting = true;
      state.submitError = null;
      state.submitSuccess = false;
      console.log('ratingSlice: Submit rating request started');
    },
    submitRatingSuccess: (state, action) => {
      state.isSubmitting = false;
      state.submitError = null;
      state.submitSuccess = true;
      state.userRating = action.payload;
      state.userRatingLoaded = true;
      state.userRatingLastFetch = new Date().getTime();
      console.log('ratingSlice: Rating submitted successfully');
    },
    submitRatingFailure: (state, action) => {
      state.isSubmitting = false;
      state.submitError = action.payload;
      state.submitSuccess = false;
      console.log('ratingSlice: Rating submission failed:', action.payload);
    },
    
    // Get User Rating Actions
    getUserRatingRequest: (state, action) => {
      const userId = action.payload;
      const now = new Date().getTime();
      const isExpired = !state.userRatingLastFetch || (now - state.userRatingLastFetch > state.cacheExpiration);
      const userChanged = state.currentUserId !== userId;
      
      // Only set loading if we should actually fetch
      if (!state.userRatingLoaded || isExpired || userChanged || state.userRatingError) {
        state.isLoadingUserRating = true;
        state.userRatingError = null;
        state.currentUserId = userId;
        console.log('ratingSlice: Get user rating request started for user:', userId);
      } else {
        console.log('ratingSlice: User rating already loaded and fresh, skipping API call');
      }
    },
    getUserRatingSuccess: (state, action) => {
      state.isLoadingUserRating = false;
      state.userRatingError = null;
      state.userRating = action.payload;
      state.userRatingLoaded = true;
      state.userRatingLastFetch = new Date().getTime();
      console.log('ratingSlice: User rating loaded successfully');
    },
    getUserRatingFailure: (state, action) => {
      state.isLoadingUserRating = false;
      state.userRatingError = action.payload;
      state.userRatingLoaded = true; // Set to true to prevent retry loops
      console.log('ratingSlice: User rating loading failed:', action.payload);
    },
    
    // Get Rating Stats Actions
    getRatingStatsRequest: (state, action) => {
      const userId = action.payload;
      const now = new Date().getTime();
      const isExpired = !state.statsLastFetch || (now - state.statsLastFetch > state.cacheExpiration);
      const userChanged = state.currentUserId !== userId;
      
      // Only set loading if we should actually fetch
      if (!state.statsLoaded || isExpired || userChanged || state.statsError) {
        state.isLoadingStats = true;
        state.statsError = null;
        state.currentUserId = userId;
        console.log('ratingSlice: Get rating stats request started for user:', userId);
      } else {
        console.log('ratingSlice: Rating stats already loaded and fresh, skipping API call');
      }
    },
    getRatingStatsSuccess: (state, action) => {
      state.isLoadingStats = false;
      state.statsError = null;
      state.ratingStats = action.payload;
      state.statsLoaded = true;
      state.statsLastFetch = new Date().getTime();
      console.log('ratingSlice: Rating stats loaded successfully');
    },
    getRatingStatsFailure: (state, action) => {
      state.isLoadingStats = false;
      state.statsError = action.payload;
      state.statsLoaded = true; // Set to true to prevent retry loops
      console.log('ratingSlice: Rating stats loading failed:', action.payload);
    },
    
    // Update Rating Actions
    updateRatingRequest: (state) => {
      state.isUpdating = true;
      state.updateError = null;
      state.updateSuccess = false;
      console.log('ratingSlice: Update rating request started');
    },
    updateRatingSuccess: (state, action) => {
      state.isUpdating = false;
      state.updateError = null;
      state.updateSuccess = true;
      state.userRating = action.payload;
      state.userRatingLastFetch = new Date().getTime(); // Update cache time
      console.log('ratingSlice: Rating updated successfully');
    },
    updateRatingFailure: (state, action) => {
      state.isUpdating = false;
      state.updateError = action.payload;
      state.updateSuccess = false;
      console.log('ratingSlice: Rating update failed:', action.payload);
    },
    
    // Delete Rating Actions
    deleteRatingRequest: (state) => {
      state.isDeleting = true;
      state.deleteError = null;
      state.deleteSuccess = false;
      console.log('ratingSlice: Delete rating request started');
    },
    deleteRatingSuccess: (state) => {
      state.isDeleting = false;
      state.deleteError = null;
      state.deleteSuccess = true;
      state.userRating = null;
      state.userRatingLoaded = false; // Reset loaded state
      state.userRatingLastFetch = null;
      console.log('ratingSlice: Rating deleted successfully');
    },
    deleteRatingFailure: (state, action) => {
      state.isDeleting = false;
      state.deleteError = action.payload;
      state.deleteSuccess = false;
      console.log('ratingSlice: Rating deletion failed:', action.payload);
    },
    
    // Reset Actions
    resetSubmitState: (state) => {
      state.submitError = null;
      state.submitSuccess = false;
    },
    resetUpdateState: (state) => {
      state.updateError = null;
      state.updateSuccess = false;
    },
    resetDeleteState: (state) => {
      state.deleteError = null;
      state.deleteSuccess = false;
    },
    
    // Force refresh rating data
    forceRefreshRatingData: (state) => {
      state.userRatingLoaded = false;
      state.statsLoaded = false;
      state.userRatingLastFetch = null;
      state.statsLastFetch = null;
      state.userRatingError = null;
      state.statsError = null;
      console.log('ratingSlice: Force refresh rating data initiated');
    },
    
    // Clear rating data (on user change)
    clearRatingData: (state) => {
      state.userRating = null;
      state.ratingStats = null;
      state.userRatingLoaded = false;
      state.statsLoaded = false;
      state.userRatingLastFetch = null;
      state.statsLastFetch = null;
      state.userRatingError = null;
      state.statsError = null;
      state.currentUserId = null;
      console.log('ratingSlice: Rating data cleared');
    },
    
    // Check if user rating data needs refresh
    checkUserRatingFreshness: (state, action) => {
      const userId = action.payload;
      const now = new Date().getTime();
      const isExpired = !state.userRatingLastFetch || (now - state.userRatingLastFetch > state.cacheExpiration);
      const userChanged = state.currentUserId !== userId;
      
      if ((isExpired || userChanged) && state.userRatingLoaded) {
        state.userRatingLoaded = false;
        state.userRatingLastFetch = null;
        console.log('ratingSlice: User rating data expired, marking for refresh');
      }
    },
    
    // Check if stats data needs refresh
    checkStatsFreshness: (state, action) => {
      const userId = action.payload;
      const now = new Date().getTime();
      const isExpired = !state.statsLastFetch || (now - state.statsLastFetch > state.cacheExpiration);
      const userChanged = state.currentUserId !== userId;
      
      if ((isExpired || userChanged) && state.statsLoaded) {
        state.statsLoaded = false;
        state.statsLastFetch = null;
        console.log('ratingSlice: Stats data expired, marking for refresh');
      }
    },
  },
});

export const {
  submitRatingRequest,
  submitRatingSuccess,
  submitRatingFailure,
  getUserRatingRequest,
  getUserRatingSuccess,
  getUserRatingFailure,
  getRatingStatsRequest,
  getRatingStatsSuccess,
  getRatingStatsFailure,
  updateRatingRequest,
  updateRatingSuccess,
  updateRatingFailure,
  deleteRatingRequest,
  deleteRatingSuccess,
  deleteRatingFailure,
  resetSubmitState,
  resetUpdateState,
  resetDeleteState,
  forceRefreshRatingData,
  clearRatingData,
  checkUserRatingFreshness,
  checkStatsFreshness,
} = ratingSlice.actions;

// Selectors with cache checking
export const selectUserRating = (state) => state.rating.userRating;
export const selectRatingStats = (state) => state.rating.ratingStats;
export const selectIsLoadingUserRating = (state) => state.rating.isLoadingUserRating;
export const selectIsLoadingStats = (state) => state.rating.isLoadingStats;
export const selectUserRatingLoaded = (state) => state.rating.userRatingLoaded;
export const selectStatsLoaded = (state) => state.rating.statsLoaded;

// Helper selectors to determine if we should fetch
export const selectShouldFetchUserRating = (state, userId) => {
  const now = new Date().getTime();
  const isExpired = !state.rating.userRatingLastFetch || 
                   (now - state.rating.userRatingLastFetch > state.rating.cacheExpiration);
  const userChanged = state.rating.currentUserId !== userId;
  
  return !state.rating.userRatingLoaded || 
         isExpired || 
         userChanged || 
         state.rating.userRatingError ||
         (!state.rating.userRating && !state.rating.isLoadingUserRating);
};

export const selectShouldFetchStats = (state, userId) => {
  const now = new Date().getTime();
  const isExpired = !state.rating.statsLastFetch || 
                   (now - state.rating.statsLastFetch > state.rating.cacheExpiration);
  const userChanged = state.rating.currentUserId !== userId;
  
  return !state.rating.statsLoaded || 
         isExpired || 
         userChanged || 
         state.rating.statsError ||
         (!state.rating.ratingStats && !state.rating.isLoadingStats);
};

export default ratingSlice.reducer;