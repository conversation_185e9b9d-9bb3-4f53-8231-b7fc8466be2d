import { createSlice } from '@reduxjs/toolkit';

const reconcileSlice = createSlice({
  name: 'reconcile',
  initialState: {
    transactions: [],
    reconciledTransactions: [],
    loading: false,
    error: null,
    reconciling: false,
    showReconciled: false,
    status: 'idle', // Track reconciliation status
  },
  reducers: {
    fetchReconcileRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchReconcileSuccess(state, action) {
      state.loading = false;
      state.transactions = action.payload;
    },
    fetchReconcileFailure(state, action) {
      state.loading = false;
      state.error = action.payload;
    },
    fetchReconciledRequest(state) {
      state.loading = true;
      state.error = null;
    },
    fetchReconciledSuccess(state, action) {
      state.loading = false;
      state.reconciledTransactions = action.payload;
    },
    fetchReconciledFailure(state, action) {
      state.loading = false;
      state.error = action.payload;
    },
    manualReconcileRequest(state) {
      state.reconciling = true;
      state.error = null;
      state.status = 'idle';
    },
    manualReconcileSuccess(state) {
      state.reconciling = false;
      state.status = 'success';
    },
    manualReconcileFailure(state, action) {
      state.reconciling = false;
      state.error = action.payload;
      state.status = 'error';
    },
    resetReconcileStatus(state) {
      state.status = 'idle';
    },
    toggleShowReconciled(state) {
      state.showReconciled = !state.showReconciled;
    },
  },
});

export const {
  fetchReconcileRequest,
  fetchReconcileSuccess,
  fetchReconcileFailure,
  fetchReconciledRequest,
  fetchReconciledSuccess,
  fetchReconciledFailure,
  manualReconcileRequest,
  manualReconcileSuccess,
  manualReconcileFailure,
  resetReconcileStatus,
  toggleShowReconciled
} = reconcileSlice.actions;

export default reconcileSlice.reducer;