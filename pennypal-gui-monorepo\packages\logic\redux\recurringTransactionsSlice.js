import { createSlice } from '@reduxjs/toolkit';
const initialState = {
  recurringTransactions: [],
  loading: false,
  error: null,
  selectedMonth: new Date().getMonth(),
  selectedYear: new Date().getFullYear(),
  viewMode: 'calendar', // 'calendar' or 'grid'
};
export const recurringTransactionsSlice = createSlice({
  name: 'recurringTransactions',
  initialState,
  reducers: {
    fetchRecurringTransactionsStart: (state) => {
      state.loading = true;
      state.error = null;
    },
    fetchRecurringTransactionsSuccess: (state, action) => {
      state.recurringTransactions = action.payload;
      state.loading = false;
      state.error = null;
    },
    fetchRecurringTransactionsFailed: (state, action) => {
      state.loading = false;
      state.error = action.payload;
    },
    setSelectedMonth: (state, action) => {
      state.selectedMonth = action.payload;
    },
    setSelectedYear: (state, action) => {
      state.selectedYear = action.payload;
    },
    setViewMode: (state, action) => {
      state.viewMode = action.payload;
    },
    goToPrevMonth: (state) => {
      if (state.selectedMonth === 0) {
        state.selectedMonth = 11;
        state.selectedYear -= 1;
      } else {
        state.selectedMonth -= 1;
      }
    },
    goToNextMonth: (state) => {
      if (state.selectedMonth === 11) {
        state.selectedMonth = 0;
        state.selectedYear += 1;
      } else {
        state.selectedMonth += 1;
      }
    },
    goToCurrentMonth: (state) => {
      const currentDate = new Date();
      state.selectedMonth = currentDate.getMonth();
      state.selectedYear = currentDate.getFullYear();
    },
    setTransactions: (state, action) => {
      state.recurringTransactions = action.payload;
      state.loading = false;
      state.error = null;
    },
    setError: (state, action) => {
      state.error = action.payload;
      state.loading = false;
    }
  },
});
export const {
  fetchRecurringTransactionsStart,
  fetchRecurringTransactionsSuccess,
  fetchRecurringTransactionsFailed,
  setSelectedMonth,
  setSelectedYear,
  setViewMode,
  goToPrevMonth,
  goToNextMonth,
  goToCurrentMonth,
  setTransactions,
  setError
} = recurringTransactionsSlice.actions;
// Selectors
export const selectRecurringTransactions = (state) => state.recurringTransactions.recurringTransactions;
export const selectLoading = (state) => state.recurringTransactions.loading;
export const selectError = (state) => state.recurringTransactions.error;
export const selectSelectedMonth = (state) => state.recurringTransactions.selectedMonth;
export const selectSelectedYear = (state) => state.recurringTransactions.selectedYear;
export const selectViewMode = (state) => state.recurringTransactions.viewMode;
export default recurringTransactionsSlice.reducer;