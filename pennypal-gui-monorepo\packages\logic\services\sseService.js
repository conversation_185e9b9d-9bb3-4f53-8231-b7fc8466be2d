import { getAuthToken } from '../api/axiosConfig';
import Cookies from 'js-cookie';

class SseService {
  constructor() {
    this.eventSource = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000; // Start with 1 second
    this.maxReconnectDelay = 30000; // Max 30 seconds
    this.eventListeners = new Map();
    this.connectionListeners = [];
    this.baseUrl = this.getBaseUrl();

    // SSE chunk processing state
    this.currentEventType = null;
    this.currentData = null;
  }

  getBaseUrl() {
    // Use the same base URL logic as axiosConfig
    if (typeof window !== 'undefined') {
      return 'http://localhost:8080'; // Web
    }
    // For React Native, you'd need to determine the platform
    return 'http://localhost:8080';
  }

  /**
   * Get auth token for web or mobile
   */
  getToken() {
    console.log('SSE Service: getToken() called, window exists:', typeof window !== 'undefined');

    // For web environment, get token from cookies
    if (typeof window !== 'undefined') {
      const TOKEN_COOKIE_NAME = 'pennypal_jwt_token';
      console.log('SSE Service: Getting token from cookies with name:', TOKEN_COOKIE_NAME);
      const token = Cookies.get(TOKEN_COOKIE_NAME);
      console.log('SSE Service: Cookie token result:', token ? 'FOUND' : 'NOT_FOUND');
      console.log('SSE Service: Token length:', token ? token.length : 0);
      console.log('SSE Service: Returning token from getToken()');
      return Promise.resolve(token);
    }
    // For mobile, use the existing getAuthToken function
    console.log('SSE Service: Using mobile getAuthToken');
    return getAuthToken();
  }

  /**
   * Connect using fetch API with custom headers (since EventSource doesn't support them)
   */
  async connectWithFetch(url, token) {
    console.log('SSE Service: connectWithFetch called with URL:', url);

    try {
      console.log('SSE Service: Making fetch request...');
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        }
      });

      console.log('SSE Service: Fetch response received:', response.status, response.statusText);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const reader = response.body.getReader();
      const decoder = new TextDecoder();

      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.reconnectDelay = 1000;
      this.notifyConnectionListeners('connected');

      // Read the stream
      const readStream = async () => {
        try {
          while (true) {
            const { done, value } = await reader.read();

            if (done) {
              console.log('SSE stream ended');
              this.isConnected = false;
              this.notifyConnectionListeners('disconnected');
              this.scheduleReconnect();
              break;
            }

            const chunk = decoder.decode(value, { stream: true });
            this.processSSEChunk(chunk);
          }
        } catch (error) {
          console.error('Error reading SSE stream:', error);
          this.isConnected = false;
          this.notifyConnectionListeners('error');
          this.scheduleReconnect();
        }
      };

      readStream();

    } catch (error) {
      console.error('Failed to connect with fetch:', error);
      this.isConnected = false;
      this.notifyConnectionListeners('error');
      this.scheduleReconnect();
    }
  }

  /**
   * Process SSE chunk data
   */
  processSSEChunk(chunk) {
    console.log('SSE Service: Processing chunk:', chunk);
    const lines = chunk.split('\n');

    for (const line of lines) {
      if (line.startsWith('event:')) {
        this.currentEventType = line.substring(6).trim();
        console.log('SSE Service: Event type:', this.currentEventType);
      } else if (line.startsWith('data:')) {
        const dataLine = line.substring(5).trim();
        console.log('SSE Service: Event data line:', dataLine);

        // Accumulate data (in case it comes in multiple data: lines)
        if (this.currentData) {
          this.currentData += '\n' + dataLine;
        } else {
          this.currentData = dataLine;
        }
      } else if (line === '' && this.currentEventType && this.currentData) {
        // End of event, process it
        console.log('SSE Service: Processing event:', this.currentEventType, 'with data:', this.currentData);

        // Try to parse as JSON first, fall back to plain text
        let parsedData;
        try {
          parsedData = JSON.parse(this.currentData);
          console.log('SSE Service: Successfully parsed JSON data:', parsedData);
        } catch (e) {
          console.log('SSE Service: Data is not JSON, using as plain text:', this.currentData);
          parsedData = this.currentData;
        }

        this.handleEvent(this.currentEventType, parsedData);
        this.currentEventType = null;
        this.currentData = null;
      } else if (line.trim() !== '' && !line.startsWith('event:') && !line.startsWith('data:')) {
        // Handle case where JSON data comes without data: prefix (chunked response)
        console.log('SSE Service: Found standalone data line:', line);

        // If we have a current event type but no data yet, this might be the data
        if (this.currentEventType && !this.currentData) {
          this.currentData = line.trim();
          console.log('SSE Service: Assigned standalone data to current event:', this.currentEventType);

          // Try to process immediately if it looks like complete JSON
          try {
            const parsedData = JSON.parse(this.currentData);
            console.log('SSE Service: Successfully parsed standalone JSON data:', parsedData);
            this.handleEvent(this.currentEventType, parsedData);
            this.currentEventType = null;
            this.currentData = null;
          } catch (e) {
            console.log('SSE Service: Standalone data is not complete JSON, waiting for more data');
          }
        }
      }
    }
  }

  /**
   * Connect to SSE endpoint
   */
  async connect() {
    console.log('SSE Service: connect() called');

    if (this.isConnected || this.eventSource) {
      console.log('SSE already connected or connecting');
      return;
    }

    try {
      console.log('SSE Service: Getting token...');
      const token = await this.getToken();
      console.log('SSE Service: Token retrieved:', token ? 'YES' : 'NO');
      console.log('SSE Service: Token value (first 20 chars):', token ? token.substring(0, 20) + '...' : 'null');

      if (!token) {
        console.error('SSE Service: No auth token available for SSE connection');
        return;
      }

      const url = `${this.baseUrl}/pennypal/api/v1/sse/connect`;
      console.log('SSE Service: Connecting to SSE:', url);

      // Use fetch-based SSE implementation since EventSource doesn't support custom headers
      console.log('SSE Service: About to call connectWithFetch...');
      await this.connectWithFetch(url, token);
      console.log('SSE Service: connectWithFetch completed');

    } catch (error) {
      console.error('SSE Service: Failed to connect to SSE:', error);
      this.scheduleReconnect();
    }
  }

  /**
   * Handle incoming events
   */
  handleEvent(eventType, data) {
    const listeners = this.eventListeners.get(eventType) || [];
    listeners.forEach(listener => {
      try {
        listener(data);
      } catch (error) {
        console.error(`Error in SSE event listener for ${eventType}:`, error);
      }
    });
  }

  /**
   * Add event listener for specific event types
   */
  addEventListener(eventType, listener) {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, []);
    }
    this.eventListeners.get(eventType).push(listener);
    
    console.log(`Added SSE event listener for: ${eventType}`);
  }

  /**
   * Remove event listener
   */
  removeEventListener(eventType, listener) {
    const listeners = this.eventListeners.get(eventType);
    if (listeners) {
      const index = listeners.indexOf(listener);
      if (index > -1) {
        listeners.splice(index, 1);
        console.log(`Removed SSE event listener for: ${eventType}`);
      }
    }
  }

  /**
   * Add connection status listener
   */
  addConnectionListener(listener) {
    this.connectionListeners.push(listener);
  }

  /**
   * Remove connection status listener
   */
  removeConnectionListener(listener) {
    const index = this.connectionListeners.indexOf(listener);
    if (index > -1) {
      this.connectionListeners.splice(index, 1);
    }
  }

  /**
   * Notify connection listeners
   */
  notifyConnectionListeners(status) {
    this.connectionListeners.forEach(listener => {
      try {
        listener(status);
      } catch (error) {
        console.error('Error in SSE connection listener:', error);
      }
    });
  }

  /**
   * Schedule reconnection with exponential backoff
   */
  scheduleReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Max SSE reconnection attempts reached');
      this.notifyConnectionListeners('max-retries-reached');
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);
    
    console.log(`Scheduling SSE reconnection attempt ${this.reconnectAttempts} in ${delay}ms`);
    
    setTimeout(() => {
      this.disconnect();
      this.connect();
    }, delay);
  }

  /**
   * Disconnect from SSE
   */
  disconnect() {
    console.log('Disconnecting from SSE');
    this.isConnected = false;
    this.notifyConnectionListeners('disconnected');

    // Note: With fetch-based approach, the connection will naturally close
    // when the component unmounts or the page is refreshed
  }

  /**
   * Get connection status
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      reconnectAttempts: this.reconnectAttempts,
      readyState: this.isConnected ? 1 : 0 // 1 = OPEN, 0 = CONNECTING/CLOSED
    };
  }

  /**
   * Send test event (for development/testing)
   */
  async sendTestEvent(message = 'Test from frontend') {
    try {
      const token = await this.getToken();
      if (!token) {
        console.error('No auth token available for test event');
        return;
      }

      const response = await fetch(`${this.baseUrl}/pennypal/api/v1/sse/test-event`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ message })
      });

      const result = await response.json();
      console.log('Test event response:', result);
      return result;
    } catch (error) {
      console.error('Failed to send test event:', error);
      throw error;
    }
  }
}

// Create singleton instance
const sseService = new SseService();

export default sseService;