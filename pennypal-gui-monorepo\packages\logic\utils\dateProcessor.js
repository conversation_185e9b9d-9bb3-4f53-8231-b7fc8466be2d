/**
 * Robust date parsing and processing system
 * Handles natural language dates, multiple formats, and relative date expressions
 */

import { textProcessingCache } from './textProcessing.js';

// Date format patterns with priority (higher priority = more specific)
const DATE_PATTERNS = [
  // ISO formats (highest priority)
  { pattern: /(\d{4})-(\d{1,2})-(\d{1,2})/, format: 'YYYY-MM-DD', priority: 10 },
  { pattern: /(\d{4})\/(\d{1,2})\/(\d{1,2})/, format: 'YYYY/MM/DD', priority: 9 },
  
  // US formats
  { pattern: /(\d{1,2})\/(\d{1,2})\/(\d{4})/, format: 'MM/DD/YYYY', priority: 8 },
  { pattern: /(\d{1,2})-(\d{1,2})-(\d{4})/, format: 'MM-DD-YYYY', priority: 7 },
  
  // European formats
  { pattern: /(\d{1,2})\/(\d{1,2})\/(\d{4})/, format: 'DD/MM/YYYY', priority: 6 },
  { pattern: /(\d{1,2})\.(\d{1,2})\.(\d{4})/, format: 'DD.MM.YYYY', priority: 6 },
  
  // Short year formats
  { pattern: /(\d{1,2})\/(\d{1,2})\/(\d{2})/, format: 'MM/DD/YY', priority: 5 },
  { pattern: /(\d{1,2})-(\d{1,2})-(\d{2})/, format: 'MM-DD-YY', priority: 4 },
  
  // Month name formats
  { pattern: /(\w+)\s+(\d{1,2}),?\s+(\d{4})/, format: 'MONTH DD, YYYY', priority: 8 },
  { pattern: /(\d{1,2})\s+(\w+)\s+(\d{4})/, format: 'DD MONTH YYYY', priority: 7 },
  { pattern: /(\w+)\s+(\d{1,2})/, format: 'MONTH DD', priority: 3 },
];

// Month name mappings
const MONTH_NAMES = {
  'january': 1, 'jan': 1,
  'february': 2, 'feb': 2,
  'march': 3, 'mar': 3,
  'april': 4, 'apr': 4,
  'may': 5,
  'june': 6, 'jun': 6,
  'july': 7, 'jul': 7,
  'august': 8, 'aug': 8,
  'september': 9, 'sep': 9, 'sept': 9,
  'october': 10, 'oct': 10,
  'november': 11, 'nov': 11,
  'december': 12, 'dec': 12
};

// Relative date patterns
const RELATIVE_DATE_PATTERNS = [
  // Today/Yesterday/Tomorrow
  { pattern: /\b(today|now)\b/i, offset: 0, unit: 'day' },
  { pattern: /\b(yesterday)\b/i, offset: -1, unit: 'day' },
  { pattern: /\b(tomorrow)\b/i, offset: 1, unit: 'day' },
  
  // Last/Next periods
  { pattern: /\b(last|past|previous)\s+(week|7\s*days?)\b/i, offset: -7, unit: 'day' },
  { pattern: /\b(next)\s+(week|7\s*days?)\b/i, offset: 7, unit: 'day' },
  { pattern: /\b(last|past|previous)\s+(month|30\s*days?)\b/i, offset: -1, unit: 'month' },
  { pattern: /\b(next)\s+(month|30\s*days?)\b/i, offset: 1, unit: 'month' },
  { pattern: /\b(last|past|previous)\s+(year|12\s*months?)\b/i, offset: -1, unit: 'year' },
  { pattern: /\b(next)\s+(year|12\s*months?)\b/i, offset: 1, unit: 'year' },
  
  // This period
  { pattern: /\b(this)\s+(week)\b/i, offset: 0, unit: 'week' },
  { pattern: /\b(this)\s+(month)\b/i, offset: 0, unit: 'month' },
  { pattern: /\b(this)\s+(year)\b/i, offset: 0, unit: 'year' },
  
  // Specific numbers
  { pattern: /\b(\d+)\s+(days?)\s+(ago)\b/i, offset: 'negative', unit: 'day' },
  { pattern: /\b(\d+)\s+(weeks?)\s+(ago)\b/i, offset: 'negative', unit: 'week' },
  { pattern: /\b(\d+)\s+(months?)\s+(ago)\b/i, offset: 'negative', unit: 'month' },
  { pattern: /\b(\d+)\s+(years?)\s+(ago)\b/i, offset: 'negative', unit: 'year' },
  
  // Days of week
  { pattern: /\b(monday|mon)\b/i, offset: 0, unit: 'weekday', day: 1 },
  { pattern: /\b(tuesday|tue)\b/i, offset: 0, unit: 'weekday', day: 2 },
  { pattern: /\b(wednesday|wed)\b/i, offset: 0, unit: 'weekday', day: 3 },
  { pattern: /\b(thursday|thu)\b/i, offset: 0, unit: 'weekday', day: 4 },
  { pattern: /\b(friday|fri)\b/i, offset: 0, unit: 'weekday', day: 5 },
  { pattern: /\b(saturday|sat)\b/i, offset: 0, unit: 'weekday', day: 6 },
  { pattern: /\b(sunday|sun)\b/i, offset: 0, unit: 'weekday', day: 0 },
  
  // Last/Next weekday
  { pattern: /\b(last|previous)\s+(monday|mon)\b/i, offset: -1, unit: 'weekday', day: 1 },
  { pattern: /\b(next)\s+(monday|mon)\b/i, offset: 1, unit: 'weekday', day: 1 },
  // ... (similar patterns for other weekdays)
];

// Date range patterns
const DATE_RANGE_PATTERNS = [
  { pattern: /\b(last|past|previous)\s+(\d+)\s+(days?)\b/i, type: 'days', multiplier: -1 },
  { pattern: /\b(next)\s+(\d+)\s+(days?)\b/i, type: 'days', multiplier: 1 },
  { pattern: /\b(last|past|previous)\s+(\d+)\s+(weeks?)\b/i, type: 'weeks', multiplier: -1 },
  { pattern: /\b(last|past|previous)\s+(\d+)\s+(months?)\b/i, type: 'months', multiplier: -1 },
  { pattern: /\b(last|past|previous)\s+(quarter)\b/i, type: 'quarter', multiplier: -1 },
  { pattern: /\b(this)\s+(quarter)\b/i, type: 'quarter', multiplier: 0 },
  { pattern: /\b(ytd|year\s+to\s+date)\b/i, type: 'ytd' },
  { pattern: /\b(mtd|month\s+to\s+date)\b/i, type: 'mtd' },
];

/**
 * Parse date from text input with multiple format support
 */
export const parseDate = (input, options = {}) => {
  const {
    preferredFormat = 'US', // 'US', 'EU', 'ISO'
    baseDate = new Date(),
    strict = false
  } = options;

  if (!input || typeof input !== 'string') return null;

  // Check cache first
  const cacheKey = `date:${input}:${preferredFormat}:${baseDate.toISOString()}`;
  const cached = textProcessingCache.get(cacheKey);
  if (cached) return cached;

  const normalizedInput = input.toLowerCase().trim();
  let result = null;

  // Try relative date parsing first
  result = parseRelativeDate(normalizedInput, baseDate);
  if (result) {
    textProcessingCache.set(cacheKey, result);
    return result;
  }

  // Try absolute date parsing
  result = parseAbsoluteDate(input, preferredFormat, baseDate);
  if (result) {
    textProcessingCache.set(cacheKey, result);
    return result;
  }

  // If strict mode, return null for unparseable dates
  if (strict) return null;

  // Fallback: try to extract any numbers that might be dates
  result = parseLooseDate(input, baseDate);
  textProcessingCache.set(cacheKey, result);
  return result;
};

/**
 * Parse relative dates (today, yesterday, last week, etc.)
 */
const parseRelativeDate = (input, baseDate) => {
  for (const { pattern, offset, unit, day } of RELATIVE_DATE_PATTERNS) {
    const match = input.match(pattern);
    if (match) {
      const date = new Date(baseDate);
      
      switch (unit) {
        case 'day':
          if (offset === 'negative') {
            const days = parseInt(match[1]);
            date.setDate(date.getDate() - days);
          } else {
            date.setDate(date.getDate() + offset);
          }
          break;
          
        case 'week':
          if (offset === 'negative') {
            const weeks = parseInt(match[1]);
            date.setDate(date.getDate() - (weeks * 7));
          } else {
            date.setDate(date.getDate() + (offset * 7));
          }
          break;
          
        case 'month':
          if (offset === 'negative') {
            const months = parseInt(match[1]);
            date.setMonth(date.getMonth() - months);
          } else {
            date.setMonth(date.getMonth() + offset);
          }
          break;
          
        case 'year':
          if (offset === 'negative') {
            const years = parseInt(match[1]);
            date.setFullYear(date.getFullYear() - years);
          } else {
            date.setFullYear(date.getFullYear() + offset);
          }
          break;
          
        case 'weekday':
          const currentDay = date.getDay();
          const targetDay = day;
          let daysToAdd = targetDay - currentDay;
          
          if (offset === -1) { // last weekday
            if (daysToAdd >= 0) daysToAdd -= 7;
          } else if (offset === 1) { // next weekday
            if (daysToAdd <= 0) daysToAdd += 7;
          }
          
          date.setDate(date.getDate() + daysToAdd);
          break;
      }
      
      return {
        date: date,
        type: 'relative',
        confidence: 0.9,
        original: match[0]  // Only the matched date portion, not the entire input
      };
    }
  }
  
  return null;
};

/**
 * Parse absolute dates with format detection
 */
const parseAbsoluteDate = (input, preferredFormat, baseDate) => {
  // Sort patterns by priority and preferred format
  const sortedPatterns = [...DATE_PATTERNS].sort((a, b) => {
    let scoreA = a.priority;
    let scoreB = b.priority;
    
    // Boost score for preferred format
    if (preferredFormat === 'US' && a.format.includes('MM/DD')) scoreA += 2;
    if (preferredFormat === 'EU' && a.format.includes('DD/MM')) scoreA += 2;
    if (preferredFormat === 'ISO' && a.format.includes('YYYY-MM-DD')) scoreA += 2;
    
    return scoreB - scoreA;
  });

  for (const { pattern, format } of sortedPatterns) {
    const match = input.match(pattern);
    if (match) {
      const date = parseDateFromMatch(match, format, baseDate);
      if (date && isValidDate(date)) {
        return {
          date: date,
          type: 'absolute',
          format: format,
          confidence: 0.8,
          original: match[0]  // Only the matched date portion
        };
      }
    }
  }
  
  return null;
};

/**
 * Parse date from regex match based on format
 */
const parseDateFromMatch = (match, format, baseDate) => {
  try {
    let year, month, day;
    
    switch (format) {
      case 'YYYY-MM-DD':
      case 'YYYY/MM/DD':
        year = parseInt(match[1]);
        month = parseInt(match[2]) - 1; // JS months are 0-based
        day = parseInt(match[3]);
        break;
        
      case 'MM/DD/YYYY':
      case 'MM-DD-YYYY':
        month = parseInt(match[1]) - 1;
        day = parseInt(match[2]);
        year = parseInt(match[3]);
        break;
        
      case 'DD/MM/YYYY':
      case 'DD.MM.YYYY':
        day = parseInt(match[1]);
        month = parseInt(match[2]) - 1;
        year = parseInt(match[3]);
        break;
        
      case 'MM/DD/YY':
      case 'MM-DD-YY':
        month = parseInt(match[1]) - 1;
        day = parseInt(match[2]);
        year = parseInt(match[3]);
        // Handle 2-digit years
        year = year < 50 ? 2000 + year : 1900 + year;
        break;
        
      case 'MONTH DD, YYYY':
        const monthName1 = match[1].toLowerCase();
        month = MONTH_NAMES[monthName1] - 1;
        day = parseInt(match[2]);
        year = parseInt(match[3]);
        break;
        
      case 'DD MONTH YYYY':
        day = parseInt(match[1]);
        const monthName2 = match[2].toLowerCase();
        month = MONTH_NAMES[monthName2] - 1;
        year = parseInt(match[3]);
        break;
        
      case 'MONTH DD':
        const monthName3 = match[1].toLowerCase();
        month = MONTH_NAMES[monthName3] - 1;
        day = parseInt(match[2]);
        year = baseDate.getFullYear(); // Use current year
        break;
        
      default:
        return null;
    }
    
    if (month === undefined || isNaN(month)) return null;
    
    return new Date(year, month, day);
  } catch (error) {
    return null;
  }
};

/**
 * Loose date parsing for fallback
 */
const parseLooseDate = (input, baseDate) => {
  // Extract any date-like numbers
  const numbers = input.match(/\d+/g);
  if (!numbers || numbers.length < 2) return null;
  
  // Try to construct a date from available numbers
  const nums = numbers.map(n => parseInt(n));
  
  // Simple heuristics for date construction
  let year = baseDate.getFullYear();
  let month = 0;
  let day = 1;
  
  if (nums.length >= 3) {
    // Assume first number > 31 is year
    const yearIndex = nums.findIndex(n => n > 31);
    if (yearIndex !== -1) {
      year = nums[yearIndex];
      const remaining = nums.filter((_, i) => i !== yearIndex);
      month = Math.min(remaining[0], 12) - 1;
      day = remaining[1] || 1;
    }
  } else if (nums.length === 2) {
    month = Math.min(nums[0], 12) - 1;
    day = nums[1];
  }
  
  const date = new Date(year, month, day);
  return isValidDate(date) ? {
    date: date,
    type: 'loose',
    confidence: 0.3,
    original: input
  } : null;
};

/**
 * Parse date ranges from text
 */
export const parseDateRange = (input, baseDate = new Date()) => {
  if (!input || typeof input !== 'string') return null;

  const cacheKey = `dateRange:${input}:${baseDate.toISOString()}`;
  const cached = textProcessingCache.get(cacheKey);
  if (cached) return cached;

  const normalizedInput = input.toLowerCase().trim();
  
  for (const { pattern, type, multiplier } of DATE_RANGE_PATTERNS) {
    const match = normalizedInput.match(pattern);
    if (match) {
      const result = calculateDateRange(type, multiplier, match, baseDate);
      if (result) {
        textProcessingCache.set(cacheKey, result);
        return result;
      }
    }
  }
  
  return null;
};

/**
 * Calculate date range based on type and parameters
 */
const calculateDateRange = (type, multiplier, match, baseDate) => {
  const endDate = new Date(baseDate);
  let startDate = new Date(baseDate);
  
  switch (type) {
    case 'days':
      const days = parseInt(match[2]) * multiplier;
      startDate.setDate(startDate.getDate() + days);
      break;
      
    case 'weeks':
      const weeks = parseInt(match[2]) * multiplier;
      startDate.setDate(startDate.getDate() + (weeks * 7));
      break;
      
    case 'months':
      const months = parseInt(match[2]) * multiplier;
      startDate.setMonth(startDate.getMonth() + months);
      break;
      
    case 'quarter':
      const currentQuarter = Math.floor(endDate.getMonth() / 3);
      const quarterStart = currentQuarter + multiplier;
      startDate = new Date(endDate.getFullYear(), quarterStart * 3, 1);
      endDate.setMonth((quarterStart + 1) * 3, 0); // Last day of quarter
      break;
      
    case 'ytd':
      startDate = new Date(endDate.getFullYear(), 0, 1);
      break;
      
    case 'mtd':
      startDate = new Date(endDate.getFullYear(), endDate.getMonth(), 1);
      break;
      
    default:
      return null;
  }
  
  // Ensure start is before end
  if (startDate > endDate) {
    [startDate, endDate] = [endDate, startDate];
  }
  
  return {
    startDate,
    endDate,
    type: 'range',
    confidence: 0.8,
    original: match[0]
  };
};

/**
 * Validate if date is reasonable
 */
const isValidDate = (date) => {
  if (!(date instanceof Date) || isNaN(date.getTime())) return false;
  
  // Check for reasonable date range (1900-2100)
  const year = date.getFullYear();
  return year >= 1900 && year <= 2100;
};

/**
 * Format date for display
 */
export const formatDateForDisplay = (date, format = 'short') => {
  if (!date || !isValidDate(date)) return '';
  
  const options = {
    short: { month: 'short', day: 'numeric', year: 'numeric' },
    long: { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' },
    iso: { year: 'numeric', month: '2-digit', day: '2-digit' }
  };
  
  if (format === 'iso') {
    return date.toISOString().split('T')[0];
  }
  
  return date.toLocaleDateString('en-US', options[format] || options.short);
};

export default {
  parseDate,
  parseDateRange,
  formatDateForDisplay
};