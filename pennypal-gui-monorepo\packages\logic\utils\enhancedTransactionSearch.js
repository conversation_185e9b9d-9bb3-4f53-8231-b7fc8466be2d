/**
 * Enhanced transaction search with intelligent merchant/date identification
 * Integrates fuzzy matching, natural language processing, and confidence scoring
 */

import { 
  normalizeText, 
  calculateSimilarity, 
  fuzzySearch, 
  calculateConfidence,
  createDebouncedSearch 
} from './textProcessing.js';

import { 
  processMerchantName, 
  searchMerchants, 
  getMerchantSuggestions 
} from './merchantProcessor.js';

import { 
  parseDate, 
  parseDateRange, 
  formatDateForDisplay 
} from './dateProcessor.js';

/**
 * Enhanced search query parser that extracts merchants, dates, and amounts
 */
export const parseSearchQuery = (query, options = {}) => {
  const {
    includeConfidence = true,
    strictDateParsing = false,
    preferredDateFormat = 'US'
  } = options;

  if (!query || typeof query !== 'string') {
    return {
      merchants: [],
      dateRange: null,
      amounts: [],
      categories: [],
      rawQuery: '',
      confidence: 0
    };
  }

  const result = {
    merchants: [],
    dateRange: null,
    amounts: [],
    categories: [],
    rawQuery: query.trim(),
    confidence: 0
  };

  // Extract date information first
  console.log('🔍 Starting date extraction for query:', query);
  const dateInfo = extractDateFromQuery(query, {
    strict: strictDateParsing,
    preferredFormat: preferredDateFormat
  });

  if (dateInfo) {
    console.log('✅ Date info extracted:', dateInfo);
    result.dateRange = dateInfo;
    // Remove date text from query for further processing
    const originalQuery = query;
    if (dateInfo.original) {
      query = query.replace(dateInfo.original, '').trim();
      console.log(`📅 Removed date "${dateInfo.original}" from query`);
      console.log(`📝 Query before: "${originalQuery}"`);
      console.log(`📝 Query after: "${query}"`);
    } else {
      console.log('⚠️ dateInfo.original is missing, cannot remove date text');
    }
  } else {
    console.log('❌ No date info extracted');
  }

  // Extract amount information
  const amountInfo = extractAmountFromQuery(query);
  if (amountInfo.length > 0) {
    result.amounts = amountInfo;
    // Remove amount text from query
    amountInfo.forEach(amount => {
      query = query.replace(amount.original || '', '').trim();
    });
  }

  // Extract category information
  const categoryInfo = extractCategoryFromQuery(query);
  if (categoryInfo.length > 0) {
    result.categories = categoryInfo;
    // Remove category text from query
    categoryInfo.forEach(cat => {
      query = query.replace(cat.original || '', '').trim();
    });
  }

  // Process remaining text as merchant names
  if (query.length > 0) {
    const merchantInfo = processMerchantName(query, { 
      includeAlternatives: true, 
      includeCategory: true 
    });
    
    if (merchantInfo.primary) {
      result.merchants.push({
        name: merchantInfo.primary,
        alternatives: merchantInfo.alternatives,
        category: merchantInfo.category,
        confidence: merchantInfo.confidence,
        original: query
      });
    }
  }

  // Calculate overall confidence
  result.confidence = calculateOverallConfidence(result);

  return result;
};

/**
 * Extract date information from search query
 */
const extractDateFromQuery = (query, options = {}) => {
  console.log('🗓️ Extracting date from query:', query);

  // Try date range parsing first
  const rangeResult = parseDateRange(query);
  if (rangeResult) {
    console.log('✅ Date range found:', rangeResult);
    return {
      type: 'range',
      startDate: rangeResult.startDate,
      endDate: rangeResult.endDate,
      confidence: rangeResult.confidence,
      original: rangeResult.original
    };
  }

  // Try single date parsing
  const dateResult = parseDate(query, options);
  if (dateResult) {
    console.log('✅ Single date found:', dateResult);
    return {
      type: 'single',
      date: dateResult.date,
      confidence: dateResult.confidence,
      original: dateResult.original
    };
  }

  console.log('❌ No date found in query');
  return null;
};

/**
 * Extract amount information from search query
 */
const extractAmountFromQuery = (query) => {
  const amounts = [];
  
  // Amount patterns
  const patterns = [
    // Currency symbols
    { pattern: /\$(\d+(?:\.\d{2})?)/g, currency: 'USD' },
    { pattern: /(\d+(?:\.\d{2})?)\s*(?:dollars?|usd)/gi, currency: 'USD' },
    { pattern: /€(\d+(?:\.\d{2})?)/g, currency: 'EUR' },
    { pattern: /£(\d+(?:\.\d{2})?)/g, currency: 'GBP' },
    
    // Range patterns
    { pattern: /between\s+\$?(\d+(?:\.\d{2})?)\s+and\s+\$?(\d+(?:\.\d{2})?)/gi, type: 'range' },
    { pattern: /from\s+\$?(\d+(?:\.\d{2})?)\s+to\s+\$?(\d+(?:\.\d{2})?)/gi, type: 'range' },
    
    // Comparison patterns
    { pattern: /(?:more|greater|above|over)\s+than\s+\$?(\d+(?:\.\d{2})?)/gi, type: 'greater' },
    { pattern: /(?:less|under|below)\s+than\s+\$?(\d+(?:\.\d{2})?)/gi, type: 'less' },
    { pattern: /(?:exactly|equal\s+to)\s+\$?(\d+(?:\.\d{2})?)/gi, type: 'exact' }
  ];

  patterns.forEach(({ pattern, currency = 'USD', type = 'exact' }) => {
    let match;
    while ((match = pattern.exec(query)) !== null) {
      if (type === 'range') {
        amounts.push({
          type: 'range',
          min: parseFloat(match[1]),
          max: parseFloat(match[2]),
          currency,
          original: match[0],
          confidence: 0.9
        });
      } else {
        amounts.push({
          type,
          value: parseFloat(match[1]),
          currency,
          original: match[0],
          confidence: 0.8
        });
      }
    }
  });

  return amounts;
};

/**
 * Extract category information from search query
 */
const extractCategoryFromQuery = (query) => {
  const categories = [];
  
  // Common category keywords
  const categoryKeywords = {
    'food': ['food', 'restaurant', 'dining', 'meal', 'lunch', 'dinner', 'breakfast'],
    'gas': ['gas', 'fuel', 'gasoline', 'petrol'],
    'grocery': ['grocery', 'groceries', 'supermarket', 'food shopping'],
    'entertainment': ['entertainment', 'movie', 'cinema', 'theater', 'concert'],
    'shopping': ['shopping', 'retail', 'store', 'purchase'],
    'transportation': ['transport', 'taxi', 'uber', 'lyft', 'bus', 'train'],
    'utilities': ['utilities', 'electric', 'water', 'internet', 'phone'],
    'healthcare': ['medical', 'doctor', 'hospital', 'pharmacy', 'health'],
    'education': ['education', 'school', 'university', 'tuition', 'books']
  };

  const normalizedQuery = normalizeText(query);
  
  Object.entries(categoryKeywords).forEach(([category, keywords]) => {
    keywords.forEach(keyword => {
      if (normalizedQuery.includes(keyword)) {
        categories.push({
          category,
          keyword,
          confidence: 0.7,
          original: keyword
        });
      }
    });
  });

  return categories;
};

/**
 * Calculate overall confidence for parsed query
 */
const calculateOverallConfidence = (parsedQuery) => {
  let totalConfidence = 0;
  let componentCount = 0;

  // Merchant confidence
  if (parsedQuery.merchants.length > 0) {
    const avgMerchantConfidence = parsedQuery.merchants.reduce(
      (sum, m) => sum + m.confidence, 0
    ) / parsedQuery.merchants.length;
    totalConfidence += avgMerchantConfidence;
    componentCount++;
  }

  // Date confidence
  if (parsedQuery.dateRange) {
    totalConfidence += parsedQuery.dateRange.confidence;
    componentCount++;
  }

  // Amount confidence
  if (parsedQuery.amounts.length > 0) {
    const avgAmountConfidence = parsedQuery.amounts.reduce(
      (sum, a) => sum + a.confidence, 0
    ) / parsedQuery.amounts.length;
    totalConfidence += avgAmountConfidence;
    componentCount++;
  }

  // Category confidence
  if (parsedQuery.categories.length > 0) {
    const avgCategoryConfidence = parsedQuery.categories.reduce(
      (sum, c) => sum + c.confidence, 0
    ) / parsedQuery.categories.length;
    totalConfidence += avgCategoryConfidence;
    componentCount++;
  }

  return componentCount > 0 ? totalConfidence / componentCount : 0;
};

/**
 * Enhanced transaction search with intelligent filtering
 */
export const searchTransactions = (query, transactions, options = {}) => {
  const {
    threshold = 0.3,
    limit = 100,
    includeScore = false,
    sortBy = 'relevance', // 'relevance', 'date', 'amount'
    sortOrder = 'desc'
  } = options;

  if (!query || !Array.isArray(transactions)) return [];

  // Parse the search query
  const parsedQuery = parseSearchQuery(query, options);
  
  if (parsedQuery.confidence < 0.1) {
    // Fallback to simple text search
    return fuzzySearch(query, transactions, {
      keys: ['name', 'description'],
      threshold,
      limit,
      includeScore
    });
  }

  // Filter transactions based on parsed criteria
  let filteredTransactions = [...transactions];

  // Apply date filters
  if (parsedQuery.dateRange) {
    filteredTransactions = filterByDate(filteredTransactions, parsedQuery.dateRange);
  }

  // Apply amount filters
  if (parsedQuery.amounts.length > 0) {
    filteredTransactions = filterByAmount(filteredTransactions, parsedQuery.amounts);
  }

  // Apply category filters
  if (parsedQuery.categories.length > 0) {
    filteredTransactions = filterByCategory(filteredTransactions, parsedQuery.categories);
  }

  // Apply merchant filters with fuzzy matching
  if (parsedQuery.merchants.length > 0) {
    filteredTransactions = filterByMerchant(filteredTransactions, parsedQuery.merchants, threshold);
  }

  // Calculate relevance scores
  const scoredTransactions = filteredTransactions.map(transaction => {
    const score = calculateTransactionRelevance(transaction, parsedQuery);
    return { transaction, score };
  });

  // Sort results
  scoredTransactions.sort((a, b) => {
    switch (sortBy) {
      case 'date':
        const dateA = new Date(a.transaction.transactionDate || a.transaction.date);
        const dateB = new Date(b.transaction.transactionDate || b.transaction.date);
        return sortOrder === 'desc' ? dateB - dateA : dateA - dateB;
      
      case 'amount':
        const amountA = Math.abs(parseFloat(a.transaction.amount) || 0);
        const amountB = Math.abs(parseFloat(b.transaction.amount) || 0);
        return sortOrder === 'desc' ? amountB - amountA : amountA - amountB;
      
      case 'relevance':
      default:
        return sortOrder === 'desc' ? b.score - a.score : a.score - b.score;
    }
  });

  // Return results
  const results = scoredTransactions
    .slice(0, limit)
    .map(({ transaction, score }) => 
      includeScore ? { item: transaction, score } : { item: transaction }
    );

  return results;
};

/**
 * Filter transactions by date range
 */
const filterByDate = (transactions, dateRange) => {
  return transactions.filter(transaction => {
    const txDate = new Date(transaction.transactionDate || transaction.date);
    if (isNaN(txDate.getTime())) return false;

    if (dateRange.type === 'range') {
      return txDate >= dateRange.startDate && txDate <= dateRange.endDate;
    } else if (dateRange.type === 'single') {
      const targetDate = dateRange.date;
      return txDate.toDateString() === targetDate.toDateString();
    }

    return true;
  });
};

/**
 * Filter transactions by amount criteria
 */
const filterByAmount = (transactions, amounts) => {
  return transactions.filter(transaction => {
    const txAmount = Math.abs(parseFloat(transaction.amount) || 0);
    
    return amounts.some(amountCriteria => {
      switch (amountCriteria.type) {
        case 'exact':
          return Math.abs(txAmount - amountCriteria.value) < 0.01;
        case 'range':
          return txAmount >= amountCriteria.min && txAmount <= amountCriteria.max;
        case 'greater':
          return txAmount > amountCriteria.value;
        case 'less':
          return txAmount < amountCriteria.value;
        default:
          return true;
      }
    });
  });
};

/**
 * Filter transactions by category
 */
const filterByCategory = (transactions, categories) => {
  return transactions.filter(transaction => {
    const txCategory = normalizeText(transaction.category || transaction.subcategory || '');
    
    return categories.some(categoryInfo => {
      return txCategory.includes(categoryInfo.category) ||
             calculateSimilarity(txCategory, categoryInfo.category) > 0.7;
    });
  });
};

/**
 * Filter transactions by merchant with fuzzy matching
 */
const filterByMerchant = (transactions, merchants, threshold) => {
  return transactions.filter(transaction => {
    const txName = transaction.name || transaction.description || '';
    
    return merchants.some(merchantInfo => {
      // Check primary name
      if (calculateSimilarity(normalizeText(txName), merchantInfo.name) >= threshold) {
        return true;
      }
      
      // Check alternatives
      return merchantInfo.alternatives.some(alt => 
        calculateSimilarity(normalizeText(txName), alt) >= threshold
      );
    });
  });
};

/**
 * Calculate transaction relevance score
 */
const calculateTransactionRelevance = (transaction, parsedQuery) => {
  let score = 0;
  let factors = 0;

  // Merchant relevance
  if (parsedQuery.merchants.length > 0) {
    const txName = normalizeText(transaction.name || transaction.description || '');
    const merchantScore = Math.max(...parsedQuery.merchants.map(merchant => {
      const primaryScore = calculateSimilarity(txName, merchant.name);
      const altScore = Math.max(0, ...merchant.alternatives.map(alt => 
        calculateSimilarity(txName, alt)
      ));
      return Math.max(primaryScore, altScore);
    }));
    score += merchantScore * 0.4;
    factors++;
  }

  // Date relevance (more recent = higher score)
  if (parsedQuery.dateRange) {
    const txDate = new Date(transaction.transactionDate || transaction.date);
    const now = new Date();
    const daysDiff = Math.abs((now - txDate) / (1000 * 60 * 60 * 24));
    const dateScore = Math.max(0, 1 - (daysDiff / 365)); // Decay over a year
    score += dateScore * 0.2;
    factors++;
  }

  // Amount relevance
  if (parsedQuery.amounts.length > 0) {
    const txAmount = Math.abs(parseFloat(transaction.amount) || 0);
    const amountScore = parsedQuery.amounts.some(amt => {
      if (amt.type === 'exact') return Math.abs(txAmount - amt.value) < 0.01;
      if (amt.type === 'range') return txAmount >= amt.min && txAmount <= amt.max;
      return true;
    }) ? 1 : 0;
    score += amountScore * 0.2;
    factors++;
  }

  // Category relevance
  if (parsedQuery.categories.length > 0) {
    const txCategory = normalizeText(transaction.category || transaction.subcategory || '');
    const categoryScore = Math.max(...parsedQuery.categories.map(cat => 
      calculateSimilarity(txCategory, cat.category)
    ));
    score += categoryScore * 0.2;
    factors++;
  }

  return factors > 0 ? score / factors : 0;
};

// Create debounced version for real-time search
export const debouncedSearchTransactions = createDebouncedSearch(searchTransactions, 300);

export default {
  parseSearchQuery,
  searchTransactions,
  debouncedSearchTransactions
};