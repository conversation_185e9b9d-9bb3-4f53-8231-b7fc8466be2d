/**
 * Advanced merchant name processing and normalization
 * Handles fuzzy matching, alias resolution, and pattern recognition
 */

import { 
  normalizeText, 
  calculateSimilarity, 
  fuzzySearch, 
  extractMerchantNames,
  calculateConfidence,
  textProcessingCache 
} from './textProcessing.js';

// Common merchant aliases and variations
const MERCHANT_ALIASES = {
  'amazon': ['amzn', 'amazon.com', 'amazon prime', 'amazon web services', 'aws'],
  'starbucks': ['sbux', 'starbuck', 'starbucks coffee', 'starbucks corp'],
  'mcdonalds': ['mcd', 'mcdonald', 'mcdonalds corp', 'golden arches'],
  'kfc': ['kentucky fried chicken', 'kfc corp', 'kentucky fried'],
  'walmart': ['wal-mart', 'walmart inc', 'walmart stores', 'wmt'],
  'target': ['target corp', 'target stores', 'tgt'],
  'apple': ['apple inc', 'apple store', 'apple.com', 'itunes'],
  'google': ['google inc', 'google llc', 'google.com', 'alphabet'],
  'microsoft': ['msft', 'microsoft corp', 'microsoft store'],
  'netflix': ['netflix inc', 'netflix.com'],
  'spotify': ['spotify ab', 'spotify premium'],
  'uber': ['uber technologies', 'uber eats'],
  'lyft': ['lyft inc'],
  'airbnb': ['airbnb inc', 'air bnb'],
  'paypal': ['paypal inc', 'paypal holdings'],
  'venmo': ['venmo llc'],
  'square': ['square inc', 'sq'],
  'stripe': ['stripe inc'],
  'shopify': ['shopify inc'],
  'etsy': ['etsy inc'],
  'ebay': ['ebay inc'],
  'costco': ['costco wholesale', 'costco wholesale corp'],
  'home depot': ['home depot inc', 'homedepot'],
  'lowes': ['lowe\'s', 'lowes companies'],
  'best buy': ['bestbuy', 'best buy co'],
  'cvs': ['cvs health', 'cvs pharmacy'],
  'walgreens': ['walgreens boots alliance', 'wag'],
  'shell': ['shell oil', 'shell gas'],
  'exxon': ['exxonmobil', 'exxon mobil'],
  'chevron': ['chevron corp'],
  'bp': ['british petroleum'],
  'mobil': ['mobil gas', 'exxonmobil']
};

// Common merchant categories for context
const MERCHANT_CATEGORIES = {
  'grocery': ['kroger', 'safeway', 'whole foods', 'trader joes', 'publix', 'wegmans'],
  'restaurant': ['mcdonalds', 'burger king', 'subway', 'chipotle', 'panera', 'kfc'],
  'coffee': ['starbucks', 'dunkin', 'peets', 'caribou'],
  'gas': ['shell', 'exxon', 'chevron', 'bp', 'mobil', 'texaco'],
  'retail': ['walmart', 'target', 'costco', 'amazon', 'best buy'],
  'pharmacy': ['cvs', 'walgreens', 'rite aid'],
  'technology': ['apple', 'microsoft', 'google', 'amazon'],
  'streaming': ['netflix', 'spotify', 'hulu', 'disney+'],
  'transportation': ['uber', 'lyft', 'taxi'],
  'financial': ['paypal', 'venmo', 'square', 'stripe']
};

// Merchant name patterns for different transaction types
const MERCHANT_PATTERNS = [
  // Natural language query patterns (highest priority)
  {
    pattern: /\b(?:show|find|search)\s+(?:me\s+)?(?:my\s+)?([a-zA-Z\s&'-]+?)(?:\s+transactions?|\s+purchases?|\s+payments?|$)/i,
    type: 'natural_language',
    confidence: 0.9,
    excludePattern: /\b(?:yesterday|today|tomorrow|last|past|previous|next|this|week|month|year|day|quarter|ago)\b/i
  },
  {
    pattern: /\b([a-zA-Z\s&'-]+?)\s+(?:transactions?|purchases?|payments?)\b/i,
    type: 'natural_language',
    confidence: 0.8,
    excludePattern: /\b(?:yesterday|today|tomorrow|last|past|previous|next|this|week|month|year|day|quarter|ago)\b/i
  },
  {
    pattern: /\btransactions?\s+(?:for|from|at)\s+([a-zA-Z\s&'-]+?)(?:\s|$)/i,
    type: 'natural_language',
    confidence: 0.8,
    excludePattern: /\b(?:yesterday|today|tomorrow|last|past|previous|next|this|week|month|year|day|quarter|ago)\b/i
  },
  // Standard transaction formats
  {
    pattern: /^([A-Z\s&'-]+?)(?:\s+\d{2,4}|\.COM|\.NET|\.ORG|\*|POS)/i,
    type: 'transaction',
    confidence: 0.7
  }
];

/**
 * Clean up merchant names extracted from natural language queries
 */
const cleanupNaturalLanguageMerchant = (merchantName) => {
  if (!merchantName) return '';

  const wordsToRemove = ['my', 'the', 'all', 'show', 'find', 'search', 'me', 'transactions', 'transaction', 'from', 'at', 'for'];
  const words = merchantName.toLowerCase().trim().split(/\s+/);
  const filteredWords = words.filter(word => !wordsToRemove.includes(word));

  return filteredWords.length > 0 ? filteredWords.join(' ').trim() : merchantName.trim();
};

/**
 * Extract and normalize merchant name from transaction description
 */
export const processMerchantName = (description, options = {}) => {
  const {
    includeAlternatives = true,
    includeCategory = true
  } = options;

  if (!description || typeof description !== 'string') {
    return {
      primary: null,
      alternatives: [],
      category: null,
      confidence: 0,
      pattern: null
    };
  }

  // Early check: if the description is purely a date expression, don't process as merchant
  const purelyDatePattern = /^\s*(?:yesterday|today|tomorrow|last|past|previous|next|this)\s+(?:week|month|year|day|quarter|monday|tuesday|wednesday|thursday|friday|saturday|sunday)\s*$/i;
  if (purelyDatePattern.test(description)) {
    console.log(`📅 Skipping merchant processing for pure date expression: "${description}"`);
    return {
      primary: null,
      alternatives: [],
      category: null,
      confidence: 0,
      pattern: 'date_expression'
    };
  }

  // Check cache first
  const cacheKey = `merchant:${description}:${JSON.stringify(options)}`;
  const cached = textProcessingCache.get(cacheKey);
  if (cached) return cached;

  const result = {
    primary: null,
    alternatives: [],
    category: null,
    confidence: 0,
    pattern: null
  };

  // Try pattern matching first
  for (const { pattern, type, confidence, excludePattern } of MERCHANT_PATTERNS) {
    const match = description.match(pattern);
    if (match && match[1]) {
      let merchantName = normalizeText(match[1]);

      // Check if the matched text should be excluded (e.g., date terms)
      if (excludePattern && excludePattern.test(merchantName)) {
        console.log(`🚫 Excluding "${merchantName}" as it matches date pattern`);
        continue;
      }

      // Additional cleanup for natural language queries
      if (type === 'natural_language') {
        merchantName = cleanupNaturalLanguageMerchant(merchantName);
      }

      if (merchantName.length >= 2) {
        result.primary = merchantName;
        result.confidence = confidence;
        result.pattern = type;
        break;
      }
    }
  }

  // Fallback to general extraction
  if (!result.primary) {
    const extracted = extractMerchantNames(description);
    if (extracted.length > 0) {
      result.primary = extracted[0];
      result.confidence = 0.5;
      result.pattern = 'general';
    }
  }

  if (result.primary) {
    // Find aliases and alternatives
    if (includeAlternatives) {
      result.alternatives = findMerchantAliases(result.primary);
    }

    // Determine category
    if (includeCategory) {
      result.category = determineMerchantCategory(result.primary);
    }

    // Enhance confidence based on known merchants
    if (isKnownMerchant(result.primary)) {
      result.confidence = Math.min(1, result.confidence + 0.2);
    }
  }

  // Cache the result
  textProcessingCache.set(cacheKey, result);
  return result;
};

/**
 * Find merchant aliases and variations
 */
export const findMerchantAliases = (merchantName) => {
  const normalized = normalizeText(merchantName);
  const aliases = new Set();

  // Check direct aliases
  for (const [canonical, variations] of Object.entries(MERCHANT_ALIASES)) {
    if (normalized.includes(canonical) || variations.some(v => normalized.includes(v))) {
      aliases.add(canonical);
      variations.forEach(v => aliases.add(v));
    }
  }

  // Fuzzy matching against known merchants
  const knownMerchants = Object.keys(MERCHANT_ALIASES);
  const fuzzyMatches = fuzzySearch(normalized, knownMerchants, {
    threshold: 0.7,
    limit: 3,
    includeScore: true
  });

  fuzzyMatches.forEach(({ item, score }) => {
    if (score > 0.7) {
      aliases.add(item);
      MERCHANT_ALIASES[item]?.forEach(alias => aliases.add(alias));
    }
  });

  return Array.from(aliases).filter(alias => alias !== normalized);
};

/**
 * Determine merchant category
 */
export const determineMerchantCategory = (merchantName) => {
  const normalized = normalizeText(merchantName);

  for (const [category, merchants] of Object.entries(MERCHANT_CATEGORIES)) {
    if (merchants.some(merchant => 
      normalized.includes(merchant) || 
      calculateSimilarity(normalized, merchant) > 0.8
    )) {
      return category;
    }
  }

  return 'other';
};

/**
 * Check if merchant is in known database
 */
export const isKnownMerchant = (merchantName) => {
  const normalized = normalizeText(merchantName);
  
  // Check against canonical names
  if (Object.keys(MERCHANT_ALIASES).some(canonical => 
    normalized.includes(canonical) || calculateSimilarity(normalized, canonical) > 0.8
  )) {
    return true;
  }

  // Check against aliases
  return Object.values(MERCHANT_ALIASES).some(aliases =>
    aliases.some(alias => 
      normalized.includes(alias) || calculateSimilarity(normalized, alias) > 0.8
    )
  );
};

/**
 * Fuzzy search for merchants in transaction list
 */
export const searchMerchants = (query, transactions, options = {}) => {
  const {
    threshold = 0.3,
    limit = 50,
    includeScore = false,
    searchFields = ['name', 'description']
  } = options;

  if (!query || !Array.isArray(transactions)) return [];

  const processedQuery = processMerchantName(query);
  const searchTerms = [
    processedQuery.primary,
    ...processedQuery.alternatives
  ].filter(Boolean);

  const results = new Map(); // Use Map to avoid duplicates

  // Search with each term
  searchTerms.forEach(term => {
    const matches = fuzzySearch(term, transactions, {
      keys: searchFields,
      threshold,
      limit: limit * 2, // Get more to filter later
      includeScore: true
    });

    matches.forEach(({ item, score, index }) => {
      const existingScore = results.get(index)?.score || 0;
      if (score > existingScore) {
        results.set(index, { item, score, index });
      }
    });
  });

  // Convert to array and sort
  let finalResults = Array.from(results.values())
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);

  if (!includeScore) {
    finalResults = finalResults.map(({ item, index }) => ({ item, index }));
  }

  return finalResults;
};

/**
 * Normalize merchant name for consistent storage
 */
export const normalizeMerchantForStorage = (merchantName) => {
  if (!merchantName) return '';

  const processed = processMerchantName(merchantName);
  
  // Use canonical name if available
  const canonical = Object.keys(MERCHANT_ALIASES).find(key =>
    processed.alternatives.includes(key) || 
    calculateSimilarity(processed.primary, key) > 0.8
  );

  return canonical || processed.primary || normalizeText(merchantName);
};

/**
 * Get merchant suggestions for autocomplete
 */
export const getMerchantSuggestions = (query, recentMerchants = [], options = {}) => {
  const { limit = 10 } = options;
  
  if (!query || query.length < 2) return [];

  const suggestions = new Set();
  
  // Add exact matches from recent merchants first
  recentMerchants.forEach(merchant => {
    if (normalizeText(merchant).includes(normalizeText(query))) {
      suggestions.add(merchant);
    }
  });

  // Add fuzzy matches from known merchants
  const knownMerchants = Object.keys(MERCHANT_ALIASES);
  const fuzzyMatches = fuzzySearch(query, knownMerchants, {
    threshold: 0.4,
    limit: limit - suggestions.size,
    includeScore: false
  });

  fuzzyMatches.forEach(({ item }) => suggestions.add(item));

  return Array.from(suggestions).slice(0, limit);
};

export default {
  processMerchantName,
  findMerchantAliases,
  determineMerchantCategory,
  isKnownMerchant,
  searchMerchants,
  normalizeMerchantForStorage,
  getMerchantSuggestions
};