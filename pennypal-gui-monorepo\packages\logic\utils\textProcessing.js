/**
 * Enterprise-grade text processing utilities for merchant/date identification
 * Provides fuzzy matching, normalization, and pattern recognition capabilities
 */

// String normalization utilities
export const normalizeText = (text) => {
  if (!text || typeof text !== 'string') return '';
  
  return text
    .toLowerCase()
    .trim()
    // Remove extra whitespace
    .replace(/\s+/g, ' ')
    // Remove common punctuation that doesn't affect meaning
    .replace(/[.,;:!?'"()[\]{}]/g, '')
    // Normalize common abbreviations
    .replace(/\b(inc|llc|ltd|corp|co)\b/g, '')
    // Remove leading/trailing articles
    .replace(/^(the|a|an)\s+/g, '')
    .replace(/\s+(the|a|an)$/g, '')
    .trim();
};

// Simplified string similarity using Levenshtein distance
export const calculateSimilarity = (str1, str2, options = {}) => {
  const { caseSensitive = false } = options;

  if (!str1 || !str2) return 0;

  const s1 = caseSensitive ? str1 : str1.toLowerCase();
  const s2 = caseSensitive ? str2 : str2.toLowerCase();

  return levenshteinSimilarity(s1, s2);
};

// Levenshtein distance similarity (0-1 scale)
const levenshteinSimilarity = (str1, str2) => {
  const distance = levenshteinDistance(str1, str2);
  const maxLength = Math.max(str1.length, str2.length);
  return maxLength === 0 ? 1 : 1 - (distance / maxLength);
};

// Levenshtein distance calculation
const levenshteinDistance = (str1, str2) => {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

  for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
  for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1,     // deletion
        matrix[j - 1][i] + 1,     // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      );
    }
  }

  return matrix[str2.length][str1.length];
};



// Fuzzy search in array of strings
export const fuzzySearch = (query, items, options = {}) => {
  const {
    keys = null,           // For object arrays, specify which keys to search
    threshold = 0.3,       // Minimum similarity score
    limit = 10,           // Maximum results to return
    includeScore = false  // Include similarity scores in results
  } = options;

  const normalizedQuery = normalizeText(query);
  const results = [];

  items.forEach((item, index) => {
    let searchText = '';
    
    if (typeof item === 'string') {
      searchText = item;
    } else if (keys && Array.isArray(keys)) {
      searchText = keys.map(key => item[key] || '').join(' ');
    } else if (typeof item === 'object') {
      searchText = Object.values(item).join(' ');
    }

    const normalizedText = normalizeText(searchText);
    const similarity = calculateSimilarity(normalizedQuery, normalizedText);

    if (similarity >= threshold) {
      const result = includeScore 
        ? { item, score: similarity, index }
        : { item, index };
      results.push(result);
    }
  });

  // Sort by similarity score (highest first)
  results.sort((a, b) => (b.score || 0) - (a.score || 0));

  return limit > 0 ? results.slice(0, limit) : results;
};

// Extract potential merchant names from transaction descriptions
export const extractMerchantNames = (description) => {
  if (!description || typeof description !== 'string') return [];

  const normalized = normalizeText(description);
  const merchants = [];

  // Common patterns for merchant extraction
  const patterns = [
    // Standard merchant format: "MERCHANT NAME LOCATION"
    /^([a-zA-Z\s&'-]+?)(?:\s+\d{2,4}|\s+[A-Z]{2,3}\s*\d|\s*$)/,
    // Card transaction format: "MERCHANT*LOCATION"
    /^([^*]+)\*/,
    // Online transaction: "MERCHANT.COM" or "MERCHANT ONLINE"
    /^([a-zA-Z\s&'-]+?)(?:\.com|\.net|\.org|\s+online)/i,
    // Generic cleanup: remove trailing numbers, codes
    /^([a-zA-Z\s&'-]+?)(?:\s+\d+|\s+[A-Z0-9]{3,}|\s*$)/
  ];

  for (const pattern of patterns) {
    const match = description.match(pattern);
    if (match && match[1]) {
      const merchant = normalizeText(match[1]);
      if (merchant.length >= 2) {
        merchants.push(merchant);
      }
    }
  }

  return [...new Set(merchants)]; // Remove duplicates
};

// Confidence scoring for text matches
export const calculateConfidence = (query, match, context = {}) => {
  const {
    exactMatch = false,
    partialMatch = false,
    fuzzyScore = 0,
    contextRelevance = 0
  } = context;

  let confidence = 0;

  // Base score from similarity
  confidence += fuzzyScore * 0.6;

  // Bonus for exact matches
  if (exactMatch) {
    confidence += 0.3;
  } else if (partialMatch) {
    confidence += 0.15;
  }

  // Context relevance (e.g., category match, frequency)
  confidence += contextRelevance * 0.1;

  // Length penalty for very short matches
  if (match.length < 3) {
    confidence *= 0.7;
  }

  return Math.min(1, Math.max(0, confidence));
};

// Debounced search function for performance
export const createDebouncedSearch = (searchFunction, delay = 300) => {
  let timeoutId;
  
  return (...args) => {
    clearTimeout(timeoutId);
    return new Promise((resolve) => {
      timeoutId = setTimeout(() => {
        resolve(searchFunction(...args));
      }, delay);
    });
  };
};

// Performance monitoring utilities
export const measurePerformance = (fn, label = 'operation') => {
  return (...args) => {
    const start = performance.now();
    const result = fn(...args);
    const end = performance.now();
    
    console.log(`${label} took ${(end - start).toFixed(2)}ms`);
    return result;
  };
};

// Cache implementation for processed text
class TextProcessingCache {
  constructor(maxSize = 1000, ttl = 300000) { // 5 minutes TTL
    this.cache = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.value;
  }

  set(key, value) {
    if (this.cache.size >= this.maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
  }

  clear() {
    this.cache.clear();
  }
}

export const textProcessingCache = new TextProcessingCache();