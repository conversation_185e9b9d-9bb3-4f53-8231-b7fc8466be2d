/**
 * Simplified integration layer for enhanced transaction search
 */

import {
  searchTransactions,
  parseSearchQuery,
  debouncedSearchTransactions
} from './enhancedTransactionSearch.js';

import { textProcessingCache } from './textProcessing.js';

// Simple cache for search results
const searchCache = new Map();
const CACHE_TTL = 300000; // 5 minutes

const getCachedResult = (key) => {
  const cached = searchCache.get(key);
  if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
    return cached.data;
  }
  searchCache.delete(key);
  return null;
};

const setCachedResult = (key, data) => {
  // Simple cache size limit
  if (searchCache.size > 100) {
    const firstKey = searchCache.keys().next().value;
    searchCache.delete(firstKey);
  }
  searchCache.set(key, { data, timestamp: Date.now() });
};

/**
 * Simple cached search function
 */
export const cachedSearchTransactions = (query, transactions, options = {}) => {
  const cacheKey = `${query}:${transactions.length}:${JSON.stringify(options)}`;

  // Check cache first
  const cachedResult = getCachedResult(cacheKey);
  if (cachedResult) {
    return cachedResult;
  }

  // Perform search
  const results = searchTransactions(query, transactions, options);

  // Cache results
  setCachedResult(cacheKey, results);

  return results;
};

/**
 * Simple search function for Redux integration
 */
export const optimizedTransactionSearch = (query, allTransactions, options = {}) => {
  const { maxResults = 100, ...searchOptions } = options;

  return cachedSearchTransactions(query, allTransactions, {
    limit: maxResults,
    ...searchOptions
  });
};

/**
 * Simple search suggestions
 */
export const getSearchSuggestions = (query, recentSearches = [], options = {}) => {
  const { limit = 5 } = options;

  if (!query || query.length < 2) return [];

  const suggestions = new Set();

  // Add basic suggestions based on query content
  if (query.toLowerCase().includes('last') || query.toLowerCase().includes('past')) {
    suggestions.add('last 7 days');
    suggestions.add('last month');
  }

  if (query.includes('$')) {
    suggestions.add('over $50');
    suggestions.add('under $20');
  }

  // Add recent searches that match
  recentSearches.slice(0, 3).forEach(search => {
    if (search.toLowerCase().includes(query.toLowerCase())) {
      suggestions.add(search);
    }
  });

  return Array.from(suggestions).slice(0, limit);
};

/**
 * Clear all caches (useful for logout or data refresh)
 */
export const clearAllCaches = () => {
  searchCache.clear();
  textProcessingCache.clear();
};

export default {
  cachedSearchTransactions,
  optimizedTransactionSearch,
  getSearchSuggestions,
  clearAllCaches
};