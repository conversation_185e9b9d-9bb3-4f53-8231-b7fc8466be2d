{"name": "web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development", "dev:prod": "vite --mode production", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:watch": "vitest --watch", "test:coverage": "vitest run --coverage", "test:coverage:ui": "vitest --ui --coverage"}, "dependencies": {"@date-io/date-fns": "^3.2.1", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@iconscout/react-unicons": "^2.2.4", "@mui/icons-material": "^6.4.7", "@mui/material": "^6.5.0", "@mui/x-data-grid": "^7.24.0", "@mui/x-date-pickers": "^8.4.0", "@react-oauth/google": "^0.12.2", "@reduxjs/toolkit": "^2.5.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "axios": "^1.7.9", "canvg": "^4.0.3", "chart.js": "^4.4.7", "cra-template": "1.2.0", "date-fns": "^4.1.0", "framer-motion": "^12.12.1", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.475.0", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-calendar": "^6.0.0", "react-chartjs-2": "^5.3.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-modal": "^3.16.3", "react-phone-input-2": "^2.15.1", "react-plaid-link": "^3.6.1", "react-redux": "^9.2.0", "react-router-dom": "^7.1.5", "react-scripts": "5.0.1", "recharts": "^2.15.0", "redux-observable": "^3.0.0-rc.2", "rxjs": "^7.8.1", "simple-icons": "^14.12.1", "uuid": "^11.1.0", "web-vitals": "^4.2.4", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.17.0", "@tailwindcss/vite": "^4.0.14", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.2.3", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "jsdom": "^26.1.0", "vite": "^6.3.5", "vitest": "^3.2.3"}}