import { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faTimes,
  faArrowUp,
  faRobot,
  faThumbsUp,
  faThumbsDown,
  faCopy
} from "@fortawesome/free-solid-svg-icons";
import { getCurrentUserId } from '@pp-web/utils/AuthUtil';
import { logEvent } from '@pp-web/utils/EventLogger';
import { fetchHistoryRequest, queryRequest } from '@pp-logic/redux/chatbotSlice';

const GenericAIChat = ({ 
  darkMode, 
  currentTheme, 
  setShowAIChat, 
  showAIChat, 
  title = "AI Assistant",
  subtitle = "Ask me anything",
  welcomeMessage = "👋 Hi! I'm your AI assistant. How can I help you today?",
  exampleQueries = [
    "Help me understand my data",
    "Show me insights",
    "Analyze my information",
    "Give me tips"
  ],
  placeholder = "Ask me anything...",
  onQuerySent = null // Optional callback for when a query is sent
}) => {
  const colors = currentTheme?.colors || {};

  // Early return if theme is not loaded
  if (!currentTheme?.colors) {
    return null;
  }

  const userId = getCurrentUserId();
  const [chatHistory, setChatHistory] = useState([]);
  const [newQuery, setNewQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState(null);
  const chatContainerRef = useRef(null);
  const chatEndRef = useRef(null);
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(true);
  const cache = useSelector(state => state.cache);
  const chatbotState = useSelector(state => state.chatbot);
  const dispatch = useDispatch();

  useEffect(() => {
    if (cache?.chatbotHistoryLoaded && cache?.chatbotHistory?.length >= 0 &&
        cache?.chatbotHistoryParams?.userId === userId) {
      const flatHistory = cache.chatbotHistory.flatMap(log => {
        const parsed = tryParseJSON(log.response);
        return [
          { type: 'user', text: log.userQuery },
          { type: 'ai', text: parsed?.summary || log.response, chatId: log.id }
        ];
      });
      setChatHistory(flatHistory);
    } else {
      dispatch(fetchHistoryRequest({ userId }));
    }
  }, [dispatch, userId, cache?.chatbotHistoryLoaded]);

  useEffect(() => {
    if (cache?.chatbotHistoryLoaded && cache?.chatbotHistory) {
      const flatHistory = cache.chatbotHistory.flatMap(log => {
        const parsed = tryParseJSON(log.response);
        return [
          { type: 'user', text: log.userQuery },
          { type: 'ai', text: parsed?.summary || log.response, chatId: log.id }
        ];
      });
      setChatHistory(flatHistory);
      setIsLoading(false);
    }
  }, [cache?.chatbotHistory, cache?.chatbotHistoryLoaded]);

  // Listen for cache updates after new queries
  useEffect(() => {
    if (cache?.chatbotHistoryLoaded && cache?.chatbotHistory) {
      const flatHistory = cache.chatbotHistory.flatMap(log => {
        const parsed = tryParseJSON(log.response);
        return [
          { type: 'user', text: log.userQuery },
          { type: 'ai', text: parsed?.summary || log.response, chatId: log.id }
        ];
      });
      setChatHistory(flatHistory);
      setIsLoading(false);
    }
  }, [cache?.chatbotHistory?.length, cache?.chatbotHistoryLoaded]);

  // Listen for chatbot query completion to stop loading
  useEffect(() => {
    if (!chatbotState.querying && isLoading) {
      setIsLoading(false);
    }
  }, [chatbotState.querying, isLoading]);

  const tryParseJSON = (text) => {
    try {
      const cleanText = text.replace(/^```json|```$/g, '').trim();
      const evaluatedText = cleanText.replace(
        /"value"\s*:\s*([0-9\.\s\+\-\*\/]+)/g,
        (_, expr) => `"value": ${eval(expr)}`
      );
      return JSON.parse(evaluatedText);
    } catch {
      return null;
    }
  };

  const sendQuery = async () => {
    logEvent('GenericAIChat', 'sendQuery', { query: newQuery });
    if (!newQuery.trim()) return;

    const currentQuery = newQuery;
    const updatedHistory = [...chatHistory, { type: 'user', text: currentQuery }];
    setChatHistory(updatedHistory);
    setNewQuery('');
    setIsLoading(true);

    // Use Redux action to handle the query - this will automatically update cache
    dispatch(queryRequest({ userId, userQuery: currentQuery }));

    // Call optional callback if provided
    if (onQuerySent) {
      onQuerySent(currentQuery);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') sendQuery();
  };

  const scrollToBottom = () => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatHistory, isLoading]);

  // Auto-scroll to bottom when chat window is opened
  useEffect(() => {
    if (showAIChat) {
      // Small delay to ensure the component is fully rendered
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [showAIChat]);

  const handleScroll = () => {
    const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
    setIsScrolledToBottom(scrollTop + clientHeight >= scrollHeight - 20);
  };

  const copyToClipboard = (text, index) => {
    navigator.clipboard.writeText(text);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  if (!showAIChat) {
    return null;
  }

  return (
    <>
      <style>
        {`
          @keyframes bounce-dot {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
          }
          .ai-chat-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            margin: 0 2px;
            background-color: ${currentTheme?.colors?.primary || '#8bc34a'};
            border-radius: 50%;
            animation: bounce-dot 1.4s infinite ease-in-out both;
          }
          .ai-chat-dot:nth-child(1) { animation-delay: -0.32s; }
          .ai-chat-dot:nth-child(2) { animation-delay: -0.16s; }
          .ai-chat-dot:nth-child(3) { animation-delay: 0s; }
        `}
      </style>

      <div
        className="fixed top-0 right-0 h-screen w-96 border-l shadow-2xl flex flex-col transition-all duration-300 ease-in-out z-50"
        style={{
          backgroundColor: darkMode ? '#1e293b' : '#ffffff',
          borderColor: '#e2e8f0'
        }}
      >
      
      {/* Header */}
      <div 
        className={`p-4 border-b flex items-center justify-between`} 
        style={{
          background: darkMode
            ? colors.darkCardBg
            : `linear-gradient(to right, ${currentTheme?.colors?.primary || '#8bc34a'}10, ${currentTheme?.colors?.accent || '#81c784'}05)`,
          borderColor: colors.border
        }}
      >
        <div className="flex items-center space-x-3">
          <div 
            className={`p-2 rounded-xl shadow-lg border`} 
            style={{ 
              backgroundColor: darkMode ? colors.darkCardBg : colors.cardBg, 
              borderColor: colors.border 
            }}
          >
            <FontAwesomeIcon 
              icon={faRobot} 
              className={`text-lg`} 
              style={{ color: currentTheme?.colors?.primary || '#8bc34a' }}
            />
          </div>
          <div>
            <h3 className={`text-lg font-bold`} style={{ color: colors.text }}>{title}</h3>
            <p className={`text-xs ${colors.neutral}`}>{subtitle}</p>
          </div>
        </div>
        <button 
          onClick={() => setShowAIChat(false)} 
          className={`p-2 rounded-xl transition-all duration-200 ${darkMode ? 'text-slate-400 hover:text-slate-300' : 'text-slate-500 hover:text-slate-700'}`} 
          onMouseEnter={(e) => e.target.style.backgroundColor = `${currentTheme?.colors?.primary || '#8bc34a'}20`}
          onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
        >
          <FontAwesomeIcon icon={faTimes} className="text-lg" />
        </button>
      </div>

      {/* Chat Messages Area */}
      <div 
        className="flex-1 p-4 overflow-y-auto space-y-4 scrollbar-hide" 
        style={{ 
          height: 'calc(100vh - 160px)', 
          WebkitOverflowScrolling: 'touch', 
          'scrollbar-width': 'none', 
          '::-webkit-scrollbar': { display: 'none' } 
        }} 
        ref={chatContainerRef} 
        onScroll={handleScroll}
      >
        {/* Welcome message with example queries */}
        {chatHistory.length === 0 && (
          <div className="flex items-start space-x-3 mb-4">
            <div
              className={`p-2 rounded-full`}
              style={{ backgroundColor: `${currentTheme?.colors?.primary || '#8bc34a'}20` }}
            >
              <FontAwesomeIcon
                icon={faRobot}
                style={{ color: currentTheme?.colors?.primary || '#8bc34a' }}
              />
            </div>
            <div className="flex-1">
              <div
                className={`inline-block max-w-full px-4 py-3 rounded-lg`}
                style={{ backgroundColor: `${currentTheme?.colors?.primary || '#8bc34a'}10`, color: colors.text }}
              >
                <p className="mb-3">{welcomeMessage}</p>

                <p className="mb-2 font-medium">Try these examples:</p>
                <div className="space-y-2">
                  {exampleQueries.map((example, index) => (
                    <button
                      key={index}
                      onClick={() => setNewQuery(example)}
                      className="block w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-200 hover:scale-105"
                      style={{
                        backgroundColor: `${currentTheme?.colors?.primary || '#8bc34a'}15`,
                        color: currentTheme?.colors?.primary || '#8bc34a'
                      }}
                    >
                      💬 "{example}"
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex items-start space-x-3">
          <div
            className={`p-2 rounded-full`}
            style={{ backgroundColor: `${currentTheme?.colors?.primary || '#8bc34a'}20` }}
          >
            <FontAwesomeIcon
              icon={faRobot}
              className={`text-sm`} 
              style={{ color: currentTheme?.colors?.primary || '#8bc34a' }}
            />
          </div>
          <div 
            className={`flex-1 p-3 rounded-xl max-w-xs`} 
            style={{ 
              backgroundColor: darkMode ? `${currentTheme?.colors?.primary || '#8bc34a'}20` : `${currentTheme?.colors?.primary || '#8bc34a'}10`,
              color: colors.text 
            }}
          >
            <p className="text-sm">
              {welcomeMessage}
            </p>
          </div>
        </div>

        {chatHistory.map((msg, index) => (
          <div 
            key={index} 
            className={`flex flex-col ${msg.type === 'user' ? 'items-start' : 'items-end'}`}
          >
            <div 
              className={`inline-block max-w-full px-4 py-2 rounded-lg ${msg.type === 'user' ? `bg-blue-100 text-blue-900` : `bg-green-100 text-green-900`} ml-2`}
            >
              {msg.text}
            </div>
            {msg.type === 'ai' && (
              <div className={`flex space-x-2 mt-1 mr-2 items-center relative`}>
                <FontAwesomeIcon 
                  icon={faThumbsUp} 
                  className={`w-4 h-4 cursor-pointer ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} 
                />
                <FontAwesomeIcon 
                  icon={faThumbsDown} 
                  className={`w-4 h-4 cursor-pointer ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} 
                />
                <FontAwesomeIcon 
                  icon={faCopy} 
                  className={`w-4 h-4 cursor-pointer ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} 
                  onClick={() => copyToClipboard(msg.text, index)} 
                />
                {copiedIndex === index && (
                  <span className={`absolute -top-6 left-1/2 -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-0.5 rounded shadow-md`}>
                    Copied!
                  </span>
                )}
              </div>
            )}
          </div>
        ))}
        {isLoading && (
          <div className="flex flex-col items-end">
            <div className="inline-block max-w-full px-4 py-2 rounded-lg bg-green-100 text-green-900 ml-2">
              <span className="ai-chat-dot"></span>
              <span className="ai-chat-dot"></span>
              <span className="ai-chat-dot"></span>
            </div>
          </div>
        )}
        <div ref={chatEndRef} />
      </div>

      {!isScrolledToBottom && (
        <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-10">
          <button
            onClick={scrollToBottom}
            className={`bg-gray-800 text-white text-sm px-3 py-1 rounded-md shadow-lg transition hover:bg-gray-700`}
          >
            ↓
          </button>
        </div>
      )}

      <div
        className={`p-4 border-t`}
        style={{ backgroundColor: darkMode ? colors.darkCardBg : colors.cardBg, borderColor: colors.border }}
      >
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={newQuery}
            onChange={(e) => setNewQuery(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder={placeholder}
            className={`flex-1 border rounded-xl px-4 py-3 focus:outline-none focus:ring-2 transition-all duration-200`}
            style={{
              backgroundColor: darkMode ? colors.bg : colors.cardBg,
              color: colors.text,
              borderColor: colors.border
            }}
            onFocus={(e) => e.target.style.borderColor = currentTheme?.colors?.primary || '#8bc34a'}
            onBlur={(e) => e.target.style.borderColor = colors.border}
          />
          <button
            onClick={sendQuery}
            disabled={isLoading}
            className={`p-3 rounded-xl text-white transition-all duration-200 font-semibold shadow-lg ${currentTheme.layout?.animations === false ? '' : 'hover:scale-105'}`}
            style={{
              backgroundColor: currentTheme?.colors?.primary || '#8bc34a',
              borderRadius: currentTheme.layout?.borderRadius === 'square' ? '6px' :
                          currentTheme.layout?.borderRadius === 'pill' ? '50%' : '12px',
              boxShadow: currentTheme.layout?.shadows === false ? 'none' : ''
            }}
          >
            <FontAwesomeIcon icon={faArrowUp} />
          </button>
        </div>

        {/* AI Search Indicator */}
        <div className="mt-2 flex items-center justify-between text-xs">
          <div className="flex items-center space-x-2">
            <div
              className="flex items-center space-x-1 px-2 py-1 rounded-full"
              style={{
                backgroundColor: `${currentTheme?.colors?.primary || '#8bc34a'}15`,
                color: currentTheme?.colors?.primary || '#8bc34a'
              }}
            >
              <span className="w-2 h-2 bg-current rounded-full animate-pulse"></span>
              <span className="font-medium">AI Assistant Active</span>
            </div>
          </div>
          <div className="text-xs opacity-60" style={{ color: colors.text }}>
            Natural language • Smart responses
          </div>
        </div>
      </div>
    </div>
    </>
  );
};

export default GenericAIChat;