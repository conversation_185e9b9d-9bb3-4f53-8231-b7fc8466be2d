import { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faTimes,
  faArrowUp,
  faRobot,
  faThumbsUp,
  faThumbsDown,
  faCopy
} from "@fortawesome/free-solid-svg-icons";
import { getCurrentUserId } from '@pp-web/utils/AuthUtil';
import { logEvent } from '@pp-web/utils/EventLogger';
import {
  setCustomSearchText,
  setSearchDate,
  setSelectedDateRange,
  applyFilters
} from '@pp-logic/redux/transactionSlice';
import { fetchHistoryRequest, queryRequest } from '@pp-logic/redux/chatbotSlice';

const TransactionAIChat = ({ darkMode, currentTheme, setShowAIChat, showAIChat }) => {
  const colors = currentTheme?.colors || {};

  // Early return if theme is not loaded
  if (!currentTheme?.colors) {
    return null;
  }
  const userId = getCurrentUserId();
  const [chatHistory, setChatHistory] = useState([]);
  const [newQuery, setNewQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState(null);
  const chatContainerRef = useRef(null);
  const chatEndRef = useRef(null);

  const cache = useSelector(state => state.cache);
  const chatbotState = useSelector(state => state.chatbot);
  const dispatch = useDispatch();

  useEffect(() => {
    if (cache?.chatbotHistoryLoaded && cache?.chatbotHistory?.length >= 0 &&
        cache?.chatbotHistoryParams?.userId === userId) {
      const flatHistory = cache.chatbotHistory.flatMap(log => {
        const parsed = tryParseJSON(log.response);
        return [
          { type: 'user', text: log.userQuery },
          { type: 'ai', text: parsed?.summary || log.response, chatId: log.id }
        ];
      });
      setChatHistory(flatHistory);
    } else {
      dispatch(fetchHistoryRequest({ userId }));
    }
  }, [dispatch, userId, cache?.chatbotHistoryLoaded]);

  useEffect(() => {
    if (cache?.chatbotHistoryLoaded && cache?.chatbotHistory) {
      const flatHistory = cache.chatbotHistory.flatMap(log => {
        const parsed = tryParseJSON(log.response);
        return [
          { type: 'user', text: log.userQuery },
          { type: 'ai', text: parsed?.summary || log.response, chatId: log.id }
        ];
      });
      setChatHistory(flatHistory);
      setIsLoading(false);
    }
  }, [cache?.chatbotHistory, cache?.chatbotHistoryLoaded]);

  // Listen for chatbot query completion to stop loading
  useEffect(() => {
    if (!chatbotState.querying && isLoading) {
      setIsLoading(false);
    }
  }, [chatbotState.querying, isLoading]);

  const tryParseJSON = (text) => {
    try {
      const cleanText = text.replace(/^```json|```$/g, '').trim();
      const evaluatedText = cleanText.replace(
        /"value"\s*:\s*([0-9\.\s\+\-\*\/]+)/g,
        (_, expr) => `"value": ${eval(expr)}`
      );
      return JSON.parse(evaluatedText);
    } catch {
      return null;
    }
  };

  // Simple mapping function for common date expressions
  const mapToDateRange = (query) => {
    const lowerQuery = query.toLowerCase();

    // Direct mappings to predefined ranges
    if (lowerQuery.includes('previous month') || lowerQuery.includes('last month') || lowerQuery.includes('past month')) {
      return 'previousMonth';
    }
    if (lowerQuery.includes('current month') || lowerQuery.includes('this month')) {
      return 'currentMonth';
    }
    if (lowerQuery.includes('last 7 days') || lowerQuery.includes('past week') || lowerQuery.includes('this week')) {
      return 'last7days';
    }
    if (lowerQuery.includes('last 6 months') || lowerQuery.includes('past 6 months')) {
      return 'last6months';
    }

    return null;
  };

  const sendQuery = async () => {
    logEvent('TransactionAIChat', 'sendQuery', { query: newQuery });
    if (!newQuery.trim()) return;

    const currentQuery = newQuery;
    const updatedHistory = [...chatHistory, { type: 'user', text: currentQuery }];
    setChatHistory(updatedHistory);
    setNewQuery('');
    setIsLoading(true);

    // Use Redux action to handle the query - this will automatically update cache
    dispatch(queryRequest({ userId, userQuery: currentQuery }));

    // Enhanced query parsing
    let searchText = '';
    let dateRange = null;
    let startDate = '';
    let endDate = '';
    let parsedQuery = null;

    try {
      // Import enhanced search utilities
      const { parseSearchQuery } = await import('../../../../logic/utils/enhancedTransactionSearch.js');

      // First, try simple date mapping
      const mappedRange = mapToDateRange(currentQuery);
      if (mappedRange) {
        dateRange = mappedRange;
      }

      // Use enhanced query parsing for merchant extraction
      parsedQuery = parseSearchQuery(currentQuery);
      console.log('🔍 Enhanced parsing result:', parsedQuery);

      // Extract merchant information
      if (parsedQuery.merchants.length > 0) {
        searchText = parsedQuery.merchants[0].name;
        console.log('✅ Enhanced searchText:', searchText);
      } else {
        console.log('⚠️ No merchants found in enhanced parsing, trying fallback');
        // Fallback: try to extract merchant from the query
        const fallbackPatterns = [
          /\b(?:show|find|search)\s+(?:me\s+)?(?:my\s+)?(.+?)(?:\s+transactions?|\s+for|\s+from|\s+in|\s+during|$)/i,
          /\b(.+?)\s+transactions?\s+(?:for|from|in|during)/i,
          /\btransactions?\s+(?:for|from|at)\s+(.+?)(?:\s+for|\s+from|\s+in|\s+during|$)/i
        ];

        for (const pattern of fallbackPatterns) {
          const match = currentQuery.match(pattern);
          if (match && match[1]) {
            const extracted = match[1].trim().toLowerCase();

            // Filter out common words AND date-related terms
            const excludeWords = [
              'my', 'the', 'all', 'some', 'any', 'recent', 'latest',
              'yesterday', 'today', 'tomorrow', 'last', 'past', 'previous', 'next', 'this',
              'week', 'month', 'year', 'day', 'quarter', 'ago', 'days', 'weeks', 'months', 'years'
            ];

            const containsDateTerms = excludeWords.some(word => extracted.includes(word));

            if (!containsDateTerms && extracted.length > 1) {
              searchText = extracted;
              console.log('🔄 Fallback searchText:', searchText);
              break;
            } else if (containsDateTerms) {
              console.log('🚫 Skipping date-related term in enhanced fallback:', extracted);
            }
          }
        }
      }

    } catch (error) {
      console.warn('⚠️ Enhanced parsing failed, falling back to basic parsing:', error);

      // Fallback to original parsing logic
      const lowerQuery = currentQuery.toLowerCase();

      const descriptionPatterns = [
        /\bsearch (?:for |by )?(?:transactions |transaction )?(?:for |by )?(.+?)(?: in | from | for |$)/,
        /\bshow (?:me )?(?:transactions |transaction )?(?:for |by )?(.+?)(?: in | from | for |$)/,
        /\bfind (?:transactions |transaction )?(?:for |by )?(.+?)(?: in | from | for |$)/
      ];

      const dateRangePatterns = [
        { pattern: /\b(last 7 days|past week|this week|recent week)\b/, value: 'last7days' },
        { pattern: /\b(current month|this month)\b/, value: 'currentMonth' },
        { pattern: /\b(previous month|last month)\b/, value: 'previousMonth' },
        { pattern: /\b(last 6 months|past 6 months|half year)\b/, value: 'last6months' },
        { pattern: /\b(all transactions|all time|everything)\b/, value: 'all' },
        {
          pattern: /\bfrom (\d{4}-\d{2}-\d{2}) to (\d{4}-\d{2}-\d{2})\b/,
          value: 'custom',
          extractDates: (match) => ({ start: match[1], end: match[2] })
        }
      ];

      for (const pattern of descriptionPatterns) {
        const match = lowerQuery.match(pattern);
        if (match) {
          searchText = match[1].trim();
          console.log('🔄 Fallback searchText:', searchText);
          break;
        }
      }

      for (const { pattern, value, extractDates } of dateRangePatterns) {
        const match = lowerQuery.match(pattern);
        if (match) {
          dateRange = value;
          console.log('🔄 Fallback dateRange:', dateRange);
          if (value === 'custom' && extractDates) {
            const dates = extractDates(match);
            startDate = dates.start;
            endDate = dates.end;
          }
          break;
        }
      }
    }

    if (dateRange) {
      if (dateRange === 'custom' && startDate && endDate) {
        if (new Date(endDate) >= new Date(startDate)) {
          dispatch(setSearchDate({ start: startDate, end: endDate }));
          dispatch(setSelectedDateRange('custom'));
          dispatch(applyFilters());
        } else {
          setChatHistory([...updatedHistory, {
            type: 'ai',
            text: '⚠️ End date cannot be before start date. Please provide valid dates.'
          }]);
        }
      } else {
        dispatch(setSelectedDateRange(dateRange));
        dispatch(applyFilters());
      }
    }

    if (searchText) {
      console.log('🎯 Final searchText being applied:', searchText);
      dispatch(setCustomSearchText(searchText));
      dispatch(applyFilters()); // Apply the search filter
    } else {
      console.log('⚠️ No searchText extracted from query, trying basic extraction:', currentQuery);

      // Last resort: try to extract any meaningful terms from the query
      const basicPatterns = [
        /\b(?:show|find|search)\s+(?:me\s+)?(?:my\s+)?(.+?)(?:\s+transactions?)?$/i,
        /\b(.+?)\s+transactions?$/i,
        /\btransactions?\s+(?:for|from|at)\s+(.+?)$/i
      ];

      let basicSearchText = '';
      for (const pattern of basicPatterns) {
        const match = currentQuery.match(pattern);
        if (match && match[1]) {
          const extracted = match[1].trim().toLowerCase();

          // Exclude common words AND date-related terms
          const excludeWords = [
            'my', 'the', 'all', 'some', 'any', 'recent', 'latest',
            'yesterday', 'today', 'tomorrow', 'last', 'past', 'previous', 'next', 'this',
            'week', 'month', 'year', 'day', 'quarter', 'ago', 'days', 'weeks', 'months', 'years'
          ];

          // Check if the extracted text contains any date-related terms
          const containsDateTerms = excludeWords.some(word => extracted.includes(word));

          if (!containsDateTerms && extracted.length > 1) {
            basicSearchText = extracted;
            console.log('🔧 Basic extraction found:', basicSearchText);
            break;
          } else if (containsDateTerms) {
            console.log('🚫 Skipping date-related term:', extracted);
          }
        }
      }

      if (basicSearchText) {
        dispatch(setCustomSearchText(basicSearchText));
        dispatch(applyFilters());
      }
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') sendQuery();
  };

  const scrollToBottom = () => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatHistory, isLoading]);

  // Auto-scroll to bottom when chat window is opened
  useEffect(() => {
    if (showAIChat) {
      // Small delay to ensure the component is fully rendered
      setTimeout(() => {
        scrollToBottom();
      }, 100);
    }
  }, [showAIChat]);


  const copyToClipboard = (text, index) => {
    navigator.clipboard.writeText(text);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };



  if (!showAIChat) {
    return null;
  }

  // Don't render if not shown
  if (!showAIChat) {
    return null;
  }

  return (
    <>
      <style>
        {`
          @keyframes bounce-dot {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
          }
          .ai-chat-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            margin: 0 2px;
            background-color: ${currentTheme?.colors?.primary || '#8bc34a'};
            border-radius: 50%;
            animation: bounce-dot 1.4s infinite ease-in-out both;
          }
          .ai-chat-dot:nth-child(1) { animation-delay: -0.32s; }
          .ai-chat-dot:nth-child(2) { animation-delay: -0.16s; }
          .ai-chat-dot:nth-child(3) { animation-delay: 0s; }
        `}
      </style>

      <div
        className="fixed top-0 right-0 h-full w-96 border-l shadow-2xl z-50 flex flex-col bg-white transition-transform duration-300 ease-out"
        style={{
          backgroundColor: darkMode ? '#1e293b' : '#ffffff',
          borderColor: '#e2e8f0',
          zIndex: 50,
          transform: showAIChat ? 'translateX(0)' : 'translateX(100%)'
        }}
      >
      
      {/* Header */}
      <div 
        className={`p-4 border-b flex items-center justify-between`} 
        style={{
          background: darkMode
            ? colors.darkCardBg
            : `linear-gradient(to right, ${currentTheme?.colors?.primary || '#8bc34a'}10, ${currentTheme?.colors?.accent || '#81c784'}05)`,
          borderColor: colors.border
        }}
      >
        <div className="flex items-center space-x-3">
          <div 
            className={`p-2 rounded-xl shadow-lg border`} 
            style={{ 
              backgroundColor: darkMode ? colors.darkCardBg : colors.cardBg, 
              borderColor: colors.border 
            }}
          >
            <FontAwesomeIcon 
              icon={faRobot} 
              className={`text-lg`} 
              style={{ color: currentTheme?.colors?.primary || '#8bc34a' }}
            />
          </div>
          <div>
            <h3 className={`text-lg font-bold`} style={{ color: colors.text }}>Transaction AI Assistant</h3>
            <p className={`text-xs ${colors.neutral}`}>Ask about your transactions</p>
          </div>
        </div>
        <button 
          onClick={() => setShowAIChat(false)} 
          className={`p-2 rounded-xl transition-all duration-200 ${darkMode ? 'text-slate-400 hover:text-slate-300' : 'text-slate-500 hover:text-slate-700'}`} 
          onMouseEnter={(e) => e.target.style.backgroundColor = `${currentTheme?.colors?.primary || '#8bc34a'}20`}
          onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
        >
          <FontAwesomeIcon icon={faTimes} className="text-lg" />
        </button>
      </div>

      {/* Chat Messages Area */}
      <div 
        className="flex-1 p-4 overflow-y-auto space-y-4 scrollbar-hide" 
        style={{ 
          height: 'calc(100vh - 160px)', 
          WebkitOverflowScrolling: 'touch', 
          'scrollbar-width': 'none', 
          '::-webkit-scrollbar': { display: 'none' } 
        }} 
        ref={chatContainerRef}
      >
        {/* Welcome message with example queries */}
        {chatHistory.length === 0 && (
          <div className="flex items-start space-x-3 mb-4">
            <div
              className={`p-2 rounded-full`}
              style={{ backgroundColor: `${currentTheme?.colors?.primary || '#8bc34a'}20` }}
            >
              <FontAwesomeIcon
                icon={faRobot}
                style={{ color: currentTheme?.colors?.primary || '#8bc34a' }}
              />
            </div>
            <div className="flex-1">
              <div
                className={`inline-block max-w-full px-4 py-3 rounded-lg`}
                style={{ backgroundColor: `${currentTheme?.colors?.primary || '#8bc34a'}10`, color: colors.text }}
              >
                <p className="mb-3">👋 Hi! I'm your enhanced AI assistant. I can help you search transactions using natural language!</p>

                <p className="mb-2 font-medium">Try these examples:</p>
                <div className="space-y-2">
                  {[
                    "Show me Starbucks purchases last month",
                    "Find gas stations this week under $50",
                    "Amazon orders over $25 last quarter",
                    "Coffee shops yesterday"
                  ].map((example, index) => (
                    <button
                      key={index}
                      onClick={() => setNewQuery(example)}
                      className="block w-full text-left px-3 py-2 rounded-lg text-sm transition-all duration-200 hover:scale-105"
                      style={{
                        backgroundColor: `${currentTheme?.colors?.primary || '#8bc34a'}15`,
                        color: currentTheme?.colors?.primary || '#8bc34a'
                      }}
                    >
                      💬 "{example}"
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex items-start space-x-3">
          <div
            className={`p-2 rounded-full`}
            style={{ backgroundColor: `${currentTheme?.colors?.primary || '#8bc34a'}20` }}
          >
            <FontAwesomeIcon
              icon={faRobot}
              className={`text-sm`} 
              style={{ color: currentTheme?.colors?.primary || '#8bc34a' }}
            />
          </div>
          <div 
            className={`flex-1 p-3 rounded-xl max-w-xs`} 
            style={{ 
              backgroundColor: darkMode ? `${currentTheme?.colors?.primary || '#8bc34a'}20` : `${currentTheme?.colors?.primary || '#8bc34a'}10`,
              color: colors.text 
            }}
          >
            <p className="text-sm">
              Hi! I'm your Transaction AI Assistant. I can help you filter transactions, analyze spending patterns, or answer questions about your transactions. Try asking things like "Show transactions for coffee" or "Find transactions from last month".
            </p>
          </div>
        </div>

        {chatHistory.map((msg, index) => (
          <div 
            key={index} 
            className={`flex flex-col ${msg.type === 'user' ? 'items-start' : 'items-end'}`}
          >
            <div 
              className={`inline-block max-w-full px-4 py-2 rounded-lg ${msg.type === 'user' ? `bg-blue-100 text-blue-900` : `bg-green-100 text-green-900`} ml-2`}
            >
              {msg.text}
            </div>
            {msg.type === 'ai' && (
              <div className={`flex space-x-2 mt-1 mr-2 items-center relative`}>
                <FontAwesomeIcon 
                  icon={faThumbsUp} 
                  className={`w-4 h-4 cursor-pointer ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} 
                />
                <FontAwesomeIcon 
                  icon={faThumbsDown} 
                  className={`w-4 h-4 cursor-pointer ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} 
                />
                <FontAwesomeIcon 
                  icon={faCopy} 
                  className={`w-4 h-4 cursor-pointer ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} 
                  onClick={() => copyToClipboard(msg.text, index)} 
                />
                {copiedIndex === index && (
                  <span className={`absolute -top-6 left-1/2 -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-0.5 rounded shadow-md`}>
                    Copied!
                  </span>
                )}
              </div>
            )}
          </div>
        ))}
        {isLoading && (
          <div className="flex flex-col items-end">
            <div className="inline-block max-w-full px-4 py-2 rounded-lg bg-green-100 text-green-900 ml-2">
              <span className="ai-chat-dot"></span>
              <span className="ai-chat-dot"></span>
              <span className="ai-chat-dot"></span>
            </div>
          </div>
        )}
        <div ref={chatEndRef} />
      </div>



      <div 
        className={`p-4 border-t`} 
        style={{ backgroundColor: darkMode ? colors.darkCardBg : colors.cardBg, borderColor: colors.border }}
      >
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={newQuery}
            onChange={(e) => setNewQuery(e.target.value)}
            onKeyDown={handleKeyPress}
            placeholder="Try: 'Starbucks last month over $5' or 'gas stations this week'"
            className={`flex-1 border rounded-xl px-4 py-3 focus:outline-none focus:ring-2 transition-all duration-200`}
            style={{
              backgroundColor: darkMode ? colors.bg : colors.cardBg,
              color: colors.text,
              borderColor: colors.border
            }}
            onFocus={(e) => e.target.style.borderColor = currentTheme?.colors?.primary || '#8bc34a'}
            onBlur={(e) => e.target.style.borderColor = colors.border}
          />
          <button
            onClick={sendQuery}
            disabled={isLoading}
            className={`p-3 rounded-xl text-white transition-all duration-200 font-semibold shadow-lg ${currentTheme.layout?.animations === false ? '' : 'hover:scale-105'}`}
            style={{
              backgroundColor: currentTheme?.colors?.primary || '#8bc34a',
              borderRadius: currentTheme.layout?.borderRadius === 'square' ? '6px' :
                          currentTheme.layout?.borderRadius === 'pill' ? '50%' : '12px',
              boxShadow: currentTheme.layout?.shadows === false ? 'none' : ''
            }}
          >
            <FontAwesomeIcon icon={faArrowUp} />
          </button>
        </div>

        {/* Enhanced Search Indicator */}
        <div className="mt-2 flex items-center justify-between text-xs">
          <div className="flex items-center space-x-2">
            <div
              className="flex items-center space-x-1 px-2 py-1 rounded-full"
              style={{
                backgroundColor: `${currentTheme?.colors?.primary || '#8bc34a'}15`,
                color: currentTheme?.colors?.primary || '#8bc34a'
              }}
            >
              <span className="w-2 h-2 bg-current rounded-full animate-pulse"></span>
              <span className="font-medium">Enhanced AI Search Active</span>
            </div>
          </div>
          <div className="text-xs opacity-60" style={{ color: colors.text }}>
            Natural language • Fuzzy matching • Smart dates
          </div>
        </div>
      </div>
    </div>
    </>
  );
};

export default TransactionAIChat;