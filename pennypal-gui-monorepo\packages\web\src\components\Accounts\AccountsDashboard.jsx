import React, { useState, useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { PlaidLink } from "react-plaid-link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faLink,
  faSync,
  faPlus,
  faRedo,
  faTimes,
  faClock,
  faUniversity,
  faExclamationTriangle, 
  faArrowUp, 
  faArrowDown, 
  faEquals,  
  faSpinner 
} from "@fortawesome/free-solid-svg-icons";
import AccountChart from "./AccountChart";
import BankIcon from "./BankIcon";
import AccountMiniChart from "./AccountMiniChart";
import {
  fetchAccountDetails,
  exchangePublicToken,
  refreshAllAccounts,
  syncAccount,
  connectMx,
  connectStripe,
  connectFinicity,
  resetError
} from "../../../../logic/redux/accountsDashboardSlice";
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { getCurrentUserId } from "../../utils/AuthUtil";
import { logEvent } from '../../utils/EventLogger';
import { fetchDeltaData } from '../../../../logic/redux/deltaSlice';
import { 
  selectDeltaData, 
  selectDeltaLoading, 
  selectDeltaError,
  selectDeltaAccounts,
  selectDeltaSummary 
} from '../../../../logic/redux/deltaSlice';
import { selectTimePeriod } from '../../../../logic/redux/accountChartSlice';
import { useCacheAwareness } from '../../../../logic/components/CacheAwareWrapper';
import './AccountDashboard.css';

// Utility function to format last sync time
const formatLastSyncTime = (lastSyncTimeStr) => {
  if (!lastSyncTimeStr) return "Never synced";
  
  const now = new Date();
  const lastSyncTime = new Date(lastSyncTimeStr);
  const diffMs = now - lastSyncTime;
  
  const diffMins = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
  
  if (diffMins < 1) return "Just now";
  if (diffMins < 60) return `${diffMins} min${diffMins !== 1 ? 's' : ''} ago`;
  if (diffHours < 24) return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`;
  if (diffDays < 30) return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`;
  
  return lastSyncTime.toLocaleDateString();
};

const maskAccountNumber = (accountMask) => {
  if (!accountMask) return "N/A";
  const visibleDigits = accountMask.slice(-4);
  return "******" + visibleDigits;
};

const ALL_CATEGORIES = [
  "Cash",
  "CreditCards",
  "LoanAccounts",
  "Investments",
 
];

const formatCategoryName = (boxId) => {
  switch (boxId) {
    case "Cash":
      return "Cash";
    case "CreditCards":
      return "Credit Cards";
    case "LoanAccounts":
      return "Loan Accounts";
    case "Investments":
      return "Investments";
   
    default:
      return boxId;
  }
};

// Custom 8-dot drag handle SVG component
const DragHandleIcon = ({ darkMode }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={`cursor-move ${darkMode ? 'fill-gray-400' : 'fill-gray-600'}`}
  >
    <circle cx="4" cy="4" r="1.5" />
    <circle cx="4" cy="8" r="1.5" />
    <circle cx="4" cy="12" r="1.5" />
    <circle cx="4" cy="16" r="1.5" />
    <circle cx="12" cy="4" r="1.5" />
    <circle cx="12" cy="8" r="1.5" />
    <circle cx="12" cy="12" r="1.5" />
    <circle cx="12" cy="16" r="1.5" />
  </svg>
);

const AccountsDashboard = ({ darkMode }) => {
  const userId = getCurrentUserId();
  const dispatch = useDispatch();
  const selectedTimePeriod = useSelector(selectTimePeriod);

  // Cache awareness for accounts data
  const {
    isReady: cacheReady,
    isCacheLoaded,
    getCacheData,
    isCacheLoading
  } = useCacheAwareness(['userAccounts'], false); // Disable debug mode
  
  // Get delta data
  const deltaData = useSelector(selectDeltaData);
  const deltaLoading = useSelector(selectDeltaLoading);
  const deltaError = useSelector(selectDeltaError);
  const deltaAccounts = useSelector(selectDeltaAccounts);
  const deltaSummary = useSelector(selectDeltaSummary);
  
  // Map chart time periods to delta time periods
const mapTimePeriodForDelta = (chartTimePeriod) => {
  const mapping = {
    'one-month': 'one-month',           // Maps to '1month' endpoint
    'three-month': 'three-month',       // Maps to '3months' endpoint
    'half-year': 'half-year',           // Maps to '6months' endpoint
    'yearly': 'yearly',                 // Maps to '1year' endpoint
    'ytd': 'ytd',                       // Maps to 'ytd' endpoint
    'quarterly-aggregate': 'quarterly-rolling', // Updated mapping
    'quarterly-rolling': 'quarterly-rolling'    // Direct mapping
  };
  
  return mapping[chartTimePeriod] || 'three-month';
};

  // Fetch delta data when time period changes
  useEffect(() => {
    if (selectedTimePeriod) {
      const deltaTimePeriod = mapTimePeriodForDelta(selectedTimePeriod);
      dispatch(fetchDeltaData({ timePeriod: deltaTimePeriod }));
    }
  }, [dispatch, selectedTimePeriod]);

  // Group delta accounts by type for lookup
  const deltaAccountsMap = useMemo(() => {
    if (!deltaAccounts || deltaAccounts.length === 0) return {};
    
    return deltaAccounts.reduce((map, account) => {
      map[account.accountId] = account;
      return map;
    }, {});
  }, [deltaAccounts]);

  // Color theme based on dark mode
  const colors = {
    positive: darkMode ? 'text-green-400' : 'text-green-600',
    negative: darkMode ? 'text-red-400' : 'text-red-600',
    neutral: darkMode ? 'text-gray-400' : 'text-gray-600',
    bg: darkMode ? 'bg-gray-800' : 'bg-white',
    cardBg: darkMode ? 'bg-gray-700' : 'bg-gray-50',
    text: darkMode ? 'text-white' : 'text-gray-800',
    border: darkMode ? 'border-gray-600' : 'border-gray-200'
  };

  // Format time period for display
  const formatTimePeriodDisplay = (period) => {
    const displayNames = {
      'one-month': '1 Month',
      'three-month': '3 Months',
      'half-year': '6 Months',
      'yearly': '1 Year',
      'ytd': 'Year to Date',
      'quarterly-aggregate': 'Quarterly'
    };
    return displayNames[period] || period;
  };

  // Render trend icon
  const TrendIcon = ({ trend, className = "" }) => {
    const iconProps = { className: `${className}` };
    
    switch (trend) {
      case 'increase':
        return <FontAwesomeIcon icon={faArrowUp} {...iconProps} />;
      case 'decrease':
        return <FontAwesomeIcon icon={faArrowDown} {...iconProps} />;
      default:
        return <FontAwesomeIcon icon={faEquals} {...iconProps} />;
    }
  };
  
  const { 
    linkToken, 
    accounts, 
    isLinked, 
    isLoading,
    balanceHistory,
    error,
    syncingAccounts = [],
    refreshAllStatus
  } = useSelector((state) => state.accounts);

  const [tableData, setTableData] = useState({
    Cash: [],
    CreditCards: [],
    Investments: [],
    LoanAccounts: [],
   
  });

  const [showConnectionModal, setShowConnectionModal] = useState(false);

  const [boxOrder, setBoxOrder] = useState(() => {
  const savedOrder = localStorage.getItem("accountsBoxOrder");
  const parsedOrder = savedOrder ? JSON.parse(savedOrder) : ALL_CATEGORIES;
  
  // Ensure we always have all 4 categories in the order
  const completeOrder = [...ALL_CATEGORIES];
  
  if (savedOrder) {
    // Reorder based on saved order, but ensure all categories are present
    const orderedCategories = [];
    parsedOrder.forEach(category => {
      if (ALL_CATEGORIES.includes(category)) {
        orderedCategories.push(category);
      }
    });
    
    // Add any missing categories to the end
    ALL_CATEGORIES.forEach(category => {
      if (!orderedCategories.includes(category)) {
        orderedCategories.push(category);
      }
    });
    
    return orderedCategories;
  }
  
  return completeOrder;
});
 
  // Track if refresh is in progress to avoid showing premature errors
  const [isRefreshInProgress, setIsRefreshInProgress] = useState(false);

  useEffect(() => {
    logEvent('AccountsDashboard', 'PageLoad', {});
     dispatch(fetchAccountDetails()); 
  }, [dispatch]);

  useEffect(() => {
    if (accounts && accounts.length > 0) {
      updateTableData(accounts);
    } else {
      console.log("Fetched accounts array is empty:", accounts);
    }
  }, [accounts, syncingAccounts, deltaAccountsMap]);

  useEffect(() => {
    const savedTableOrder = localStorage.getItem("accountsRowOrder");
    if (savedTableOrder) {
      const parsed = JSON.parse(savedTableOrder);
      setTableData((prev) => {
        const newData = { ...prev };
        for (const key in parsed) {
          if (newData[key]) {
            newData[key] = parsed[key]
              .map((savedId) => newData[key].find((d) => d.id === savedId))
              .filter(Boolean);
          }
        }
        return newData;
      });
    }
  }, []);

  // Monitor refresh status to control error display
useEffect(() => {
  if (refreshAllStatus === 'loading') {
    setIsRefreshInProgress(true);
  } else if (refreshAllStatus === 'success' || refreshAllStatus === 'failed') {
    setIsRefreshInProgress(false);
    // Clear the refresh status after showing success message briefly
    if (refreshAllStatus === 'success') {
      const timer = setTimeout(() => {
        dispatch(resetError()); // This should also reset refreshAllStatus in your slice
      }, 3000); // Show success message for 3 seconds
      return () => clearTimeout(timer);
    }
  }
}, [refreshAllStatus]);

  // Clear error after 5 seconds, but not if refresh is in progress
  useEffect(() => {
    if (error && !isRefreshInProgress) {
      const timer = setTimeout(() => {
        dispatch(resetError());
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, isRefreshInProgress]);

  const handleRowDragEnd = (result, category) => {
    if (!result.destination) return;

    const draggedAccount = tableData[category][result.source.index];
    logEvent('AccountsDashboard', 'ReorderAccountsInCategory', {
      category,
      accountId: draggedAccount?.id,
      fromIndex: result.source.index,
      toIndex: result.destination.index
    });

    const updatedRows = Array.from(tableData[category]);
    const [movedRow] = updatedRows.splice(result.source.index, 1);
    updatedRows.splice(result.destination.index, 0, movedRow);

    const newTableData = {
      ...tableData,
      [category]: updatedRows,
    };

    setTableData(newTableData);

    const rowOrder = JSON.parse(localStorage.getItem("accountsRowOrder") || "{}");
    rowOrder[category] = updatedRows.map((row) => row.id);
    localStorage.setItem("accountsRowOrder", JSON.stringify(rowOrder));
  };

  const formatAccounts = (type) =>
    accounts
      .filter((account) => account.accountType === type)
     .map((account, index) => {
        const accountId = account.id || account.accountId;
        const deltaAccount = deltaAccountsMap[accountId];
        
        return {
          id: accountId || index,
          institution: account.financialInstName || account.institutionId || "Unknown",
          accountName: account.accountName,
          accountNumber: maskAccountNumber(account.accountMask),
          numericBalance: account.balance || 0,
          balance: `$${account.balance ? account.balance.toFixed(2) : "0.00"}`,
          lastSyncTime: account.lastSyncTime || null,
          lastSyncFormatted: formatLastSyncTime(account.lastSyncTime),
          monthlyBalances: account.aggregatedBalances || [
            { month: "Jan", balance: (account.balance || 0) - 50 },
            { month: "Feb", balance: account.balance || 0 },
            { month: "Mar", balance: (account.balance || 0) + 75 },
          ],
          isSyncing: syncingAccounts.includes(accountId),
          // Delta data integration
          deltaData: deltaAccount ? {
            trend: deltaAccount.trend,
            percentage: deltaAccount.formattedDelta?.percentage || '0%',
            amount: deltaAccount.formattedDelta?.amount || '$0.00',
            hasDelta: true
          } : {
            trend: 'neutral',
            percentage: '0%',
            amount: '$0.00',
            hasDelta: false
          }
        };
      });

 

  const clearSavedRowOrders = () => {
    localStorage.removeItem("accountsRowOrder");
    window.location.reload();
  };

  const updateTableData = (accounts) => {
    const savedRowOrder = JSON.parse(localStorage.getItem("accountsRowOrder") || "{}");

    const formatted = {
      Cash: formatAccounts("depository"),
      CreditCards: formatAccounts("credit"),
      Investments: formatAccounts("investment"),
      LoanAccounts: formatAccounts("loan"),
     
    };

    const reordered = {};

    Object.entries(formatted).forEach(([key, list]) => {
      const order = savedRowOrder[key];
      if (order) {
        const orderedList = [];
        const idToRow = Object.fromEntries(list.map((row) => [row.id, row]));
        order.forEach((id) => {
          if (idToRow[id]) orderedList.push(idToRow[id]);
        });
        const remaining = list.filter((row) => !order.includes(row.id));
        reordered[key] = [...orderedList, ...remaining];
      } else {
        reordered[key] = list;
      }
    });

    setTableData(reordered);
  };

  const handleSyncAccount = (accountId) => {
    logEvent('AccountsDashboard', 'SyncAccount', {
      accountId,
      accountType: accounts.find(acc => acc.id === accountId || acc.accountId === accountId)?.accountType
    });

    setTableData((prevData) => {
      const newData = { ...prevData };
      Object.keys(newData).forEach((category) => {
        newData[category] = newData[category].map((acct) => {
          if (acct.id === accountId) {
            return { ...acct, isSyncing: true };
          }
          return acct;
        });
      });
      return newData;
    });

    dispatch(syncAccount(accountId)).finally(() => {
      setTableData((prevData) => {
        const newData = { ...prevData };
        Object.keys(newData).forEach((category) => {
          newData[category] = newData[category].map((acct) => {
            if (acct.id === accountId) {
              return { ...acct, isSyncing: false };
            }
            return acct;
          });
        });
        return newData;
      });
    });
  };

  const handleRefreshAll = () => {
  logEvent('AccountsDashboard', 'RefreshAllAccounts', {
    accountsCount: accounts?.length || 0,
    linkedAccountsCount: Object.values(tableData).flat().length
  });
  // Clear any existing errors before starting refresh
  dispatch(resetError());
  setIsRefreshInProgress(true);
  dispatch(refreshAllAccounts()).then(() => {
    // Ensure we reset the progress state when the promise resolves
    setIsRefreshInProgress(false);
  }).catch(() => {
    // Also reset on error
    setIsRefreshInProgress(false);
  });
};

  const handleDragEnd = (result) => {
    if (!result.destination) return;
    
    logEvent('AccountsDashboard', 'ReorderAccountCategories', {
      fromIndex: result.source.index,
      toIndex: result.destination.index,
      movedCategory: boxOrder[result.source.index]
    });

    const newOrder = Array.from(boxOrder);
    const [removed] = newOrder.splice(result.source.index, 1);
    newOrder.splice(result.destination.index, 0, removed);
    setBoxOrder(newOrder);
    localStorage.setItem("accountsBoxOrder", JSON.stringify(newOrder));
  };

  const onSuccess = async (publicToken, metadata) => {
    logEvent('AccountsDashboard', 'PlaidConnectionSuccess', {
      institutionId: metadata?.institution?.institution_id,
      institutionName: metadata?.institution?.name,
      accountsCount: metadata?.accounts?.length
    });
    try {
      await dispatch(exchangePublicToken(publicToken));
      await dispatch(fetchAccountDetails(1));
      setShowConnectionModal(false);
    } catch (err) {
      logEvent('AccountsDashboard', 'PlaidConnectionError', {
        error: err.message || 'Unknown error'
      });
      console.error("Error linking accounts:", err);
    }
  };

  const handleMxConnect = () => {
    dispatch(connectMx({ userId: userId }));
  };

  const handleStripeConnect = () => {
    dispatch(connectStripe());
  };

  const handleFinicityConnect = () => {
    dispatch(connectFinicity({ userId: userId }));
  };

  // Helper function to safely render error messages - exclude refresh-related errors while in progress
  const renderError = (error) => {
    if (!error) return null;
    
    // Don't show refresh-related errors while refresh is in progress
    if (isRefreshInProgress) {
      return null;
    }
    
    // Don't show certain sync-related errors that are expected during long operations
    const errorMessage = typeof error === 'string' ? error : (error?.message || error?.error || 'An error occurred');
    const isRefreshError = errorMessage.toLowerCase().includes('refresh') || 
                          errorMessage.toLowerCase().includes('synchronization') ||
                          errorMessage.toLowerCase().includes('sync');
    
    // Skip showing refresh/sync errors if refresh is in progress or recently completed
    if (isRefreshError && (isRefreshInProgress || refreshAllStatus === 'loading')) {
      return null;
    }
    
    return (
      <div className={`${darkMode ? 'bg-red-900 border-red-700 text-red-300' : 'bg-red-100 border-red-400 text-red-700'} border px-4 py-3 rounded mb-4 flex items-center`}>
        <FontAwesomeIcon icon={faExclamationTriangle} className="mr-2" />
        <span>{errorMessage}</span>
        <button 
          onClick={() => {
            logEvent('AccountsDashboard', 'DismissError', {
              errorMessage: typeof error === 'string' ? error : (error?.message || error?.error || 'Unknown error')
            });
            dispatch(resetError());
          }}
          className={`ml-auto ${darkMode ? 'text-red-400 hover:text-red-300' : 'text-red-500 hover:text-red-700'}`}
        >
          <FontAwesomeIcon icon={faTimes} />
        </button>
      </div>
    );
  };

  // Success message for refresh all
const renderRefreshSuccessMessage = () => {
  if (refreshAllStatus === 'success' && !isRefreshInProgress) {
    return (
      <div className={`${darkMode ? 'bg-green-900 border-green-700 text-green-300' : 'bg-green-100 border-green-400 text-green-700'} border px-4 py-3 rounded mb-4 flex items-center`}>
        <FontAwesomeIcon icon={faSync} className="mr-2" />
        <span>All accounts refreshed successfully!</span>
        <button 
          onClick={() => dispatch(resetError())}
          className={`ml-auto ${darkMode ? 'text-green-400 hover:text-green-300' : 'text-green-500 hover:text-green-700'}`}
        >
          <FontAwesomeIcon icon={faTimes} />
        </button>
      </div>
    );
  }
  return null;
};

  // Loading message for refresh all
const renderRefreshLoadingMessage = () => {
  if (refreshAllStatus === 'loading' || isRefreshInProgress) {
    return (
      <div className={`${darkMode ? 'bg-blue-900 border-blue-700 text-blue-300' : 'bg-blue-100 border-blue-400 text-blue-700'} border px-4 py-3 rounded mb-4 flex items-center`}>
        <FontAwesomeIcon icon={faSync} className="mr-2 fa-spin" />
        <span>Refreshing all accounts... This may take a few moments.</span>
      </div>
    );
  }
  return null;
};

  // Render a table for a specific category
const renderTable = (boxId, dragHandleProps) => {
  const data = tableData[boxId] || [];
  const categoryName = formatCategoryName(boxId);
  
  // Create mock data when no real data exists
  const displayData = data.length === 0 ? [{
    id: `mock-${boxId}`,
    institution: "***",
      // accountName: "***",
    accountNumber: "******0000",
    numericBalance: 0,
    balance: "$0.00",
    lastSyncTime: null,
    lastSyncFormatted: "Never synced",
    monthlyBalances: [
    { month: "Jan", balance: 0 },
    { month: "Feb", balance: 0 },
    { month: "Mar", balance: 0 }
  ],  // Empty array instead of [0,0,0]
      isSyncing: false,
      isMock: true,
      deltaData: {
        trend: 'neutral',
        percentage: '0%',
        amount: '$0.00',
        hasDelta: false
      }
    }] : data;

  const totalBalance = data
    .reduce((sum, row) => sum + (parseFloat(row.numericBalance) || 0), 0)
    .toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });

    const headers = [categoryName, "Account Number", `$${totalBalance}`, "Chart", "Last Sync", ""];
    const columnWidths = ["20%", "20%", "20%", "20%", "15%", "10%", "5%"];

  return (
    <div className={`border rounded-lg overflow-hidden w-full ${darkMode ? 'border-gray-600' : 'border-gray-300'} group relative`}>
      <table className="w-full font-roboto border-collapse">
        <colgroup>
          {columnWidths.map((width, i) => (
            <col key={i} style={{ width }} />
          ))}
        </colgroup>
        <thead>
          <tr className={`${darkMode ? 'bg-gray-700 text-gray-100 border-gray-600' : 'bg-[#c5e1a5] border-gray-300'} border-b text-[16px]`}>
            {headers.map((header, index) => (
              <th
                key={header}
                className={`p-2 text-left ${index === 0 ? 'relative' : ''}`}
              >
                {index === 0 ? (
                  <div className="flex items-center gap-1">
                    <div
                      {...dragHandleProps} // MOVE dragHandleProps here, only to the drag handle icon
                      className="opacity-0 group-hover:opacity-100 transition-opacity cursor-move"
                    >
                      <DragHandleIcon darkMode={darkMode} />
                    </div>
                    <span className="ml-1">{header}</span>
                  </div>
                ) : (
                  header
                )}
              </th>
            ))}
          </tr>
        </thead>
        <DragDropContext onDragEnd={(result) => handleRowDragEnd(result, boxId)}>
          <Droppable droppableId={`rows-${boxId}`}>
            {(provided) => (
              <tbody
                ref={provided.innerRef}
                {...provided.droppableProps}
                className="text-[15px]"
              >
                {displayData.map((row, index) => {
                  const isPositive = row.numericBalance >= 0;
                  const isMockRow = row.isMock;
 const deltaData = row.deltaData || {};
                    const trendColor = deltaData.trend === 'increase' ? colors.positive :
                                     deltaData.trend === 'decrease' ? colors.negative : colors.neutral;
                  return (
                    <Draggable key={row.id} draggableId={String(row.id)} index={index} isDragDisabled={isMockRow}>
                      {(provided, snapshot) => (
                        <tr
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...(isMockRow ? {} : provided.dragHandleProps)}
                          className={`
                            border-b ${darkMode ? 'border-gray-600 text-gray-100 hover:bg-gray-800' : 'border-gray-300 text-black hover:bg-green-100'}
                            ${isMockRow ? (darkMode ? 'bg-gray-800/50 text-gray-500' : 'bg-gray-50 text-gray-400') : (darkMode ? 'hover:bg-gray-800' : 'hover:bg-green-100')}
                            transition-colors duration-150
                            ${snapshot.isDragging ? "bg-white shadow-lg z-10" : ""}
                            ${!isPositive && !isMockRow ? "text-red-500" : ""}
                          `}
                          style={{
                            ...provided.draggableProps.style,
                            display: snapshot.isDragging ? "table" : "table-row",
                            width: "100%",
                          }}
                        >
                           
                          <td className="p-2 pl-[2%]">
                            <div className="flex items-center gap-3">
                              <BankIcon
                                institutionName={row.institution}
                                className="w-6 h-6"
                              />
                              <div>
                                <div>{row.institution}</div>
                                <div className="text-gray-500 text-xs">{row.accountName}</div>
                              </div>
                            </div>
                          </td>
                         <td className="p-2 pl-[2%]">{row.accountNumber}</td>
                          <td className="p-2">
  <div className="flex items-center space-x-4">
    <div className="text-sm font-semibold">
      {row.balance}
    </div>
    {/* Delta Section */}
    {!isMockRow && deltaData.hasDelta ? (
      <div className="flex items-center space-x-2">
        <TrendIcon 
          trend={deltaData.trend} 
          className={`text-sm ${trendColor}`}
        />
        <div className="flex flex-col">
          <span className={`text-sm font-small ${trendColor}`}>
            {deltaData.percentage}
          </span>
          <span className={`text-xs ${trendColor}`}>
            {deltaData.amount}
          </span>
        </div>
      </div>
    ) : isMockRow ? (
      <div className="text-sm">
        <span>$0.00</span>
      </div>
    ) : (
      <div className={`text-sm ${colors.neutral}`}>
        <span>--</span>
      </div>
    )}

    {/* Balance Section */}
    
  </div>
</td>

                         

                           
 <td className="p-2">
 {!isMockRow ? (
                            <AccountMiniChart 
                              accountId={row.id}
                              data={row.monthlyBalances} 
                              darkMode={darkMode}
                            />
			      ) : (
                                // Show empty space for mock chart
                                <div className="h-8 w-full">********</div>
                              )}
                          </td>
                         <td className="p-2 align-middle">
  <div className="flex flex-col items-center justify-center gap-2">
    
    {/* Sync Button */}
    {!isMockRow && (
      <div
        className={`cursor-pointer ${row.isSyncing ? "spinning" : ""}`}
        onClick={() => handleSyncAccount(row.id)}
        title="Sync Account"
      >
        <FontAwesomeIcon
          icon={faSync}
          className={
            row.isSyncing
              ? "fa-spin text-blue-500"
              : darkMode
              ? "text-gray-400 hover:text-blue-400"
              : "text-gray-500 hover:text-blue-500"
          }
        />
      </div>
    )}

    {/* Last Sync Time */}
    <div className="flex items-center gap-1">
      <FontAwesomeIcon 
        icon={faClock} 
        className={darkMode ? "text-gray-400" : "text-gray-500"} 
      />
      <span className="text-xs">{row.lastSyncFormatted}</span>
    </div>
    
  </div>
</td>


                          
                          <td className="p-2"></td>
                        </tr>
                      )}
                    </Draggable>
                  );
                })}
                {provided.placeholder}
              </tbody>
            )}
          </Droppable>
        </DragDropContext>
      </table>
    </div>
  );
};
  return (
    <div className={`min-h-screen w-full font-roboto overflow-x-hidden ${darkMode ? 'bg-gray-900 text-gray-100' : 'bg-white text-black'}`}>
      <div className="p-5 animate-fadeInPage">
        <div className="flex flex-col justify-between mb-5 ">
          <h2 className="text-2xl">Accounts</h2>
          <div className="flex justify-end space-x-3 mt-3">
            <button
              className={`${darkMode
                  ? 'bg-gray-700 hover:bg-gray-600 text-white'
                  : 'bg-[#8bc34a] hover:bg-[#6ec122] text-white'
                } py-2 px-4 rounded flex items-center`}
              onClick={() => setShowConnectionModal(true)}
            >
              <FontAwesomeIcon icon={faPlus} className="mr-2" /> Add Account
            </button>
            <button
              className={`${darkMode
                  ? 'bg-gray-700 hover:bg-gray-600 text-white'
                  : 'bg-[#8bc34a] hover:bg-[#6ec122] text-white'
                } py-2 px-4 rounded flex items-center disabled:opacity-50`}
              onClick={handleRefreshAll}
              disabled={!isLinked || refreshAllStatus === 'loading' || isRefreshInProgress}
            >
              <FontAwesomeIcon 
                icon={faRedo} 
                className={`mr-2  ${(refreshAllStatus === 'loading' || isRefreshInProgress) ? "fa-spin" : ""}`} 
              />
              {(refreshAllStatus === 'loading' || isRefreshInProgress) ? "Syncing..." : "Refresh All"}
            </button>
          </div>
        </div>

        {/* Loading Message for Refresh */}
        {renderRefreshLoadingMessage()}

        {/* Error Display - Only show if not refresh-related or refresh is not in progress */}
        {renderError(error)}
        
        {/* Success Message */}
        {renderRefreshSuccessMessage()}

        {isLinked && (
          <div className="mb-6">
            <AccountChart darkMode={darkMode} />
          </div>
        )}
        
        {/* Connection Modal */}
        {showConnectionModal && (
          <div className={`fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 p-5 z-50`}>
            <div className={`${darkMode ? 'bg-gray-800 text-white' : 'bg-white text-black'} p-6 rounded shadow-lg max-w-lg w-full`}>
              <div className="flex justify-between items-center border-b pb-3 mb-4">
                <p className="font-bold">Connect Your Financial Account</p>
                <FontAwesomeIcon
                  icon={faTimes}
                  className="cursor-pointer"
                  onClick={() => setShowConnectionModal(false)}
                />
              </div>
              <div className="space-y-4">
                {/* Plaid Connection */}
                {linkToken ? (
                  <PlaidLink
                    className={`flex items-center space-x-4 ${darkMode ? 'bg-gray-700 text-white' : 'bg-gray-800 text-white'} py-2 px-4 rounded hover:opacity-80 transition-opacity`}
                    token={linkToken}
                    onSuccess={onSuccess}
                  >
                    <img
                      src="/images/plaids.png"
                      alt="Plaid Icon"
                      className="w-6 h-6"
                    />
                    <span>Connect Plaid</span>
                  </PlaidLink>
                ) : (
                  <div className={`flex items-center space-x-4 ${darkMode ? 'bg-gray-700 text-white' : 'bg-gray-800 text-white'} py-2 px-4 rounded`}>
                    <FontAwesomeIcon icon={faLink} />
                    <div>
                      <span>Plaid</span>
                      <p className="text-xs">Loading connection...</p>
                    </div>
                  </div>
                )}
                <div className="text-center">--------------- OR ---------------</div>
                <button
                  className={`flex items-center space-x-4 ${darkMode ? 'bg-gray-700 text-white hover:bg-gray-600' : 'bg-gray-800 text-white hover:bg-gray-700'} py-2 px-4 rounded transition-colors w-full`}
                  onClick={handleMxConnect}
                >
                  <img src="/images/mx.png" alt="MX Icon" className="w-6 h-6" />
                  <span>Connect MX Financial</span>
                </button>
                <button
                  className={`flex items-center space-x-4 ${darkMode ? 'bg-gray-700 text-white hover:bg-gray-600' : 'bg-gray-800 text-white hover:bg-gray-700'} py-2 px-4 rounded transition-colors w-full`}
                  onClick={handleStripeConnect}
                >
                  <img
                    src="/images/stripe.png"
                    alt="Stripe Icon"
                    className="w-7 h-7"
                  />
                  <span>Connect Stripe</span>
                </button>
                <button
                  className={`flex items-center space-x-4 ${darkMode ? 'bg-gray-700 text-white hover:bg-gray-600' : 'bg-gray-800 text-white hover:bg-gray-700'} py-2 px-4 rounded transition-colors w-full`}
                  onClick={handleFinicityConnect}
                >
                  {/* <img src="/images/finicity.svg" alt="Finicity Icon" className="w-6 h-6"/> */}
                  {isLoading ? (
                    <FontAwesomeIcon icon={faSpinner} spin />
                  ) : (
                    <img src="/images/finicity.svg" alt="Finicity Icon" className="w-6 h-6" />
                  )}
                  <span>{isLoading ? "Connecting..." : "Connect with Finicity"}</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* No Accounts State */}
        {!isLinked && !showConnectionModal && (
          <div className={`text-center py-10 border border-dashed ${darkMode ? 'border-gray-600' : 'border-gray-300'} rounded-lg p-8`}>
            <FontAwesomeIcon icon={faUniversity} className={`text-4xl ${darkMode ? 'text-gray-500' : 'text-gray-400'} mb-4`} />
            <p className={`text-lg ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>You haven't connected any accounts yet.</p>
            <p className={`${darkMode ? 'text-gray-400' : 'text-gray-500'} mb-4`}>Click "Add Account" to get started.</p>
            <button
              className={`${darkMode ? 'bg-blue-700 hover:bg-blue-600' : 'bg-blue-600 hover:bg-blue-700'} text-white py-2 px-4 rounded`}
              onClick={() => setShowConnectionModal(true)}
            >
              <FontAwesomeIcon icon={faPlus} className="mr-2" /> Add Your First Account
            </button>
          </div>
        )}

        {/* Account Tables */}
     
{isLinked && (
  <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="categories">
      {(provided) => (
        <div
          className="space-y-6"
          {...provided.droppableProps}
          ref={provided.innerRef}
        >
          {boxOrder.map((boxId, index) => (
            <Draggable draggableId={boxId} index={index} key={boxId}>
                        {(provided, snapshot) => (
                <div
                  ref={provided.innerRef}
                  {...provided.draggableProps}
                  // REMOVE {...provided.dragHandleProps} from here
                  className={`${darkMode ? 'bg-gray-800 shadow-gray-900/20' : 'bg-white '} `}
                >
                  <h3 className={`text-xl font-semibold mb-4 ${darkMode ? 'text-white' : 'text-black'}`}>{boxId} </h3>
                  {renderTable(boxId, provided.dragHandleProps)} {/* Pass dragHandleProps to renderTable */}
                </div>
              )}
            </Draggable>
          ))}
          {provided.placeholder}
        </div>
      )}
    </Droppable>
  </DragDropContext>
)}
      </div>
 
    </div>
  );
};

export default AccountsDashboard;
