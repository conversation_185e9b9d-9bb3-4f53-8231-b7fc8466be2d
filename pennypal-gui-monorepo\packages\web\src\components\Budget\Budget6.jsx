import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import {
  setBudgetData,
  changeMonth,
  setToToday,
  toggleAllTables,
  toggleCategory,
  setExpandedCategories,
  toggleZeroBudget,
  formatCurrency,
  getProgress,
  calculateTotals,
} from "../../../../logic/redux/budgetSlice";
import BudgetPopupComponent from "./BudgetPopupComponent";
import EditBudgetPopupComponent from "./EditBudgetPopupComponent";
import PaymentLoader from "../load/PaymentLoader";
import GenericAIChat from "../AI/GenericAIChat";
import {
  FaWallet,
  FaDollarSign,
  FaCreditCard,
  FaBuilding,
  FaChevronDown,
  FaChevronUp,
  FaArrowCircleRight,
  FaArrowCircleLeft,
  FaPlus,
  FaMinus,
  Fa<PERSON>rash<PERSON>lt,
  FaQuestionCircle,
  FaMoneyBillWave,
  // APT-125 fix for correct icons not showing
  FaBusinessTime,
  FaCar,
  FaChild,
  FaFilm,
  FaGraduationCap,
  FaHeart,
  FaHome,
  FaHospital,
  FaPlane,
  FaShoppingCart,
  FaUtensils,
  /** End of APT-125 fix */
} from "react-icons/fa";
import {
  UilShoppingBasket,
  UilCoffee,
  UilAirplay,
  UilMoneyBill,
  UilWallet,
  UilDollarSign,
  UilCreditCard,
  UilBuilding,
} from "@iconscout/react-unicons";

const iconMapping = {
  FaMoneyBillWave,
  FaWallet,
  FaDollarSign,
  FaCreditCard,
  FaBuilding,
  FaQuestionCircle,
  // APT-125 fix for correct icons not showing
  FaBusinessTime,
  FaCar,
  FaChild,
  FaFilm,
  FaGraduationCap,
  FaHeart,
  FaHome,
  FaHospital,
  FaPlane,
  FaShoppingCart,
  FaUtensils,
  /** End of APT-125 fix */
  UilShoppingBasket,
  UilCoffee,
  UilAirplay,
  UilMoneyBill,
  UilWallet,
  UilDollarSign,
  UilCreditCard,
  UilBuilding,
};

const iconColorMapping = {
  FaMoneyBillWave: "#4caf50",
  FaWallet: "#3f51b5",
  FaDollarSign: "#4caf50",
  FaCreditCard: "#009688",
  FaBuilding: "#ff9800",
  FaQuestionCircle: "#607d8b",
  UilShoppingBasket: "#795548",
  UilCoffee: "#6f4e37",
  UilAirplay: "#009688",
  UilMoneyBill: "#4caf50",
  FaChevronDown: "#607d8b",
  FaChevronUp: "#607d8b",
  FaArrowCircleRight: "#009688",
  FaArrowCircleLeft: "#009688",
  FaPlus: "#8bc34a",
  FaMinus: "#f44336",
  FaTrashAlt: "#f44336",
};

const ProgressBar = ({ spent, budget }) => {
  const percentage = getProgress(spent, budget);
  let colorClass = "bg-[#8bc34a]";
  let shadowClass = "shadow-green-200";
  if (percentage > 100) {
    colorClass = "bg-red-500";
    shadowClass = "shadow-red-200";
  } else if (percentage > 75) {
    colorClass = "bg-amber-500";
    shadowClass = "shadow-amber-200";
  }
  return (
    <div className="mt-3 mb-4 w-full px-1">
      <div className="flex justify-between text-sm font-medium text-gray-600 dark:text-gray-300 mb-2">
        <span className="flex items-center gap-1">
          <span className="text-xs opacity-75">Spent:</span>
          <span className="font-bold">${formatCurrency(spent)}</span>
          <span className="text-xs opacity-75">of</span>
          <span className="font-bold">${formatCurrency(budget)}</span>
        </span>
        <span className={`font-bold px-2 py-1 rounded-full text-xs ${
          percentage > 100 ? 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300' :
          percentage > 75 ? 'bg-amber-100 text-amber-700 dark:bg-amber-900 dark:text-amber-300' :
          'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300'
        }`}>
          {percentage}%
        </span>
      </div>
      <div className="relative h-3 w-full bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden shadow-inner">
        <div
          className={`absolute top-0 left-0 h-full ${colorClass} rounded-full transition-all duration-700 ease-out ${shadowClass} shadow-sm`}
          style={{
            width: `${Math.min(percentage, 100)}%`,
            boxShadow: percentage > 0 ? '0 0 10px rgba(0,0,0,0.1)' : 'none'
          }}
        />
        {percentage > 100 && (
          <div className="absolute top-0 left-0 h-full w-full bg-red-500 opacity-20 animate-pulse rounded-full" />
        )}
      </div>
    </div>
  );
};

const Budget6 = ({ darkMode, onAIChatOpen, onAIChatClose }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    currentMonth,
    currentYear,
    budgetData,
    loading,
    expandedCategories,
    showZeroBudget,
    showAllTables,
  } = useSelector((state) => state.budget);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [isEditBudgetPopupOpen, setIsEditBudgetPopupOpen] = useState(false);
  const [selectedBudgetItem, setSelectedBudgetItem] = useState(null);
  const [clickTimeout, setClickTimeout] = useState(null);
  const [showAIChat, setShowAIChat] = useState(false);

  // Handle AI chat toggle with sidebar coordination
  const handleAIChatToggle = () => {
    const newShowAIChat = !showAIChat;
    setShowAIChat(newShowAIChat);

    // Call sidebar handlers if provided
    if (newShowAIChat && onAIChatOpen) {
      onAIChatOpen();
    } else if (!newShowAIChat && onAIChatClose) {
      onAIChatClose();
    }
  };

  // Handle AI chat close (for the close button in the chat)
  const handleAIChatClose = () => {
    setShowAIChat(false);
    if (onAIChatClose) {
      onAIChatClose();
    }
  };

  // Create a simple theme object for the AI chat component
  const currentTheme = {
    colors: {
      primary: "#8bc34a",
      secondary: "#7CB342",
      accent: "#9CCC65",
      success: "#4CAF50",
      background: darkMode ? "#0f172a" : "#f8fffe",
      cardBg: darkMode ? "#1e293b" : "#ffffff",
      darkCardBg: "#1e293b",
      text: darkMode ? "#f1f5f9" : "#334155",
      border: darkMode ? "#475569" : "#e2e8f0",
      neutral: darkMode ? "text-slate-400" : "text-slate-600"
    },
    layout: {
      animations: true,
      borderRadius: "rounded",
      shadows: true
    }
  };

  useEffect(() => {
    dispatch(setToToday());
  }, [dispatch]);

  if (loading) {
    return (
      <div
        className={`min-h-screen w-full p-5 flex flex-col items-center justify-center ${
          darkMode ? "bg-gray-900 text-gray-100" : "bg-white text-gray-900"
        }`}
      >
        <div className="relative">
          <PaymentLoader darkMode={darkMode} cycleDuration={8} />
          <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-600 opacity-20 rounded-full blur-xl animate-pulse" />
        </div>
        <p className={`mt-6 text-lg font-medium ${darkMode ? "text-gray-300" : "text-gray-600"}`}>
          Loading budget data...
        </p>
      </div>
    );
  }

  const calculateCategory1Actual = () => {
    const category1 = budgetData.find((cat) => cat.categoryName === "Income");
    if (!category1) return 0;
    return category1.subcategories.reduce(
      (total, sub) => total + (sub.actual || 0),
        0
    );
  };

  const category1Actual = calculateCategory1Actual();
  const { totalBudget, totalActual, totalRemaining } = calculateTotals(budgetData);

  const handleCategoryClick = (categoryId) => {
    navigate(`/dashboard/category-details/${categoryId}`);
  };

  const handleSubcategoryClick = (categoryId, subCategoryId) => {
    if (clickTimeout) {
      clearTimeout(clickTimeout);
    }

    const timeout = setTimeout(() => {
      navigate(`/dashboard/category-details/${categoryId}/${subCategoryId}`);
    }, 250);

    setClickTimeout(timeout);
  };

  const handleSubcategoryDoubleClick = (categoryId, subcategory) => {
    if (clickTimeout) {
      clearTimeout(clickTimeout);
      setClickTimeout(null);
    }

    const category = budgetData.find((cat) => cat.categoryId === categoryId);
    setSelectedBudgetItem({
      id: subcategory.id,
      categoryId,
      categoryName: category?.categoryName || "Unknown",
      subcategoryId: subcategory.subcategoryId,
      compositeKey: subcategory.compositeKey,
      subcategoryName: subcategory.subcategoryName,
      customSubCategoryName: subcategory.customSubCategoryName,
      allocated: subcategory.allocated,
      rollover: subcategory.rollover,
      isExcluded: subcategory.isExcluded || false,
      icon: subcategory.icon,
    });
    setIsEditBudgetPopupOpen(true);
  };

  const handleToggleAllTables = () => {
    dispatch(toggleAllTables());
    // Update expandedCategories for all categories
    const newExpandedState = {};
    budgetData.forEach((category) => {
      newExpandedState[category.categoryId] = !showAllTables;
    });
    dispatch(setExpandedCategories(newExpandedState));
  };

  const handlePopupToggle = () => setIsPopupOpen(!isPopupOpen);

  const handleSaveBudget = (budgetItem) => {
    dispatch({ type: "budget/addBudgetItem", payload: budgetItem });
    setIsPopupOpen(false);
  };

  const handleSaveEditBudget = (updatedBudget) => {
    dispatch({ type: "budget/saveBudget", payload: updatedBudget });
    setIsEditBudgetPopupOpen(false);
  };

  const handleCloseSubcategory = (categoryId, subcategoryId) => {
    const category = budgetData.find((cat) => cat.categoryId === categoryId);
    if (!category) return;
    const subcategory = category.subcategories.find(
      (sub) => sub.compositeKey === subcategoryId
    );
    if (!subcategory) return;
    const budgetId = subcategory.id;

    dispatch({
      type: "budget/deleteSubcategoryBudget",
      payload: { budgetId, categoryId, subcategoryId },
    });
  };

  const monthNames = [
    "January","February","March","April","May","June",
    "July","August","September","October","November","December"
  ];

  const summaryCards = [
    { title: "Income", value: category1Actual, icon: UilWallet, color: darkMode ? "bg-gray-700" : "bg-[#8bc34a]", bgColor: "bg-green-50 dark:bg-green-900/20" },
    { title: "Budget", value: totalBudget, icon: UilDollarSign, color: darkMode ? "bg-gray-700" : "bg-[#8bc34a]", bgColor: "bg-green-50 dark:bg-green-900/20" },
    { title: "Actual", value: totalActual, icon: UilCreditCard, color: darkMode ? "bg-gray-700" : "bg-[#8bc34a]", bgColor: "bg-green-50 dark:bg-green-900/20" },
    { title: "Remaining", value: totalRemaining, icon: UilBuilding, color: totalRemaining < 0 ? "bg-red-500" : (darkMode ? "bg-gray-700" : "bg-[#8bc34a]"), bgColor: totalRemaining < 0 ? "bg-red-50 dark:bg-red-900/20" : "bg-green-50 dark:bg-green-900/20" }
  ];

  return (
    <div className="min-h-screen w-full font-inter">
      {/* Main Content Area */}
      <div
        className={`p-6 transition-all duration-300 ${
          darkMode ? "bg-gray-900 text-gray-100" : "bg-white text-gray-900"
        }`}
        style={{ marginRight: showAIChat ? '384px' : '0' }}
      >
      {/* Header Section */}
      <div className="flex justify-between items-center mb-8">
        <div className="flex items-center gap-3">
          <div className={`p-3 rounded-xl ${darkMode ? "bg-gray-700" : "bg-[#8bc34a]"} shadow-lg`}>
            <FaWallet className="text-2xl text-white" />
          </div>
          <h1 className={`text-4xl font-bold ${darkMode ? "text-gray-100" : "text-gray-900"}`}>
            Budget
          </h1>
        </div>
        {/* Date Navigation */}
        <div className="flex items-center gap-4">
          {/* AI Chat Icon */}
          <button
            className={`p-3 rounded-xl transition-all duration-200 hover:scale-110 ${
              darkMode
                ? "bg-gray-800/50 border-gray-700/50 hover:bg-gray-800/70"
                : "bg-white/70 border-white/50 hover:bg-white/90"
            } backdrop-blur-lg border shadow-lg`}
            onClick={handleAIChatToggle}
          >
            <svg
              className="w-6 h-6 text-purple-500"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
              <path d="M19.5 8.5L20.5 11L23 12L20.5 13L19.5 15.5L18.5 13L16 12L18.5 11L19.5 8.5Z" />
              <path d="M4.5 16.5L5.5 19L8 20L5.5 21L4.5 23.5L3.5 21L1 20L3.5 19L4.5 16.5Z" />
            </svg>
          </button>
          <div className={`flex items-center gap-4 px-6 py-3 rounded-2xl backdrop-blur-lg border transition-all duration-300 ${
            darkMode
              ? "bg-gray-800/50 border-gray-700/50 hover:bg-gray-800/70"
              : "bg-white/70 border-white/50 hover:bg-white/90"
          } shadow-lg`}>
            <button
              className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
                darkMode ? "text-gray-300 hover:text-white hover:bg-gray-700" : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              }`}
              onClick={() => dispatch(setToToday())}
            >
              Today
            </button>
            <button
              className={`p-2 rounded-lg transition-all duration-200 hover:scale-110 ${
                darkMode ? "text-gray-300 hover:text-white hover:bg-gray-700" : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              }`}
              onClick={() => dispatch(changeMonth(-1))}
            >
              <FaArrowCircleLeft className="text-xl" />
            </button>
            <span className="text-lg font-semibold min-w-[150px] text-center">
              {monthNames[currentMonth]} {currentYear}
            </span>
            <button
              className={`p-2 rounded-lg transition-all duration-200 hover:scale-110 ${
                darkMode ? "text-gray-300 hover:text-white hover:bg-gray-700" : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
              }`}
              onClick={() => dispatch(changeMonth(1))}
            >
              <FaArrowCircleRight className="text-xl" />
            </button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {summaryCards.map((card, index) => (
          <div
            key={card.title}
            className={`relative overflow-hidden rounded-2xl p-6 backdrop-blur-lg border transition-all duration-300 hover:scale-105 hover:shadow-2xl ${
              darkMode
                ? "bg-gray-800/50 border-gray-700/50 hover:bg-gray-800/70"
                : "bg-white/70 border-white/50 hover:bg-white/90"
            } ${card.bgColor}`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className={`text-sm font-medium ${darkMode ? "text-gray-400" : "text-gray-600"}`}>
                  {card.title}
                </p>
                <p className={`text-2xl font-bold mt-2 ${
                  card.title === "Remaining" && totalRemaining < 0
                    ? "text-red-500"
                    : card.title === "Remaining"
                    ? "text-[#8bc34a]"
                    : darkMode ? "text-white" : "text-gray-900"
                }`}>
                  ${formatCurrency(card.value)}
                </p>
              </div>
              <div className={`p-3 rounded-xl ${card.color} shadow-lg`}>
                <card.icon size={24} className="text-white" />
              </div>
            </div>
            <div className={`absolute bottom-0 left-0 right-0 h-1 ${card.color}`} />
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex justify-between items-center mb-6">
        <button
          onClick={handleToggleAllTables}
          className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 border ${
            darkMode
              ? "text-gray-300 hover:text-gray-100 border-gray-600 hover:bg-gray-700"
              : "text-gray-700 hover:text-gray-900 border-gray-300 hover:bg-gray-50"
          }`}
        >
          {showAllTables ? (
            <>
              <FaChevronUp className="text-sm" />
              <span>Collapse All Categories</span>
            </>
          ) : (
            <>
              <FaChevronDown className="text-sm" />
              <span>Expand All Categories</span>
            </>
          )}
        </button>
        <button
          onClick={handlePopupToggle}
          className={`flex items-center gap-2 px-6 py-3 text-white rounded-xl transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl ${
            darkMode
              ? "bg-gray-700 hover:bg-gray-600"
              : "bg-[#8bc34a] hover:bg-[#6ec122]"
          }`}
        >
          <FaPlus className="text-lg" />
          <span className="font-medium">Add Budget Item</span>
        </button>
      </div>

      {/* Popups */}
      {isPopupOpen && (
        <BudgetPopupComponent
          onSave={handleSaveBudget}
          onClose={handlePopupToggle}
          darkMode={darkMode}
        />
      )}

      {isEditBudgetPopupOpen && selectedBudgetItem && (
        <EditBudgetPopupComponent
          budgetItem={selectedBudgetItem}
          onSave={handleSaveEditBudget}
          onClose={() => setIsEditBudgetPopupOpen(false)}
          darkMode={darkMode}
        />
      )}

      {/* Budget Categories */}
      <div className="space-y-6">
        {/* Header */}
        <div className={`grid grid-cols-4 gap-4 p-4 rounded-2xl backdrop-blur-lg border font-semibold ${
          darkMode
            ? "bg-gray-800/50 border-gray-700/50 text-gray-200"
            : "bg-white/70 border-white/50 text-gray-700"
        } shadow-lg`}>
          <span className="text-left pl-3">Category</span>
          <span className="text-center">Budget</span>
          <span className="text-center">Actual</span>
          <span className="text-center">Remaining</span>
        </div>
        {budgetData.length > 0 ? (
          budgetData.map((category) => {
            const nonZeroSubs = category.subcategories.filter(
              (sub) => sub.allocated !== 0
            );
            const zeroSubs = category.subcategories.filter(
              (sub) => sub.allocated === 0
            );
            const isExpanded = showAllTables || (expandedCategories[category.categoryId] === true);
            const IconComponent = iconMapping[category.icon] || FaQuestionCircle;

            const categoryBudgetTotal = category.subcategories.reduce(
              (acc, sub) => acc + (sub.allocated || 0),
              0
            );
            const categoryActualTotal = category.subcategories.reduce(
              (acc, sub) => acc + (sub.actual || 0),
              0
            );
            const categoryRemainingTotal = category.subcategories.reduce(
              (acc, sub) => acc + (sub.remaining || 0),
              0
            );
            return (
              <div
                key={category.categoryId}
                className={`overflow-hidden rounded-2xl backdrop-blur-lg border transition-all duration-300 hover:shadow-xl ${
                  darkMode
                    ? "bg-gray-800/50 border-gray-700/50 hover:bg-gray-800/70"
                    : "bg-white/70 border-white/50 hover:bg-white/90"
                } shadow-lg`}
              >
                {/* Category Header */}
                <div
                  className={`grid grid-cols-4 gap-4 p-4 cursor-pointer transition-all duration-200 hover:bg-gradient-to-r ${
                    darkMode
                      ? "hover:from-gray-700/30 hover:to-gray-600/30"
                      : "hover:from-blue-50/50 hover:to-purple-50/50"
                  }`}
                  onClick={() => {
                    dispatch(toggleCategory(category.categoryId));
                    handleCategoryClick(category.categoryId);
                  }}
                >
                  <div className="flex items-center gap-3 font-semibold">
                    <div className={`p-2 rounded-lg ${
                      darkMode ? "bg-gray-700" : "bg-[#8bc34a]"
                    }`}>
                      <IconComponent
                        className="text-white"
                        style={{ fontSize: '16px' }}
                      />
                    </div>
                    <span>{category.categoryName || "Unknown"}</span>
                  </div>
                  <span className="text-center font-medium">
                    ${formatCurrency(categoryBudgetTotal)}
                  </span>
                  <span className="text-center font-medium">
                    ${formatCurrency(categoryActualTotal)}
                  </span>
                  <span
                    className={`text-center font-medium ${
                      categoryRemainingTotal < 0 ? "text-red-500" : "text-[#8bc34a]"
                    }`}
                  >
                    ${formatCurrency(categoryRemainingTotal)}
                  </span>
                </div>
                {/* Category Progress Bar */}
                {/* APT-210 - progress bar not showing when collapsed */}
                {/* {isExpanded && (
                  <div className="px-6">
                    <ProgressBar
                      spent={categoryActualTotal}
                      budget={categoryBudgetTotal}
                    />
                  </div>
                )} */}
                <div className="px-6">
                  <ProgressBar
                    spent={categoryActualTotal}
                    budget={categoryBudgetTotal}
                  />
                </div>
                {/* Subcategories */}
                {isExpanded && category.subcategories.length > 0 && (
                  <div className="border-t border-gray-200 dark:border-gray-700">
                    {/* Non-zero subcategories */}
                    {nonZeroSubs.map((sub) => {
                      const SubIconComponent = iconMapping[sub.icon] || FaQuestionCircle;
                      return (
                        <div key={sub.compositeKey}>
                          <div
                            className={`grid grid-cols-4 gap-4 p-4 transition-all duration-200 group hover:bg-gradient-to-r ${
                              darkMode
                                ? "hover:from-gray-700/30 hover:to-gray-600/30"
                                : "hover:from-blue-50/30 hover:to-purple-50/30"
                            } cursor-pointer`}
                            onClick={() => handleSubcategoryClick(category.categoryId, sub.subcategoryId)}
                            onDoubleClick={() => handleSubcategoryDoubleClick(category.categoryId, sub)}
                          >
                            <div className="flex items-center gap-3">
                              <div className="p-1.5 rounded-lg bg-gray-200 dark:bg-gray-600 opacity-75">
                                <SubIconComponent
                                  style={{
                                    color: iconColorMapping[sub.icon] || "#6b7280",
                                    fontSize: '14px'
                                  }}
                                />
                              </div>
                              <span className="font-medium opacity-75">
                                {sub.subcategoryName || sub.customSubCategoryName || "N/A"}
                              </span>
                            </div>
                            <span className="text-center opacity-75">${formatCurrency(sub.allocated ?? 0)}</span>
                            <span className="text-center opacity-75">${formatCurrency(sub.actual || 0)}</span>
                            <div className="flex items-center justify-center gap-2">
                              <span
                                className={`${
                                  sub.remaining < 0 ? "text-red-500" : "text-[#8bc34a]"
                                } font-medium opacity-75`}
                              >
                                ${formatCurrency(sub.remaining || 0)}
                              </span>
                              {sub.allocated !== 0 && (
                                <div
                                  className="text-center cursor-pointer hidden group-hover:block"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleCloseSubcategory(category.categoryId, sub.compositeKey);
                                  }}
                                >
                                  <FaTrashAlt />
                                </div>
                              )}
                            </div>
                          </div>
                          <div className={`px-6 py-2 bg-gradient-to-r ${
                            darkMode
                              ? "from-gray-700/20 to-gray-600/20"
                              : "from-gray-100/40 to-gray-200/40"
                          }`}>
                            <ProgressBar spent={sub.actual || 0} budget={sub.allocated || 0} />
                          </div>
                        </div>
                      );
                    })}
                    {/* Zero budget toggle */}
                    {zeroSubs.length > 0 && !showZeroBudget[category.categoryId] && (
                      <div
                        className="flex justify-center items-center p-4 text-blue-500 hover:text-blue-600 cursor-pointer transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                        onClick={() => dispatch(toggleZeroBudget(category.categoryId))}
                      >
                        <FaPlus className="mr-2" />
                        <span className="font-medium">Show zero budget items</span>
                      </div>
                    )}
                    {/* Zero budget subcategories */}
                    {zeroSubs.length > 0 && showZeroBudget[category.categoryId] && (
                      <>
                        {zeroSubs.map((sub) => {
                          const SubIconComponent = iconMapping[sub.icon] || FaQuestionCircle;
                          return (
                            <div key={sub.compositeKey}>
                              <div
                                className={`grid grid-cols-4 gap-4 p-4 transition-all duration-200 group hover:bg-gradient-to-r ${
                                  darkMode
                                    ? "hover:from-gray-700/30 hover:to-gray-600/30"
                                    : "hover:from-blue-50/30 hover:to-purple-50/30"
                                } cursor-pointer opacity-75`}
                                onClick={() => handleSubcategoryClick(category.categoryId, sub.subcategoryId)}
                                onDoubleClick={() => handleSubcategoryDoubleClick(category.categoryId, sub)}
                              >
                                <div className="flex items-center gap-3">
                                  <div className="p-1.5 rounded-lg bg-gray-100 dark:bg-gray-700">
                                    <SubIconComponent
                                      style={{
                                        color: iconColorMapping[sub.icon] || "#6b7280",
                                        fontSize: '14px'
                                      }}
                                    />
                                  </div>
                                  <span className="font-medium">
                                    {sub.subcategoryName || sub.customSubCategoryName || "N/A"}
                                  </span>
                                </div>
                                <span className="text-center">${formatCurrency(sub.allocated ?? 0)}</span>
                                <span className="text-center">${formatCurrency(sub.actual || 0)}</span>
                                <div className="flex items-center justify-center gap-2">
                                  <span
                                    className={`${
                                      sub.remaining < 0 ? "text-red-500" : "text-[#8bc34a]"
                                    } font-medium`}
                                  >
                                    ${formatCurrency(sub.remaining || 0)}
                                  </span>
                                  {sub.allocated !== 0 && (
                                    <div
                                      className="text-center cursor-pointer hidden group-hover:block"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleCloseSubcategory(category.categoryId, sub.compositeKey);
                                      }}
                                    >
                                      <FaTrashAlt />
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div className="px-6">
                                <ProgressBar spent={sub.actual || 0} budget={sub.allocated || 0} />
                              </div>
                            </div>
                          );
                        })}
                        <div
                          className="flex justify-center items-center p-4 text-blue-500 hover:text-blue-600 cursor-pointer transition-all duration-200 hover:bg-blue-50 dark:hover:bg-blue-900/20"
                          onClick={() => dispatch(toggleZeroBudget(category.categoryId))}
                        >
                          <FaMinus className="mr-2" />
                          <span className="font-medium">Hide zero budget items</span>
                        </div>
                      </>
                    )}
                  </div>
                )}
              </div>
            );
          })
        ) : (
          <div className={`text-center p-12 rounded-2xl backdrop-blur-lg border ${
            darkMode
              ? "bg-gray-800/50 border-gray-700/50 text-gray-400"
              : "bg-white/70 border-white/50 text-gray-600"
          } shadow-lg`}>
            <div className="text-6xl mb-4">📊</div>
            <p className="text-lg font-medium">No budget data available</p>
            <p className="text-sm opacity-75 mt-2">Start by adding your first budget category</p>
          </div>
        )}
      </div>
    </div>

    {/* AI Chat Component - Outside main content */}
      {showAIChat && (
        <GenericAIChat
          darkMode={darkMode}
          currentTheme={currentTheme}
          setShowAIChat={handleAIChatClose}
          showAIChat={showAIChat}
          title="Budget AI Assistant"
          subtitle="Budget insights & advice"
          welcomeMessage="👋 Hi! I'm your Budget AI Assistant. I can help you analyze your spending, optimize your budget, and provide financial insights."
          exampleQueries={[
            "Analyze my spending patterns",
            "How can I optimize my budget?",
            "Show me budget insights",
            "Give me savings tips",
            "Help me reduce expenses",
            "Budget recommendations"
          ]}
          placeholder="Ask about your budget, spending, or financial goals..."
          onQuerySent={(query) => {
            // Optional: Add any budget-specific logic when a query is sent
            console.log('Budget AI query sent:', query);
          }}
        />
      )}
    </div>
  );
};

export default Budget6;