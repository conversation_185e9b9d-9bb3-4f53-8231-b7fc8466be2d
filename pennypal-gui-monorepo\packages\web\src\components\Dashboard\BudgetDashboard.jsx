import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { fetchBudgetDashboardData } from '../../../../logic/redux/budgetDashboardSlice';
import { useCacheStatus } from '../../../../logic/hooks/useCacheStatus';
import { getCurrentUserId } from '../../utils/AuthUtil';
import { GripVertical, TrendingUp, DollarSign } from 'lucide-react';

// --- Card Design Components ---
const Card = ({ children, className = '', darkMode }) => (
  <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-sm border ${darkMode ? 'border-gray-700' : 'border-gray-200'} ${className}`}>
    {children}
  </div>
);

const CardHeader = ({ children, className = '', darkMode }) => (
  <div className={`px-6 py-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} ${className}`}>
    {children}
  </div>
);

const CardContent = ({ children, className = '' }) => (
  <div className={`px-6 py-4 ${className}`}>
    {children}
  </div>
);

const CardTitle = ({ children, className = '', darkMode }) => (
  <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} ${className}`}>
    {children}
  </h3>
);
// --- End Card Design ---

const BudgetDashboard = ({ darkMode }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const userId = getCurrentUserId();
  
  // Simple cache status check
  const cacheStatus = useCacheStatus('budgetSummary');

  // Fallback to component state
  const { summary: componentSummary, loading: componentLoading, error: componentError } = useSelector((state) => state.budgetDashboard || {});

  // Use cached data if available, otherwise use component data
  const summary = cacheStatus.isLoaded ? cacheStatus.data : componentSummary;
  // Only show loading if both cache and component are loading and no data is available
  const loading = (!summary) && (cacheStatus.isLoading || componentLoading);
  const error = cacheStatus.hasError ? cacheStatus.error : componentError;

  // Cache-aware data fetching with current month/year
  useEffect(() => {
    if (cacheStatus.isLoaded) {
      console.log('✅ Using cached budget summary data');
      return; // Already have cached data
    }

    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth() + 1; // JavaScript months are 0-indexed

    if (!cacheStatus.isLoading && !componentLoading) {
      console.log('🔄 Budget summary not cached, fetching data');
      dispatch(fetchBudgetDashboardData({
        year: currentYear,
        month: currentMonth
      }));
    }
  }, [dispatch]);

  const formatCurrency = (amount) =>
    new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount || 0);

  const getCurrentMonthName = () => {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[new Date().getMonth()]; // Use current month instead of hardcoded
  };

  const handleComponentClick = () => {
    navigate('/dashboard/budget');
  };

  // Calculate percentages for progress bars
  const calculateBudgetPercentage = () => {
    if (!summary || !summary.totalBudget || summary.totalBudget === 0) return 0;
    return Math.min(100, (summary.actualBudget / summary.totalBudget) * 100);
  };

  const calculateIncomePercentage = () => {
    if (!summary || !summary.actualIncome || !summary.actualBudget) return 0;
    const targetIncome = summary.actualBudget * 1.5; // Example target
    return Math.min(100, (summary.actualIncome / targetIncome) * 100);
  };

  if (loading) {
    return (
      <Card className="dashboard-card" darkMode={darkMode}>
        <CardContent>
          <div className="flex justify-center items-center h-24">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#7fe029]"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="dashboard-card" darkMode={darkMode}>
        <CardContent>
          <div className={`${darkMode ? 'bg-red-900 border-red-700 text-red-300' : 'bg-red-50 border-red-500 text-red-700'} border-l-4 p-4 rounded-md`}>
            {error}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!summary) {
    return (
      <Card className="dashboard-card" darkMode={darkMode}>
        <CardContent>
          <div className="flex flex-col items-center justify-center space-y-4">
            <div className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>No budget data available</div>
            <button 
              onClick={() => {
                const today = new Date();
                const currentYear = today.getFullYear();
                const currentMonth = today.getMonth() + 1;
                dispatch(fetchBudgetDashboardData({ year: currentYear, month: currentMonth }));
              }}
              className="px-4 py-2 rounded-full bg-[#7fe029] text-white text-sm font-medium hover:bg-[#6ec122] transition-colors"
            >
              Retry
            </button>
          </div>
        </CardContent>
      </Card>
    );
  }

  const budgetUsagePercentage = calculateBudgetPercentage();
  const incomePercentage = calculateIncomePercentage();

  return (
    <Card className="dashboard-card cursor-pointer" onClick={handleComponentClick} darkMode={darkMode}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4" darkMode={darkMode}>
        <CardTitle darkMode={darkMode}>{getCurrentMonthName()} Budget</CardTitle>
        <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Budget Progress Section */}
          <div className="space-y-3">
            <div className={`flex items-center justify-between p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${darkMode ? 'bg-blue-900' : 'bg-blue-100'}`}>
                  <DollarSign className={`h-4 w-4 ${darkMode ? 'text-blue-400' : 'text-blue-600'}`} />
                </div>
                <div>
                  <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Budget Usage</p>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    {budgetUsagePercentage.toFixed(1)}% of total budget
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {formatCurrency(summary.actualBudget)}
                </p>
                <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  of {formatCurrency(summary.totalBudget)}
                </p>
              </div>
            </div>
            
            {/* Progress Bar */}
            <div className={`w-full rounded-full h-2.5 ${darkMode ? 'bg-gray-600' : 'bg-gray-200'}`}>
              <div
                className={`h-2.5 rounded-full transition-all duration-300 ${
                  budgetUsagePercentage > 85
                    ? 'bg-red-500'
                    : budgetUsagePercentage > 65
                    ? 'bg-yellow-500'
                    : 'bg-green-500'
                }`}
                style={{ width: `${budgetUsagePercentage}%` }}
              ></div>
            </div>
          </div>

          {/* Income Progress Section */}
          <div className="space-y-3">
            <div className={`flex items-center justify-between p-3 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
              <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center ${darkMode ? 'bg-green-900' : 'bg-green-100'}`}>
                  <TrendingUp className={`h-4 w-4 ${darkMode ? 'text-green-400' : 'text-green-600'}`} />
                </div>
                <div>
                  <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>Income Progress</p>
                  <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                    {incomePercentage.toFixed(1)}% of target
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                  {formatCurrency(summary.actualIncome)}
                </p>
                <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                  Target: {formatCurrency(summary.actualBudget * 1.5)}
                </p>
              </div>
            </div>
            
            {/* Progress Bar */}
            <div className={`w-full rounded-full h-2.5 ${darkMode ? 'bg-gray-600' : 'bg-gray-200'}`}>
              <div
                className="h-2.5 rounded-full bg-blue-500 transition-all duration-300"
                style={{ width: `${incomePercentage}%` }}
              ></div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default BudgetDashboard;
// import React, { useEffect } from 'react';
// import { useSelector, useDispatch } from 'react-redux';
// import { useNavigate } from 'react-router-dom';
// import { fetchBudgetDashboardData } from '../../../../logic/redux/budgetDashboardSlice';
// import { GripVertical, Plus, Minus } from 'lucide-react';
// import { useCacheStatus } from '../../../../logic/hooks/useCacheStatus';

// // --- Card Design ---
// const Card = ({ children, className = '', onClick }) => (
//   <div 
//     className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${onClick ? 'cursor-pointer hover:shadow-md transition-shadow duration-200' : ''} ${className}`}
//     onClick={onClick}
//   >
//     {children}
//   </div>
// );

// const CardHeader = ({ children, className = '' }) => (
//   <div className={`px-6 py-4 border-b border-gray-200 dark:border-gray-700 ${className}`}>
//     {children}
//   </div>
// );

// const CardContent = ({ children, className = '' }) => (
//   <div className={`px-6 py-4 ${className}`}>
//     {children}
//   </div>
// );

// const CardTitle = ({ children, className = '' }) => (
//   <h3 className={`text-lg font-semibold text-gray-900 dark:text-white ${className}`}>
//     {children}
//   </h3>
// );
// // --- End Card Design ---

// const BudgetDashboard = ({ darkMode }) => {
//   const dispatch = useDispatch();
//   const navigate = useNavigate();

//   const userId = useSelector((state) => state.auth.userId);
//   // const { summary, loading, error } = useSelector((state) => state.budgetDashboard || {});
//   // console.log('actualBudget:', summary?.actualBudget);
//   // console.log('totalBudget:', summary?.totalBudget);

//   // Simple cache status check
//   const cacheStatus = useCacheStatus('budgetSummary');

//   // Fallback to component state
//   const { summary: componentSummary, loading: componentLoading, error: componentError } = useSelector((state) => state.budgetDashboard || {});

//   // Use cached data if available, otherwise use component data
//   const summary = cacheStatus.isLoaded ? cacheStatus.data : componentSummary;
//   // Only show loading if both cache and component are loading and no data is available
//   const loading = (!summary) && (cacheStatus.isLoading || componentLoading);
//   const error = cacheStatus.hasError ? cacheStatus.error : componentError;

//   // Cache-aware data fetching
//   useEffect(() => {
//     if (cacheStatus.isLoaded) {
//       console.log('✅ Using cached budget summary data');
//       return; // Already have cached data
//     }

//     const today = new Date();
//     const currentYear = today.getFullYear();
//     const currentMonth = today.getMonth() + 1; // JavaScript months are 0-indexed

//     if (!cacheStatus.isLoading && !componentLoading) {
//       console.log('🔄 Budget summary not cached, fetching data');
//       dispatch(fetchBudgetDashboardData({
//         year: currentYear,
//         month: currentMonth
//       }));
//     }
//   }, [dispatch]);


//   const formatCurrency = (amount) =>
//     new Intl.NumberFormat('en-US', {
//       style: 'currency',
//       currency: 'USD',
//       minimumFractionDigits: 2
//     }).format(amount || 0);

//   const getCurrentMonthName = () => {
//     const months = [
//       'January', 'February', 'March', 'April', 'May', 'June',
//       'July', 'August', 'September', 'October', 'November', 'December'
//     ];
//     return months[new Date().getMonth()]; // Hardcoded to February (index 1) to match the month in the API call
//   };

//   const handleComponentClick = () => {
//     navigate('/dashboard/budget');
//   };

//   // Calculate percentages for progress bars
//   const calculateBudgetPercentage = () => {
//     if (!summary || !summary.totalBudget || summary.totalBudget === 0) return 0;
//     return Math.min(100, (summary.actualBudget / summary.totalBudget) * 100);
//   };

//   const calculateIncomePercentage = () => {
//     if (!summary || !summary.actualIncome || !summary.actualBudget) return 0;
//     const targetIncome = summary.actualBudget * 1.5; // Example target
//     return Math.min(100, (summary.actualIncome / targetIncome) * 100);
//   };

//   if (loading) {
//     return <div className="h-full flex items-center justify-center">Loading budget data...</div>;
//   }

//   if (error) {
//     return <div className="h-full flex items-center justify-center text-red-500">{error}</div>;
//   }

//   if (!summary) {
//     return (
//       <div className="h-full flex flex-col items-center justify-center">
//         <div className={`${ darkMode ? 'text-white' : ' text-black'}`}>No budget data available</div>
//         <button 
//           onClick={() => {
//             const today = new Date();
//   const currentYear = today.getFullYear();
//   const currentMonth = today.getMonth() + 1;
//   dispatch(fetchBudgetDashboardData({ year: currentYear, month: currentMonth }));
//           }}
//           className={`${
//             darkMode 
//               ? 'bg-gray-700 hover:bg-gray-600 text-white' 
//               : 'bg-[#8bc34a] hover:bg-[#6ec122] text-white'
//           } py-2 px-4 rounded flex items-center`} >
//           Retry
//         </button>
//       </div>
//     );
//   }

//   const budgetUsagePercentage = calculateBudgetPercentage();
//   const incomePercentage = calculateIncomePercentage();
//   const netIncome = (summary.actualIncome || 0) - (summary.actualBudget || 0);

//   return (
//     <Card className="dashboard-card" onClick={handleComponentClick}>
//       <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
//         <CardTitle className="text-lg font-semibold">{getCurrentMonthName()} Budget</CardTitle>
//         <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
//       </CardHeader>
//       <CardContent>
//         <div className="space-y-6">
//           {/* Income vs Expenses */}
//           <div className="grid grid-cols-2 gap-4">
//             <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4">
//               <div className="flex items-center justify-between">
//                 <div>
//                   <p className="text-sm font-medium text-green-600 dark:text-green-400">Actual Income</p>
//                   <p className="text-2xl font-bold text-green-700 dark:text-green-300">
//                     {formatCurrency(summary.actualIncome)}
//                   </p>
//                 </div>
//                 <Plus className="h-8 w-8 text-green-600" />
//               </div>
//             </div>
//             <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4">
//               <div className="flex items-center justify-between">
//                 <div>
//                   <p className="text-sm font-medium text-red-600 dark:text-red-400">Actual Expenses</p>
//                   <p className="text-2xl font-bold text-red-700 dark:text-red-300">
//                     {formatCurrency(summary.actualBudget)}
//                   </p>
//                 </div>
//                 <Minus className="h-8 w-8 text-red-600" />
//               </div>
//             </div>
//           </div>
//           {/* Net Income */}
//           <div className={`rounded-lg p-4 ${netIncome >= 0 ? 'bg-green-50 dark:bg-green-900/20' : 'bg-red-50 dark:bg-red-900/20'}`}>
//             <div className="flex items-center justify-between">
//               <div>
//                 <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Net Income</p>
//                 <p className={`text-2xl font-bold ${netIncome >= 0 ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'}`}>
//                   {netIncome >= 0 ? '+' : '-'}{formatCurrency(Math.abs(netIncome))}
//                 </p>
//               </div>
//               <div className={`flex items-center ${netIncome >= 0 ? 'text-green-600' : 'text-red-600'}`}>
//                 {netIncome >= 0 ? <Plus className="h-8 w-8" /> : <Minus className="h-8 w-8" />}
//               </div>
//             </div>
//           </div>
//           {/* Budget Progress */}
//         <div className="w-full bg-gray-200 rounded-full h-2">
//   <div
//     className={`h-2 rounded-full transition-all duration-300 ${
//       budgetUsagePercentage > 90 ? 'bg-red-500' : budgetUsagePercentage > 70 ? 'bg-yellow-500' : 'bg-green-500'
//     }`}
//     style={{ width: `${Math.min(budgetUsagePercentage, 100)}%` }}
//   />
//         </div>
//         <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
//           {formatCurrency(summary.actualBudget)} used / {formatCurrency(summary.totalBudget)} total
//         </div>

//         </div>
//       </CardContent>
//     </Card>
//   );
// };

// export default BudgetDashboard;
