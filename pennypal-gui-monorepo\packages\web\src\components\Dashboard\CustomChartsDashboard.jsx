import React, { useState, useEffect, useRef } from 'react';
import { DndContext, useSensor, useSensors, PointerSensor } from '@dnd-kit/core';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { SortableContext, rectSortingStrategy } from '@dnd-kit/sortable';
import ChatbotVisualizer from '../v1/SideNav/ChatbotVisualizer';
import { Download, Pencil, Loader2, Trash2 } from 'lucide-react';
import * as XLSX from 'xlsx';
import html2canvas from 'html2canvas';
import { axiosInstance } from 'logic/api/axiosConfig';
import { getCurrentUserId } from '../../utils/AuthUtil';
import { themeClasses } from '../../utils/tailwindUtils'; // Adjust path as neededimport PennyLogoDrawing from '../path/to/PennyLogoDrawing'; // Adjust path as needed
import PaymentLoader from '../load/PaymentLoader'; // Adjust path as needed
import { fixColorsForCanvas, debugElementColors, simpleFixColorsForCanvas } from '../../utils/colorUtils';

// Color constants
const COLORS = {
  shadow: 'rgba(127, 224, 41, 0.15)'
};

// DraggableChart component
const DraggableChart = ({ id, children, darkMode }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 10 : 1,
    height: '100%',
  };

  return (
    <div ref={setNodeRef} style={style} {...attributes} {...listeners}>
      <div
        className={`rounded-xl shadow-lg p-5 ml-5 mr-5 border-l-4 h-full cursor-move mb-4 ${
          darkMode ? 'bg-gray-800 border-gray-600 text-gray-100' : 'bg-white border-[#7fe029] text-black'
        }`}
        style={{
          boxShadow: `0 4px 12px ${darkMode ? 'rgba(0,0,0,0.2)' : COLORS.shadow}`,
        }}
      >
        {children}
      </div>
    </div>
  );
};

// CustomChartsSection component
const CustomChartsSection = ({ darkMode }) => {
  const userId = getCurrentUserId();
  const [customCharts, setCustomCharts] = useState([]);
  const [editingChartId, setEditingChartId] = useState(null);
  const [editingTitle, setEditingTitle] = useState('');
  const [savingChartId, setSavingChartId] = useState(null);
  const [deletingChartId, setDeletingChartId] = useState(null);
  const [loadingCharts, setLoadingCharts] = useState(false);
  const [error, setError] = useState(null);

  const chartRefs = useRef({});

  // Load saved chart positions or use defaults
  const [chartPositions, setChartPositions] = useState(() => {
    const savedLayout = localStorage.getItem('customChartsLayout');
    console.log('savedLayout', savedLayout);
    return savedLayout ? JSON.parse(savedLayout) : [];
  });

  // Save chart positions to localStorage
  useEffect(() => {
    localStorage.setItem('customChartsLayout', JSON.stringify(chartPositions));
  }, [chartPositions]);

  // Fetch custom charts
  useEffect(() => {
    if (!userId) return;

    const fetchCustomCharts = async () => {
      setLoadingCharts(true);
      setError(null);
      try {
        const response = await axiosInstance.get(`pennypal/api/v1/charts/user/${userId}`);
        
        const charts = response.data.map(chart => ({
          ...chart,
          data: JSON.parse(chart.data),
        }));

        setCustomCharts(charts);

        // Initialize chartRefs for each chart
        charts.forEach(chart => {
          chartRefs.current[chart.id] = React.createRef();
        });

        // Sync chartPositions with any new charts
        setChartPositions(prevPositions => {
          const existingIds = new Set(prevPositions.map(p => p.id));
          const newCharts = charts.filter(chart => !existingIds.has(chart.id));

          const newPositions = newCharts.map((chart, index) => ({
            id: chart.id,
            position: `chart-${prevPositions.length + index}`,
          }));

          const updated = [...prevPositions, ...newPositions];
          localStorage.setItem('customChartsLayout', JSON.stringify(updated));
          return updated;
        });
      } catch (err) {
        setCustomCharts([]);
        setError('Failed to fetch charts');
      } finally {
        setLoadingCharts(false);
      }
    };

    fetchCustomCharts();
  }, [userId]);

  // Start editing chart title
  const startEditingTitle = (chart) => {
    setEditingChartId(chart.id);
    setEditingTitle(chart.title || '');
  };

  // Save edited chart title
  const saveTitleEdit = async (chartId) => {
    if (!editingTitle.trim()) return;

    if (editingTitle === customCharts.find(chart => chart.id === chartId).title) {
      setEditingChartId(null);
      setEditingTitle('');
      return;
    }

    try {
      setSavingChartId(chartId);
      const updatedCharts = customCharts.map(chart =>
        chart.id === chartId ? { ...chart, title: editingTitle } : chart
      );
      setCustomCharts(updatedCharts);

      await axiosInstance.put(`pennypal/api/v1/charts/update/${chartId}`, {
        title: editingTitle,
      });
    } catch (error) {
      console.error('Failed to update chart title:', error);
      alert('Failed to update chart title');
    } finally {
      setEditingChartId(null);
      setEditingTitle('');
      setSavingChartId(null);
    }
  };

  // Delete chart
  const handleDeleteChart = async (chartId) => {
    try {
      setDeletingChartId(chartId);
      await axiosInstance.delete(`pennypal/api/v1/charts/delete/${chartId}`);
      setCustomCharts(prevCharts => prevCharts.filter(chart => chart.id !== chartId));
    } catch (error) {
      console.error('Failed to delete chart:', error);
      alert('Failed to delete chart');
    } finally {
      setDeletingChartId(null);
    }
  };

  // Fix colors for download - convert oklch and other unsupported colors to hex
  const fixColors = (element) => {
    const allElements = element.querySelectorAll('*');

    // Color mapping for common cases
    const colorMap = {
      // Text colors
      'text-gray-900': darkMode ? '#f3f4f6' : '#111827',
      'text-gray-800': darkMode ? '#f9fafb' : '#1f2937',
      'text-gray-700': darkMode ? '#d1d5db' : '#374151',
      'text-gray-600': darkMode ? '#9ca3af' : '#4b5563',
      'text-gray-500': darkMode ? '#6b7280' : '#6b7280',
      'text-gray-400': darkMode ? '#4b5563' : '#9ca3af',
      'text-gray-300': darkMode ? '#374151' : '#d1d5db',
      'text-gray-200': darkMode ? '#1f2937' : '#e5e7eb',
      'text-gray-100': darkMode ? '#111827' : '#f3f4f6',
      'text-white': '#ffffff',
      'text-black': '#000000',

      // Background colors
      'bg-white': '#ffffff',
      'bg-gray-900': '#111827',
      'bg-gray-800': '#1f2937',
      'bg-gray-700': '#374151',
      'bg-gray-600': '#4b5563',
      'bg-gray-500': '#6b7280',
      'bg-gray-400': '#9ca3af',
      'bg-gray-300': '#d1d5db',
      'bg-gray-200': '#e5e7eb',
      'bg-gray-100': '#f3f4f6',
      'bg-gray-50': '#f9fafb',

      // Fill colors for SVG
      'fill-gray-900': darkMode ? '#f3f4f6' : '#111827',
      'fill-gray-800': darkMode ? '#f9fafb' : '#1f2937',
      'fill-gray-700': darkMode ? '#d1d5db' : '#374151',
      'fill-gray-600': darkMode ? '#9ca3af' : '#4b5563',
      'fill-gray-500': darkMode ? '#6b7280' : '#6b7280',
      'fill-gray-400': darkMode ? '#4b5563' : '#9ca3af',
      'fill-gray-300': darkMode ? '#374151' : '#d1d5db',
      'fill-gray-200': darkMode ? '#1f2937' : '#e5e7eb',
      'fill-gray-100': darkMode ? '#111827' : '#f3f4f6',
      'fill-white': '#ffffff',
      'fill-black': '#000000',

      // Stroke colors for SVG
      'stroke-gray-600': darkMode ? '#9ca3af' : '#4b5563',
      'stroke-gray-400': darkMode ? '#4b5563' : '#9ca3af',
      'stroke-gray-300': darkMode ? '#374151' : '#d1d5db',
      'stroke-black': '#000000',
      'stroke-white': '#ffffff'
    };

    allElements.forEach((el) => {
      const computedStyle = getComputedStyle(el);

      // Handle computed style colors
      const color = computedStyle.color;
      const bgColor = computedStyle.backgroundColor;
      const fill = computedStyle.fill;
      const stroke = computedStyle.stroke;

      // Fix oklch and other unsupported color functions
      if (color && (color.includes('oklch') || color.includes('color(') || color.includes('lab(') || color.includes('lch('))) {
        el.style.color = darkMode ? '#f3f4f6' : '#111827'; // Default text color
      }
      if (bgColor && (bgColor.includes('oklch') || bgColor.includes('color(') || bgColor.includes('lab(') || bgColor.includes('lch('))) {
        el.style.backgroundColor = darkMode ? '#1f2937' : '#ffffff'; // Default background color
      }
      if (fill && fill !== 'none' && (fill.includes('oklch') || fill.includes('color(') || fill.includes('lab(') || fill.includes('lch('))) {
        el.style.fill = darkMode ? '#f3f4f6' : '#111827'; // Default fill color
      }
      if (stroke && stroke !== 'none' && (stroke.includes('oklch') || stroke.includes('color(') || stroke.includes('lab(') || stroke.includes('lch('))) {
        el.style.stroke = darkMode ? '#9ca3af' : '#4b5563'; // Default stroke color
      }

      // Handle inline style colors
      if (el.style.color && (el.style.color.includes('oklch') || el.style.color.includes('color(') || el.style.color.includes('lab(') || el.style.color.includes('lch('))) {
        el.style.color = darkMode ? '#f3f4f6' : '#111827';
      }
      if (el.style.backgroundColor && (el.style.backgroundColor.includes('oklch') || el.style.backgroundColor.includes('color(') || el.style.backgroundColor.includes('lab(') || el.style.backgroundColor.includes('lch('))) {
        el.style.backgroundColor = darkMode ? '#1f2937' : '#ffffff';
      }
      if (el.style.fill && el.style.fill !== 'none' && (el.style.fill.includes('oklch') || el.style.fill.includes('color(') || el.style.fill.includes('lab(') || el.style.fill.includes('lch('))) {
        el.style.fill = darkMode ? '#f3f4f6' : '#111827';
      }
      if (el.style.stroke && el.style.stroke !== 'none' && (el.style.stroke.includes('oklch') || el.style.stroke.includes('color(') || el.style.stroke.includes('lab(') || el.style.stroke.includes('lch('))) {
        el.style.stroke = darkMode ? '#9ca3af' : '#4b5563';
      }

      // Also check class names and apply appropriate colors
      const classList = Array.from(el.classList);
      classList.forEach(className => {
        if (colorMap[className]) {
          if (className.startsWith('text-')) {
            el.style.color = colorMap[className];
          } else if (className.startsWith('bg-')) {
            el.style.backgroundColor = colorMap[className];
          } else if (className.startsWith('fill-')) {
            el.style.fill = colorMap[className];
          } else if (className.startsWith('stroke-')) {
            el.style.stroke = colorMap[className];
          }
        }
      });
    });
  };

  // Download chart
  const downloadChart = async (chartId) => {
    const chart = customCharts.find(c => c.id === chartId);
    const visualRef = chartRefs.current[chartId];

    if (!visualRef?.current) return;

    if (chart.type === 'table') {
      const worksheet = XLSX.utils.json_to_sheet(chart.data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");
      XLSX.writeFile(workbook, `${chart.title || 'table'}.xlsx`);
    } else if (visualRef.current) {
      const clone = visualRef.current.cloneNode(true);
      document.body.appendChild(clone);
      clone.style.position = 'absolute';
      clone.style.left = '-9999px';
      clone.style.pointerEvents = 'none';
      clone.style.width = visualRef.current.offsetWidth + 'px';
      clone.style.height = visualRef.current.offsetHeight + 'px';

      // Apply color fixes to prevent oklch errors
      try {
        // Debug colors before fixing (optional)
        try {
          console.log('Debugging colors before fixes:');
          debugElementColors(clone);
        } catch (debugError) {
          console.warn('Debug function failed, continuing with color fixes:', debugError);
        }

        try {
          fixColorsForCanvas(clone, darkMode);
          console.log('Advanced color fixes applied successfully');
        } catch (advancedError) {
          console.warn('Advanced color fixes failed, trying simple fix:', advancedError);
          simpleFixColorsForCanvas(clone, darkMode);
          console.log('Simple color fixes applied as fallback');
        }

        // Debug colors after fixing (optional)
        try {
          console.log('Debugging colors after fixes:');
          debugElementColors(clone);
        } catch (debugError) {
          console.warn('Post-fix debug failed, but color fixes were applied:', debugError);
        }
      } catch (colorError) {
        console.warn('Error applying color fixes:', colorError);
      }

      try {
        const canvas = await html2canvas(clone, {
          scale: 2,
          logging: false,
          useCORS: true,
          allowTaint: true,
          backgroundColor: darkMode ? '#1f2937' : '#ffffff',
          onclone: (clonedDoc) => {
            // Additional color fixes on the cloned document
            try {
              const clonedElements = clonedDoc.querySelectorAll('*');
              clonedElements.forEach(el => {
                const style = el.style;
                // Force replace any remaining oklch colors
                if (style.color && style.color.includes('oklch')) {
                  style.color = darkMode ? '#f3f4f6' : '#111827';
                }
                if (style.backgroundColor && style.backgroundColor.includes('oklch')) {
                  style.backgroundColor = darkMode ? '#1f2937' : '#ffffff';
                }
                if (style.fill && style.fill.includes('oklch')) {
                  style.fill = darkMode ? '#f3f4f6' : '#111827';
                }
                if (style.stroke && style.stroke.includes('oklch')) {
                  style.stroke = darkMode ? '#9ca3af' : '#4b5563';
                }
              });
            } catch (oncloneError) {
              console.warn('Error in onclone color fixes:', oncloneError);
            }
          }
        });
        const link = document.createElement('a');
        link.download = `${chart.title || 'chart'}.png`;
        link.href = canvas.toDataURL('image/png');
        link.click();
        console.log('Chart downloaded successfully');
      } catch (error) {
        console.error('Error generating canvas:', error);
        // Show user-friendly error message
        alert('Error downloading chart. This might be due to unsupported color formats. Please try again or contact support.');
      } finally {
        document.body.removeChild(clone);
      }
    }
  };

  // Configure sensors for drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    })
  );

  // Handle drag end event
  const handleDragEnd = (event) => {
    const { active, over } = event;

    if (active.id !== over.id) {
      setChartPositions((positions) => {
        const activeIndex = positions.findIndex(pos => pos.id === active.id);
        const overIndex = positions.findIndex(pos => pos.id === over.id);

        const newPositions = [...positions];
        const activePos = { ...newPositions[activeIndex] };
        const overPos = { ...newPositions[overIndex] };

        const tempPosition = activePos.position;
        activePos.position = overPos.position;
        overPos.position = tempPosition;

        newPositions[activeIndex] = overPos;
        newPositions[overIndex] = activePos;

        localStorage.setItem('customChartsLayout', JSON.stringify(newPositions));
        return newPositions;
      });
    }
  };

  // Reset layout to default
  const resetLayout = () => {
    const newPositions = customCharts.map((chart, index) => ({
      id: chart.id,
      position: `chart-${index}`
    }));
    setChartPositions(newPositions);
  };

  // Get chart for position
  const getChartForPosition = (position) => {
    const pos = chartPositions.find(pos => pos.position === position);
    return pos ? customCharts.find(chart => chart.id === pos.id) : null;
  };

  if (loadingCharts) {
    return (
      <div className={`min-h-screen w-full p-5 flex flex-col justify-center items-center ${themeClasses.container(darkMode)}`}>
        <PaymentLoader />
        {/* <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Loading charts...
        </p> */}
      </div>
    );
  }

  if (error) {
    return (
      <div className={`w-full max-w-4xl mx-auto p-4 ${themeClasses.container(darkMode)}`}>
        <div className={`border px-4 py-3 rounded relative ${themeClasses.error(darkMode)}`} role="alert">
          <strong className={`font-bold ${darkMode ? 'text-red-400' : 'text-red-700'}`}>Error:</strong>
          <span className={`block sm:inline ${darkMode ? 'text-red-300' : 'text-red-700'}`}> {error}</span>
        </div>
      </div>
    );
  }

  return (
    <section className="">
      <div className="flex justify-between items-center p-5 mb-6">
        {/* <h1 className={`text-2xl `}>Custom AI Charts</h1> */}
        {/* <button
          className={`py-2 px-4 rounded flex items-center text-sm transition-colors duration-300 ${
            darkMode
              ? 'bg-gray-700 hover:bg-gray-600 text-white'
              : 'bg-[#8bc34a] hover:bg-[#6ec122] text-white'
          }`}
          onClick={resetLayout}
        >
          Reset Charts Layout
        </button> */}
      </div>

      {!loadingCharts && customCharts.length === 0 && (
        <p className={`text-sm ${themeClasses.noChartsText(darkMode)}`}>No charts found.</p>
      )}

      <DndContext sensors={sensors} onDragEnd={handleDragEnd}>
        <SortableContext items={chartPositions.map(pos => pos.id)} strategy={rectSortingStrategy}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {chartPositions.map((pos, index) => {
              const chart = getChartForPosition(pos.position);
              if (!chart) return null;

              const isEditing = editingChartId === chart.id;

              return (
                <DraggableChart key={chart.id} id={chart.id} darkMode={darkMode}>
                  <div className="relative">
                    <div className="absolute right-4 z-10 flex gap-2 items-center">
                      <button
                        onClick={() => {
                          if (window.confirm('Are you sure you want to delete this chart?')) {
                            handleDeleteChart(chart.id);
                          }
                        }}
                        className={`text-xs px-3 py-1 rounded ${
                          deletingChartId === chart.id
                            ? themeClasses.actionButtonDisabled(darkMode)
                            : themeClasses.deleteButton(darkMode)
                        }`}
                        disabled={deletingChartId === chart.id}
                      >
                        {deletingChartId === chart.id ? (
                          <svg className="animate-spin h-4 w-4 mx-auto" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                            <path
                              className="opacity-75"
                              fill="currentColor"
                              d="M4 12a8 8 0 018-8v4l3-3-3-3v4a8 8 0 000 16v-4l-3 3 3 3v-4a8 8 0 01-8-8z"
                            />
                          </svg>
                        ) : (
                          <Trash2 className="h-4 w-4" />
                        )}
                      </button>
                      <button
                        onClick={() => downloadChart(chart.id)}
                        className={`px-2 py-2 rounded ${themeClasses.downloadButton(darkMode)}`}
                        title="Download Chart"
                      >
                        <Download className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="mb-2 flex items-center gap-2">
                      {isEditing ? (
                        <div className="flex items-center gap-2">
                          <input
                            type="text"
                            value={editingTitle}
                            autoFocus
                            onChange={(e) => setEditingTitle(e.target.value)}
                            onBlur={() => saveTitleEdit(chart.id)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                saveTitleEdit(chart.id);
                              }
                            }}
                            className={`text-base font-semibold rounded-md px-1 py-1 border focus:outline-none ${themeClasses.inputField(darkMode)} ${themeClasses.inputFocusRing(darkMode)}`}
                          />
                          {savingChartId === chart.id && (
                            <Loader2 className="animate-spin w-4 h-4 text-blue-500" />
                          )}
                        </div>
                      ) : (
                        <div className="flex items-center gap-1">
                          <h3 className={`text-lg font-semibold ${themeClasses.chartTitle(darkMode)}`}>{chart.title || 'Placeholder Title'}</h3>
                          <button
                            onClick={() => startEditingTitle(chart)}
                            className={`${themeClasses.editButton(darkMode)}`}
                            disabled={savingChartId === chart.id}
                          >
                            {savingChartId === chart.id ? (
                              <Loader2 className="animate-spin w-4 h-4 text-blue-500" />
                            ) : (
                              <Pencil className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      )}
                    </div>

                    <div ref={chartRefs.current[chart.id]} id={`chart-${chart.id}`}>
                      <ChatbotVisualizer
                        visualization={chart}
                        userId={userId}
                        hideTitle={true}
                        hideSaveButton={true}
                        hideDownloadButton={true}
                      />
                    </div>
                  </div>
                </DraggableChart>
              );
            })}
          </div>
        </SortableContext>
      </DndContext>
    </section>
  );
};

export default CustomChartsSection;