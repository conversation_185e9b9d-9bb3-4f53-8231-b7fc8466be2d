// import React, { useState, useCallback, useRef, useEffect } from 'react';
// import { DndProvider, useDrag, useDrop } from 'react-dnd';
// import { HTML5Backend } from 'react-dnd-html5-backend';
// import { 
//   GripVertical, 
//   TrendingUp, 
//   TrendingDown, 
//   DollarSign, 
//   Wallet, 
//   CreditCard, 
//   PiggyBank, 
//   Target,
//   Clock,
//   Search,
//   Bell,
//   Settings,
//   Download,
//   Calendar,
//   ArrowUpRight,
//   ArrowDownRight,
//   MoreHorizontal,
//   ChevronRight,
//   Users,
//   Activity,
//   Zap,
//   RefreshCcw,
//   Play,
//   Pause,
//   Plus,
//   Minus,
//   TrendingUpIcon
// } from 'lucide-react';
// import { PieChart, Pie, Cell, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
// import NetworkGraphCard from './NetworkGraphCard';
// import RecurringTransactionsCard from './RecurringTransactionsCard';
// import BudgetDashboard from './BudgetDashboard';
// import RecentTransactions from './RecentTransactions';
// import FinancialGoals from './FinancialGoals';
// import InvestmentCard from './InvestmentCard';
// import SpendingDashboard from './SpendingDashboard';
// import DashboardNetWorthAreaChart from './DashboardNetWorthAreaChart';
// import CustomChartsSection from './CustomChartsDashboard';
// import PaymentLoader from '../load/PaymentLoader'; // Add this import

// // Utility functions
// const formatCurrency = (amount) => {
//   const num = parseFloat(amount);
//   return new Intl.NumberFormat('en-US', {
//     style: 'currency',
//     currency: 'USD'
//   }).format(Math.abs(num));
// };

// const formatDate = (date) => {
//   return new Date(date).toLocaleDateString('en-US', { 
//     month: 'short', 
//     day: 'numeric' 
//   });
// };

// // Theme classes helper
// const themeClasses = {
//   container: (darkMode) => darkMode ? 'bg-gray-900' : 'bg-gray-50'
// };

// // Card component
// const Card = ({ children, className = '' }) => (
//   <div className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 ${className}`}>
//     {children}
//   </div>
// );

// const CardHeader = ({ children, className = '' }) => (
//   <div className={`px-6 py-4 border-b border-gray-200 dark:border-gray-700 ${className}`}>
//     {children}
//   </div>
// );

// const CardContent = ({ children, className = '' }) => (
//   <div className={`px-6 py-4 ${className}`}>
//     {children}
//   </div>
// );

// const CardTitle = ({ children, className = '' }) => (
//   <h3 className={`text-lg font-semibold text-gray-900 dark:text-white ${className}`}>
//     {children}
//   </h3>
// );

// // Button component
// const Button = ({ children, variant = 'default', size = 'default', className = '', onClick, ...props }) => {
//   const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
  
//   const variants = {
//     default: 'bg-gray-900 text-white hover:bg-gray-800 focus:ring-gray-500',
//     outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-gray-500',
//     ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500'
//   };
  
//   const sizes = {
//     default: 'h-10 px-4 py-2',
//     sm: 'h-8 px-3 text-sm',
//     icon: 'h-10 w-10'
//   };
  
//   return (
//     <button
//       className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`}
//       onClick={onClick}
//       {...props}
//     >
//       {children}
//     </button>
//   );
// };

// // Enhanced Dashboard Controls Component with Tabs
// const DashboardControls = ({ onResetLayout, activeTab, setActiveTab }) => {
//   const handleResetClick = () => {
//     if (onResetLayout && onResetLayout.current) {
//       onResetLayout.current();
//     }
//   };

//   return (
//     <div className="mb-6">
//       {/* Tabs */}
//     <div className="mb-6">
//   <div className="flex space-x-2 bg-white dark:bg-gray-800 p-1 rounded-full shadow-inner w-fit mx-auto">
//     <button
//       className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
//         activeTab === 'dashboard'
//           ? 'bg-[#8bc34a] text-white shadow-md'
//           : 'text-gray-600 hover:text-[#8bc34a]'
//       }`}
//       onClick={() => setActiveTab('dashboard')}
//     >
//       Dashboard
//     </button>
//     <button
//       className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
//         activeTab === 'custom'
//           ? 'bg-[#8bc34a] text-white shadow-md'
//           : 'text-gray-600 hover:text-[#8bc34a]'
//       }`}
//       onClick={() => setActiveTab('custom')}
//     >
//       Custom AI Dashboard
//     </button>
//   </div>
// </div>


//       {/* Controls - Only show for dashboard tab */}
//       {activeTab === 'dashboard' && (
//         <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
//           <div>
//             <h2 className="text-3xl font-bold text-gray-900 dark:text-white">Dashboard</h2>
//             <p className="text-gray-600 dark:text-gray-300 mt-1">Welcome back! Here's your financial overview.</p>
//           </div>
//           <div className="flex space-x-3">
//             <Button variant="outline" onClick={handleResetClick}>
//               <Settings className="mr-2 h-4 w-4" />
//               Reset Layout
//             </Button>
//             <Button className="bg-[#8bc34a] hover:bg-[#6ec122] text-white">
//               <Download className="mr-2 h-4 w-4" />
//               Export Report
//             </Button>
//           </div>
//         </div>
//       )}

//       {/* Custom AI Dashboard Header */}
//       {activeTab === 'custom' && (
//       <></>
//       )}
//     </div>
//   );
// };

// const DEFAULT_LEFT_ORDER = [
//   'networth',
//   'recurring',
//   'transactions',
//   'spending'
// ];
// const DEFAULT_RIGHT_ORDER = [
//   'budget',
//   'portfolio',
//   'goals'
// ];

// // Helper to find card and column
// const findCard = (cardId, leftOrder, rightOrder) => {
//   const leftIdx = leftOrder.indexOf(cardId);
//   if (leftIdx !== -1) return { column: 'left', index: leftIdx };
//   const rightIdx = rightOrder.indexOf(cardId);
//   if (rightIdx !== -1) return { column: 'right', index: rightIdx };
//   return null;
// };

// const DashboardGrid = ({ onResetLayout }) => {
//   const [leftCardOrder, setLeftCardOrder] = useState(() => {
//     const saved = localStorage.getItem('dashboardLeftOrder');
//     return saved ? JSON.parse(saved) : DEFAULT_LEFT_ORDER;
//   });
//   const [rightCardOrder, setRightCardOrder] = useState(() => {
//     const saved = localStorage.getItem('dashboardRightOrder');
//     return saved ? JSON.parse(saved) : DEFAULT_RIGHT_ORDER;
//   });

//   useEffect(() => {
//     localStorage.setItem('dashboardLeftOrder', JSON.stringify(leftCardOrder));
//   }, [leftCardOrder]);
//   useEffect(() => {
//     localStorage.setItem('dashboardRightOrder', JSON.stringify(rightCardOrder));
//   }, [rightCardOrder]);

//   // Move card between columns and positions
//   const moveCard = useCallback((dragId, hoverId, hoverColumn, hoverIndex) => {
//     setLeftCardOrder(prevLeft => {
//       let newLeft = [...prevLeft];
//       let newRight = [...rightCardOrder];
//       const dragInfo = findCard(dragId, prevLeft, rightCardOrder);

//       // Remove dragged card from its column
//       if (dragInfo.column === 'left') newLeft.splice(dragInfo.index, 1);
//       else newRight.splice(dragInfo.index, 1);

//       // Insert into target column at target index
//       if (hoverColumn === 'left') newLeft.splice(hoverIndex, 0, dragId);
//       else newRight.splice(hoverIndex, 0, dragId);

//       setRightCardOrder(newRight);
//       return newLeft;
//     });
//   }, [rightCardOrder]);

//   const handleResetLayout = useCallback(() => {
//     setLeftCardOrder(DEFAULT_LEFT_ORDER);
//     setRightCardOrder(DEFAULT_RIGHT_ORDER);
//     localStorage.removeItem('dashboardLeftOrder');
//     localStorage.removeItem('dashboardRightOrder');
//   }, []);

//   // Pass the reset function to parent component
//   useEffect(() => {
//     if (onResetLayout) {
//       onResetLayout.current = handleResetLayout;
//     }
//   }, [handleResetLayout, onResetLayout]);

//   const cardComponents = {
//     budget: <BudgetDashboard />,
//     recurring: <RecurringTransactionsCard />,
//     transactions: <RecentTransactions />,
//     portfolio: <InvestmentCard />,
//     goals: <FinancialGoals />,
//     spending: <SpendingDashboard />,
//     network: <NetworkGraphCard />,
//     networth: <DashboardNetWorthAreaChart />
//   };

//   // Enhanced DraggableCard to support cross-column drag
//   const DraggableCard = ({ id, column, index, children }) => {
//     const ref = useRef(null);

//     const [{ isDragging }, drag] = useDrag({
//       type: 'card',
//       item: { id, column, index },
//       collect: (monitor) => ({
//         isDragging: monitor.isDragging(),
//       }),
//     });

//     const [, drop] = useDrop({
//       accept: 'card',
//       hover: (item) => {
//         if (item.id !== id) {
//           moveCard(item.id, id, column, index);
//           item.column = column;
//           item.index = index;
//         }
//       },
//     });

//     drag(drop(ref));

//     return (
//       <div
//         ref={ref}
//         style={{ opacity: isDragging ? 0.5 : 1 }}
//         className="transition-opacity duration-200"
//       >
//         {children}
//       </div>
//     );
//   };

//   return (
//     <DndProvider backend={HTML5Backend}>
//       <div className="space-y-6">
//         {/* Financial Network - Full Width First Row */}
//         <DraggableCard
//           key="network"
//           id="network"
//           column="network"
//           index={0}
//         >
//           {cardComponents.network}
//         </DraggableCard>
//         {/* Two Column Layout with cross-column drag-and-drop */}
//         <div className="flex flex-col lg:flex-row gap-6">
//           {/* Left Column */}
//           <div className="flex-1 space-y-6">
//             {leftCardOrder.map((cardId, index) => (
//               <DraggableCard
//                 key={cardId}
//                 id={cardId}
//                 column="left"
//                 index={index}
//               >
//                 {cardComponents[cardId]}
//               </DraggableCard>
//             ))}
//           </div>
//           {/* Right Column */}
//           <div className="flex-1 space-y-6">
//             {rightCardOrder.map((cardId, index) => (
//               <DraggableCard
//                 key={cardId}
//                 id={cardId}
//                 column="right"
//                 index={index}
//               >
//                 {cardComponents[cardId]}
//               </DraggableCard>
//             ))}
//           </div>
//         </div>
//       </div>
//     </DndProvider>
//   );
// };

// // Main Dashboard Component
// const Dashboard = () => {
//   const [activeTab, setActiveTab] = useState('dashboard');
//   const [darkMode, setDarkMode] = useState(false);
//   const [loadingCharts, setLoadingCharts] = useState(true);
//   const resetLayoutRef = useRef(null);

//   // Simulate API calls loading
//   useEffect(() => {
//     const loadDashboardData = async () => {
//       try {
//         // Simulate multiple API calls
//         const apiCalls = [
//           // Simulate network data loading
//           new Promise(resolve => setTimeout(resolve, 1000)),
//           // Simulate budget data loading
//           new Promise(resolve => setTimeout(resolve, 1200)),
//           // Simulate transactions data loading
//           new Promise(resolve => setTimeout(resolve, 800)),
//           // Simulate portfolio data loading
//           new Promise(resolve => setTimeout(resolve, 1500)),
//           // Simulate goals data loading
//           new Promise(resolve => setTimeout(resolve, 900)),
//           // Simulate spending data loading
//           new Promise(resolve => setTimeout(resolve, 1100)),
//           // Simulate recurring transactions loading
//           new Promise(resolve => setTimeout(resolve, 700)),
//           // Simulate net worth data loading
//           new Promise(resolve => setTimeout(resolve, 1300))
//         ];

//         // Wait for all API calls to complete
//         await Promise.all(apiCalls);
        
//         // Set loading to false once all data is loaded
//         setLoadingCharts(false);
//       } catch (error) {
//         console.error('Error loading dashboard data:', error);
//         // Even on error, show the dashboard
//         setLoadingCharts(false);
//       }
//     };

//     loadDashboardData();
//   }, []);

//   // Show loading screen while data is being fetched
//   if (loadingCharts) {
//     return (
//       <div className={`min-h-screen w-full p-5 flex flex-col justify-center items-center ${themeClasses.container(darkMode)}`}>
//         <PaymentLoader />
//         <p className={`mt-4 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
//           Loading dashboard data...
//         </p>
//       </div>
//     );
//   }

//   return (
//     <div className="min-h-screen bg-gray-50">
//       <div className="px-4 sm:px-6 lg:px-8 py-8">
//         {/* Header and Buttons - Always shown */}
//         <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
//           <div>
//             <h2 className="text-3xl font-bold text-gray-900 dark:text-white">Dashboard</h2>
//             <p className="text-gray-600 dark:text-gray-300 mt-1">
//               Welcome back! Here's your financial overview.
//             </p>
//           </div>
//           <div className="flex space-x-3">
//             <Button variant="outline" onClick={() => resetLayoutRef.current && resetLayoutRef.current()}>
//               <Settings className="mr-2 h-4 w-4" />
//               Reset Layout
//             </Button>
//             <Button className="bg-[#8bc34a] hover:bg-[#6ec122] text-white">
//               <Download className="mr-2 h-4 w-4" />
//               Export Report
//             </Button>
//           </div>
//         </div>

//         {/* Tabs - shown below heading/buttons */}
//         <div className="w-full flex justify-center my-6">
//           <div className="bg-white dark:bg-gray-900 p-1 rounded-full shadow-md flex space-x-1 relative">
//             {/* Dashboard Tab */}
//             <button
//               className={`px-6 py-2 text-sm sm:text-base font-medium rounded-full transition-all duration-300 
//                 ${activeTab === 'dashboard'
//                   ? 'bg-gradient-to-r from-lime-500 to-green-500 text-white shadow-lg'
//                   : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-green-500'}`}
//               onClick={() => setActiveTab('dashboard')}
//             >
//               Dashboard
//             </button>

//             {/* Custom AI Dashboard Tab */}
//             <button
//               className={`px-6 py-2 text-sm sm:text-base font-medium rounded-full transition-all duration-300 
//                 ${activeTab === 'custom'
//                   ? 'bg-gradient-to-r from-lime-500 to-green-500 text-white shadow-lg'
//                   : 'text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-green-500'}`}
//               onClick={() => setActiveTab('custom')}
//             >
//               Custom AI Dashboard
//             </button>
//           </div>
//         </div>

//         {/* Tab Content */}
//         {activeTab === 'dashboard' ? (
//           <DashboardGrid onResetLayout={resetLayoutRef} />
//         ) : (
//           <CustomChartsSection />
//         )}
//       </div>
//     </div>
//   );
// };

// export default Dashboard;
import React, { useState, useCallback, useRef, useEffect } from 'react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { 
  GripVertical, 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Wallet, 
  CreditCard, 
  PiggyBank, 
  Target,
  Clock,
  Search,
  Bell,
  Settings,
  Download,
  Calendar,
  ArrowUpRight,
  ArrowDownRight,
  MoreHorizontal,
  ChevronRight,
  Users,
  Activity,
  Zap,
  RefreshCcw,
  Play,
  Pause,
  Plus,
  Minus,
  TrendingUpIcon,
  Sun,
  Moon
} from 'lucide-react';
import { PieChart, Pie, Cell, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line } from 'recharts';
import NetworkGraphCard from './NetworkGraphCard';
import RecurringTransactionsCard from './RecurringTransactionsCard';
import BudgetDashboard from './BudgetDashboard';
import RecentTransactions from './RecentTransactions';
import FinancialGoals from './FinancialGoals';
import InvestmentCard from './InvestmentCard';
import SpendingDashboard from './SpendingDashboard';
import DashboardNetWorthAreaChart from './DashboardNetWorthAreaChart';
import CustomChartsSection from './CustomChartsDashboard';
import PaymentLoader from '../load/PaymentLoader';

// Utility functions
const formatCurrency = (amount) => {
  const num = parseFloat(amount);
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(Math.abs(num));
};

const formatDate = (date) => {
  return new Date(date).toLocaleDateString('en-US', { 
    month: 'short', 
    day: 'numeric' 
  });
};

// Theme classes helper
const themeClasses = {
  container: (darkMode) => darkMode ? 'bg-gray-900' : 'bg-gray-50',
  card: (darkMode) => darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200',
  text: (darkMode) => darkMode ? 'text-white' : 'text-gray-900',
  textSecondary: (darkMode) => darkMode ? 'text-gray-300' : 'text-gray-600',
  border: (darkMode) => darkMode ? 'border-gray-700' : 'border-gray-200'
};

// Card component with dark mode support
const Card = ({ children, className = '', darkMode = false }) => (
  <div className={`${themeClasses.card(darkMode)} rounded-lg shadow-sm border ${className}`}>
    {children}
  </div>
);

const CardHeader = ({ children, className = '', darkMode = false }) => (
  <div className={`px-6 py-4 border-b ${themeClasses.border(darkMode)} ${className}`}>
    {children}
  </div>
);

const CardContent = ({ children, className = '' }) => (
  <div className={`px-6 py-4 ${className}`}>
    {children}
  </div>
);

const CardTitle = ({ children, className = '', darkMode = false }) => (
  <h3 className={`text-lg font-semibold ${themeClasses.text(darkMode)} ${className}`}>
    {children}
  </h3>
);

// Button component with dark mode support
const Button = ({ children, variant = 'default', size = 'default', className = '', onClick, darkMode = false, ...props }) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-md font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const variants = {
    default: darkMode 
      ? 'bg-gray-700 text-white hover:bg-gray-600 focus:ring-gray-500' 
      : 'bg-gray-900 text-white hover:bg-gray-800 focus:ring-gray-500',
    outline: darkMode 
      ? 'border border-gray-600 bg-gray-800 text-gray-300 hover:bg-gray-700 focus:ring-gray-500' 
      : 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-gray-500',
    ghost: darkMode 
      ? 'text-gray-300 hover:bg-gray-700 focus:ring-gray-500' 
      : 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500'
  };
  
  const sizes = {
    default: 'h-10 px-4 py-2',
    sm: 'h-8 px-3 text-sm',
    icon: 'h-10 w-10'
  };
  
  return (
    <button
      className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${className}`}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  );
};

// Enhanced Dashboard Controls Component with Tabs
const DashboardControls = ({ onResetLayout, activeTab, setActiveTab, darkMode }) => {
  const handleResetClick = () => {
    if (onResetLayout && onResetLayout.current) {
      onResetLayout.current();
    }
  };

  return (
    <div className="mb-6">
      {/* Tabs */}
      <div className="mb-6">
        <div className={`flex space-x-2 ${themeClasses.card(darkMode)} p-1 rounded-full shadow-inner w-fit mx-auto`}>
          <button
            className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
              activeTab === 'dashboard'
                ? 'bg-[#8bc34a] text-white shadow-md'
                : `${themeClasses.textSecondary(darkMode)} hover:text-[#8bc34a]`
            }`}
            onClick={() => setActiveTab('dashboard')}
          >
            Dashboard
          </button>
          <button
            className={`px-6 py-2 rounded-full text-sm font-medium transition-all duration-300 ${
              activeTab === 'custom'
                ? 'bg-[#8bc34a] text-white shadow-md'
                : `${themeClasses.textSecondary(darkMode)} hover:text-[#8bc34a]`
            }`}
            onClick={() => setActiveTab('custom')}
          >
            Custom AI Dashboard
          </button>
        </div>
      </div>

      {/* Controls - Only show for dashboard tab */}
      {activeTab === 'dashboard' && (
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div>
            <h2 className={`text-3xl font-bold ${themeClasses.text(darkMode)}`}>Dashboard</h2>
            <p className={`${themeClasses.textSecondary(darkMode)} mt-1`}>Welcome back! Here's your financial overview.</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline" onClick={handleResetClick} darkMode={darkMode}>
              <Settings className="mr-2 h-4 w-4" />
              Reset Layout
            </Button>
            <Button className="bg-[#8bc34a] hover:bg-[#6ec122] text-white">
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
          </div>
        </div>
      )}

      {/* Custom AI Dashboard Header */}
      {activeTab === 'custom' && (
        <></>
      )}
    </div>
  );
};

const DEFAULT_LEFT_ORDER = [
  'networth',
  'recurring',
  'transactions',
  'spending'
];
const DEFAULT_RIGHT_ORDER = [
  'budget',
  'portfolio',
  'goals'
];

// Helper to find card and column
const findCard = (cardId, leftOrder, rightOrder) => {
  const leftIdx = leftOrder.indexOf(cardId);
  if (leftIdx !== -1) return { column: 'left', index: leftIdx };
  const rightIdx = rightOrder.indexOf(cardId);
  if (rightIdx !== -1) return { column: 'right', index: rightIdx };
  return null;
};

const DashboardGrid = ({ onResetLayout, darkMode }) => {
  const [leftCardOrder, setLeftCardOrder] = useState(() => {
    const saved = localStorage.getItem('dashboardLeftOrder');
    return saved ? JSON.parse(saved) : DEFAULT_LEFT_ORDER;
  });
  const [rightCardOrder, setRightCardOrder] = useState(() => {
    const saved = localStorage.getItem('dashboardRightOrder');
    return saved ? JSON.parse(saved) : DEFAULT_RIGHT_ORDER;
  });

  useEffect(() => {
    localStorage.setItem('dashboardLeftOrder', JSON.stringify(leftCardOrder));
  }, [leftCardOrder]);
  useEffect(() => {
    localStorage.setItem('dashboardRightOrder', JSON.stringify(rightCardOrder));
  }, [rightCardOrder]);

  // Move card between columns and positions
  const moveCard = useCallback((dragId, hoverId, hoverColumn, hoverIndex) => {
    setLeftCardOrder(prevLeft => {
      let newLeft = [...prevLeft];
      let newRight = [...rightCardOrder];
      const dragInfo = findCard(dragId, prevLeft, rightCardOrder);

      // Remove dragged card from its column
      if (dragInfo.column === 'left') newLeft.splice(dragInfo.index, 1);
      else newRight.splice(dragInfo.index, 1);

      // Insert into target column at target index
      if (hoverColumn === 'left') newLeft.splice(hoverIndex, 0, dragId);
      else newRight.splice(hoverIndex, 0, dragId);

      setRightCardOrder(newRight);
      return newLeft;
    });
  }, [rightCardOrder]);

  const handleResetLayout = useCallback(() => {
    setLeftCardOrder(DEFAULT_LEFT_ORDER);
    setRightCardOrder(DEFAULT_RIGHT_ORDER);
    localStorage.removeItem('dashboardLeftOrder');
    localStorage.removeItem('dashboardRightOrder');
  }, []);

  // Pass the reset function to parent component
  useEffect(() => {
    if (onResetLayout) {
      onResetLayout.current = handleResetLayout;
    }
  }, [handleResetLayout, onResetLayout]);

  // Pass darkMode to child components
  const cardComponents = {
    budget: <BudgetDashboard darkMode={darkMode} />,
    recurring: <RecurringTransactionsCard darkMode={darkMode} />,
    transactions: <RecentTransactions darkMode={darkMode} />,
    portfolio: <InvestmentCard darkMode={darkMode} />,
    goals: <FinancialGoals darkMode={darkMode} />,
    spending: <SpendingDashboard darkMode={darkMode} />,
    network: <NetworkGraphCard darkMode={darkMode} />,
    networth: <DashboardNetWorthAreaChart darkMode={darkMode} />
  };

  // Enhanced DraggableCard to support cross-column drag
  const DraggableCard = ({ id, column, index, children }) => {
    const ref = useRef(null);

    const [{ isDragging }, drag] = useDrag({
      type: 'card',
      item: { id, column, index },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(),
      }),
    });

    const [, drop] = useDrop({
      accept: 'card',
      hover: (item) => {
        if (item.id !== id) {
          moveCard(item.id, id, column, index);
          item.column = column;
          item.index = index;
        }
      },
    });

    drag(drop(ref));

    return (
      <div
        ref={ref}
        style={{ opacity: isDragging ? 0.5 : 1 }}
        className="transition-opacity duration-200"
      >
        {children}
      </div>
    );
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="space-y-6">
        {/* Financial Network - Full Width First Row */}
        <DraggableCard
          key="network"
          id="network"
          column="network"
          index={0}
        >
          {cardComponents.network}
        </DraggableCard>
        {/* Two Column Layout with cross-column drag-and-drop */}
        <div className="flex flex-col lg:flex-row gap-6">
          {/* Left Column */}
          <div className="flex-1 space-y-6">
            {leftCardOrder.map((cardId, index) => (
              <DraggableCard
                key={cardId}
                id={cardId}
                column="left"
                index={index}
              >
                {cardComponents[cardId]}
              </DraggableCard>
            ))}
          </div>
          {/* Right Column */}
          <div className="flex-1 space-y-6">
            {rightCardOrder.map((cardId, index) => (
              <DraggableCard
                key={cardId}
                id={cardId}
                column="right"
                index={index}
              >
                {cardComponents[cardId]}
              </DraggableCard>
            ))}
          </div>
        </div>
      </div>
    </DndProvider>
  );
};

// Main Dashboard Component
const Dashboard = ({ darkMode, toggleDarkMode }) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [loadingCharts, setLoadingCharts] = useState(true);
  const resetLayoutRef = useRef(null);

  // Simulate API calls loading
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        // Simulate multiple API calls
        const apiCalls = [
          // Simulate network data loading
          new Promise(resolve => setTimeout(resolve, 1000)),
          // Simulate budget data loading
          new Promise(resolve => setTimeout(resolve, 1200)),
          // Simulate transactions data loading
          new Promise(resolve => setTimeout(resolve, 800)),
          // Simulate portfolio data loading
          new Promise(resolve => setTimeout(resolve, 1500)),
          // Simulate goals data loading
          new Promise(resolve => setTimeout(resolve, 900)),
          // Simulate spending data loading
          new Promise(resolve => setTimeout(resolve, 1100)),
          // Simulate recurring transactions loading
          new Promise(resolve => setTimeout(resolve, 700)),
          // Simulate net worth data loading
          new Promise(resolve => setTimeout(resolve, 1300))
        ];

        // Wait for all API calls to complete
        await Promise.all(apiCalls);
        
        // Set loading to false once all data is loaded
        setLoadingCharts(false);
      } catch (error) {
        console.error('Error loading dashboard data:', error);
        // Even on error, show the dashboard
        setLoadingCharts(false);
      }
    };

    loadDashboardData();
  }, []);

  // Show loading screen while data is being fetched
  if (loadingCharts) {
    return (
      <div className={`min-h-screen w-full p-5 flex flex-col justify-center items-center ${themeClasses.container(darkMode)}`}>
        <PaymentLoader />
        <p className={`mt-4 text-sm ${themeClasses.textSecondary(darkMode)}`}>
          Loading dashboard data...
        </p>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${themeClasses.container(darkMode)} transition-colors duration-300`}>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        {/* Header and Buttons - Always shown */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
          <div>
            <h2 className={`text-3xl font-bold ${themeClasses.text(darkMode)}`}>Dashboard</h2>
            <p className={`${themeClasses.textSecondary(darkMode)} mt-1`}>
              Welcome back! Here's your financial overview.
            </p>
          </div>
          <div className="flex space-x-3">
            {/* Dark Mode Toggle Button */}
            <Button 
              variant="outline" 
              onClick={toggleDarkMode}
              darkMode={darkMode}
              className="flex items-center"
            >
              {darkMode ? (
                <Sun className="mr-2 h-4 w-4" />
              ) : (
                <Moon className="mr-2 h-4 w-4" />
              )}
              {darkMode ? 'Light' : 'Dark'}
            </Button>
            <Button 
              variant="outline" 
              onClick={() => resetLayoutRef.current && resetLayoutRef.current()}
              darkMode={darkMode}
            >
              <Settings className="mr-2 h-4 w-4" />
              Reset Layout
            </Button>
            <Button className="bg-[#8bc34a] hover:bg-[#6ec122] text-white">
              <Download className="mr-2 h-4 w-4" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Tabs - shown below heading/buttons */}
        <div className="w-full flex justify-center my-6">
          <div className={`${themeClasses.card(darkMode)} p-1 rounded-full shadow-md flex space-x-1 relative`}>
            {/* Dashboard Tab */}
            <button
              className={`px-6 py-2 text-sm sm:text-base font-medium rounded-full transition-all duration-300 
                ${activeTab === 'dashboard'
                  ? 'bg-gradient-to-r from-lime-500 to-green-500 text-white shadow-lg'
                  : `${themeClasses.textSecondary(darkMode)} hover:bg-gray-100 ${darkMode ? 'hover:bg-gray-700' : ''} hover:text-green-500`}`}
              onClick={() => setActiveTab('dashboard')}
            >
              Dashboard
            </button>

            {/* Custom AI Dashboard Tab */}
            <button
              className={`px-6 py-2 text-sm sm:text-base font-medium rounded-full transition-all duration-300 
                ${activeTab === 'custom'
                  ? 'bg-gradient-to-r from-lime-500 to-green-500 text-white shadow-lg'
                  : `${themeClasses.textSecondary(darkMode)} hover:bg-gray-100 ${darkMode ? 'hover:bg-gray-700' : ''} hover:text-green-500`}`}
              onClick={() => setActiveTab('custom')}
            >
              Custom AI Dashboard
            </button>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'dashboard' ? (
          <DashboardGrid onResetLayout={resetLayoutRef} darkMode={darkMode} />
        ) : (
          <CustomChartsSection darkMode={darkMode} />
        )}
      </div>
    </div>
  );
};

export default Dashboard;