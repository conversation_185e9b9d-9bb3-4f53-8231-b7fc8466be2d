// import React, { useEffect } from 'react';
// import { useDispatch, useSelector } from 'react-redux';
// import { AreaChart, Area, XAxis, YAxis, Tooltip, ResponsiveContainer } from 'recharts';
// import { fetchAccountData, setTimePeriod } from '../../../../logic/redux/accountChartSlice';

// const DashboardNetWorthAreaChart = ({ darkMode }) => {
//   const dispatch = useDispatch();
  
//   // Get state from Redux store
//   const { 
//     chartData, 
//     selectedTimePeriod, 
//     loading, 
//     error 
//   } = useSelector(state => state.accountChart);
  
//   // Fixed chart color (blue)
//   const chartColor = '#4195d1';
  
//   // Fetch data on component mount and when time period changes
//   useEffect(() => {
//     dispatch(fetchAccountData({
//       chartType: 'networth',
//       timePeriod: selectedTimePeriod
//     }));
//   }, [dispatch, selectedTimePeriod]);

//   // Handle loading and error states
//   if (loading) return <div>Loading...</div>;
//   if (error) return <div>Error: {error.message}</div>;

//   // Calculate the max value dynamically for the Y-axis
//   const getMaxValue = () => {
//     if (!chartData || chartData.length === 0) return 0;
//     const maxValue = Math.max(...chartData.map(item => item.balance));
//     return Math.ceil(maxValue * 1.15); // Add 15% padding
//   };

//   // Custom render for data point labels
//   const renderDataPointLabel = (props) => {
//     const { x, y, value } = props;
    
//     return (
//       <text 
//         x={x} 
//         y={y - 10} 
//         fill={chartColor} 
//         textAnchor="middle" 
//         fontSize={12}
//         fontWeight="bold"
//       >
//         ${value.toLocaleString()}
//       </text>
//     );
//   };

//   // Handle time period change
//   const handleTimePeriodChange = (e) => {
//     dispatch(setTimePeriod(e.target.value));
//   };

//   return (
//     <div
//       style={{
//         color: darkMode ? '#e5e7eb' : '#111827',
//         backgroundColor: darkMode ? '#1f2937' : '#ffffff',
//       }}
//     >
//       {/* Header with title and dropdown */}
//       <div style={{ 
//         display: 'flex', 
//         justifyContent: 'space-between', 
//         alignItems: 'center', 
//         marginBottom: '20px' 
//       }}>
//         {/* NetWorth title on left */}
//         <h2 className='text-xl font-roboto'>NetWorth</h2>
        
//         {/* Time Period Dropdown on right */}
//         <div>
//           <select
//             value={selectedTimePeriod}
//             onChange={handleTimePeriodChange}
//             style={{
//               padding: '5px',
//               backgroundColor: darkMode ? '#374151' : '#fff',
//               color: darkMode ? '#f9fafb' : '#111827',
//               border: `1px solid ${darkMode ? '#4b5563' : '#d1d5db'}`,
//               borderRadius: '4px'
//             }}
//           >
//             <option value="one-month">One Month</option>
//             <option value="three-month">Three Months</option>
//             <option value="ytd">Year to Date</option>
//             <option value="half-year">Half Year</option>
//             <option value="yearly">Yearly</option>
//             <option value="quarterly-aggregate">Quarterly Aggregate</option>
//           </select>
//         </div>
//       </div>
 
//       {/* Responsive Area Chart */}
//       <div style={{ height: '400px', width: '100%' }}>
//         <ResponsiveContainer width="100%" height="100%">
//           <AreaChart 
//             data={chartData}
//             margin={{ top: 20, right: 0, left: 0, bottom: 10 }}
//           >
//             <XAxis 
//               dataKey="name" 
//               tick={{ fontSize: 12, fill: darkMode ? '#d1d5db' : '#111827' }}
//               height={50}
//             />
//             <YAxis
//               tick={{ fontSize: 12, fontWeight: 'bold', fill: darkMode ? '#d1d5db' : '#111827' }}
//               axisLine={{ stroke: darkMode ? '#4b5563' : '#ddd' }}
//               domain={[0, getMaxValue()]}
//               tickFormatter={(value) => {
//                 const absValue = Math.abs(value);
//                 const prefix = value < 0 ? '-$' : '$';
                
//                 if (absValue >= 1000000) {
//                   return `${prefix}${(absValue / 1000000).toFixed(1)}M`;
//                 } else if (absValue >= 1000) {
//                   return `${prefix}${(absValue / 1000).toFixed(1)}K`;
//                 } else {
//                   return `${prefix}${absValue.toFixed(0)}`;
//                 }
//               }}
//             />
//             <Tooltip 
//               formatter={(value) => {
//                 return [`$${value.toLocaleString()}`, 'Net Worth'];
//               }}
//               labelFormatter={(label) => `Date: ${label}`}
//               contentStyle={{ 
//                 backgroundColor: darkMode ? '#1f2937' : 'rgba(255, 255, 255, 0.9)',
//                 border: `2px solid ${chartColor}`,
//                 color: darkMode ? '#f9fafb' : '#111827',
//                 borderRadius: '5px',
//                 padding: '10px'
//               }}
//               labelStyle={{ color: darkMode ? '#f3f4f6' : '#374151' }}
//               itemStyle={{ color: chartColor }}
//             />
//             <Area
//               type="monotone"
//               dataKey="balance"
//               name="Net Worth"
//               stroke={chartColor}
//               fill={chartColor}
//               fillOpacity={0.3}
//               strokeWidth={3}
//               dot={{
//                 stroke: chartColor,
//                 strokeWidth: 2,
//                 r: 4,
//                 fillOpacity: 1
//               }}
//               activeDot={{
//                 r: 6,
//                 stroke: chartColor,
//                 strokeWidth: 2,
//                 fill: darkMode ? '#1f2937' : '#fff'
//               }}
//               label={selectedTimePeriod === 'quarterly-aggregate' || chartData.length < 8 ? renderDataPointLabel : false}
//             />
//           </AreaChart>
//         </ResponsiveContainer>
//       </div>
//     </div>
//   );
// };

// export default DashboardNetWorthAreaChart;
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { AreaChart, Area, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid } from 'recharts';
import { GripVertical, TrendingUp, TrendingDown } from 'lucide-react';
import { fetchAccountData, setTimePeriod } from '../../../../logic/redux/accountChartSlice';

// Card Components
const Card = ({ children, className = '', onClick, darkMode }) => (
  <div 
    className={`rounded-lg shadow-sm border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} ${onClick ? 'cursor-pointer hover:shadow-md transition-shadow duration-200' : ''} ${className}`}
    onClick={onClick}
  >
    {children}
  </div>
);

const CardHeader = ({ children, className = '', darkMode }) => (
  <div className={`px-6 py-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} ${className}`}>{children}</div>
);

const CardContent = ({ children, className = '', darkMode }) => (
  <div className={`px-6 py-4 ${className}`}>{children}</div>
);

const CardTitle = ({ children, className = '', darkMode }) => (
  <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} ${className}`}>{children}</h3>
);

const DashboardNetWorthAreaChart = ({ darkMode }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const { chartData, selectedTimePeriod, loading, error } = useSelector((state) => state.accountChart);
  const chartColor = '#8bc34a';

  useEffect(() => {
    dispatch(fetchAccountData({ chartType: 'networth', timePeriod: selectedTimePeriod }));
  }, [dispatch, selectedTimePeriod]);

  // Handle click navigation
  const handleChartClick = () => {
    navigate('/dashboard/accounts');
  };

  // Handle dropdown change
  const handleDropdownChange = (e) => {
    e.stopPropagation();
    e.preventDefault();
    dispatch(setTimePeriod(e.target.value));
  };

  // Handle dropdown click to prevent card navigation
  const handleDropdownClick = (e) => {
    e.stopPropagation();
  };

  // Format Y-axis tick labels
  const formatYAxisTick = (value) => {
    const absValue = Math.abs(value);
    if (absValue >= 1e12) return `${(value / 1e12).toFixed(1)}T`;
    if (absValue >= 1e9)  return `${(value / 1e9).toFixed(1)}B`;
    if (absValue >= 1e6)  return `${(value / 1e6).toFixed(1)}M`;
    if (absValue >= 1e3)  return `${(value / 1e3).toFixed(1)}K`;
    return `$${absValue.toFixed(0)}`;
  };

  if (loading) return <div className={darkMode ? "text-white" : "text-gray-900"}>Loading...</div>;
  if (error) return <div className={darkMode ? "text-red-300" : "text-red-600"}>Error: {error.message}</div>;

  const getMaxValue = () => {
    if (!chartData || chartData.length === 0) return 0;
    return Math.ceil(Math.max(...chartData.map((item) => item.balance)) * 1.15);
  };

  // Net worth growth logic
  let currentNetWorth = 0;
  let previousNetWorth = 0;
  let growth = 0;
  let growthPercentage = '0.0';

  if (chartData.length >= 2) {
    currentNetWorth = chartData[chartData.length - 1].balance;
    previousNetWorth =
      selectedTimePeriod === 'quarterly-aggregate'
        ? chartData[chartData.length - 2].balance
        : chartData[0].balance;
    growth = currentNetWorth - previousNetWorth;
    growthPercentage =
      previousNetWorth !== 0 ? ((growth / previousNetWorth) * 100).toFixed(1) : '0.0';
  }

  return (
    <Card className="dashboard-card" onClick={handleChartClick} darkMode={darkMode}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4" darkMode={darkMode}>
        <CardTitle className="text-lg font-semibold" darkMode={darkMode}>Net Worth Trend</CardTitle>
        <div className="flex items-center gap-2">
          <div onClick={handleDropdownClick} className="relative">
            <select
              value={selectedTimePeriod}
              onChange={handleDropdownChange}
              onClick={handleDropdownClick}
              onMouseDown={handleDropdownClick}
              onFocus={handleDropdownClick}
              className={`px-3 py-1 text-xs font-medium rounded-md border focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                darkMode 
                  ? 'bg-gray-800 text-gray-200 border-gray-600 hover:bg-gray-700' 
                  : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
              }`}
              style={{ zIndex: 10, position: 'relative' }}
            >
              <option value="one-month">One Month</option>
              <option value="three-month">Three Months</option>
              <option value="half-year">Half Year</option>
              <option value="ytd">Year to Date</option>
              <option value="yearly">Yearly</option>
              <option value="quarterly-aggregate">Quarterly Aggregate</option>
            </select>
          </div>
          <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
        </div>
      </CardHeader>

      <CardContent darkMode={darkMode}>
        {/* Net Worth Summary */}
        <div className="mb-4">
          <p className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
            ${currentNetWorth.toLocaleString()}
          </p>
          <p className={`text-sm flex items-center ${growth >= 0 ? 'text-green-400' : 'text-red-400'}`}>
            {growth >= 0 ? <TrendingUp className="h-4 w-4 mr-1" /> : <TrendingDown className="h-4 w-4 mr-1" />}
            {growth >= 0 ? '+' : '-'}${Math.abs(growth).toLocaleString()} ({growthPercentage}%)
          </p>
        </div>

        {/* Chart */}
        <div className="h-64">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart
              data={chartData}
              margin={{ top: 10, right: 20, left: 0, bottom: 10 }}
            >
              <defs>
                <linearGradient id="colorNetWorth" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor={chartColor} stopOpacity={darkMode ? 0.7 : 0.8} />
                  <stop offset="95%" stopColor={chartColor} stopOpacity={darkMode ? 0.05 : 0.1} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke={darkMode ? "#374151" : "#e5e7eb"} />
              <XAxis
                dataKey="name"
                tick={{ fontSize: 12, fill: darkMode ? '#d1d5db' : '#111827' }}
              />
              <YAxis
                tickFormatter={formatYAxisTick}
                tick={{ fontSize: 12, fill: darkMode ? '#d1d5db' : '#111827' }}
                axisLine={{ stroke: darkMode ? '#4b5563' : '#ddd' }}
              />
              <Tooltip
                formatter={(value) => [`$${value.toLocaleString()}`, 'Net Worth']}
                contentStyle={{
                  backgroundColor: darkMode ? '#1f2937' : '#fff',
                  borderColor: chartColor,
                  borderRadius: '5px',
                  fontSize: '13px',
                  color: darkMode ? '#f9fafb' : '#111827'
                }}
                labelStyle={{ color: darkMode ? '#f3f4f6' : '#374151' }}
                itemStyle={{ color: chartColor }}
              />
              <Area
                type="monotone"
                dataKey="balance"
                stroke={chartColor}
                fill="url(#colorNetWorth)"
                strokeWidth={3}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};

export default DashboardNetWorthAreaChart;
