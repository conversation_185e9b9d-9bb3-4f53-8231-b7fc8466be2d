import React, { useState } from 'react';
import DashboardGrid from './DashboardGrid';
import CustomChartsSection from './CustomChartsDashboard';

const DashboardTabs = () => {
  const [activeTab, setActiveTab] = useState('dashboard');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Tabs */}
      <div className="flex border-b mb-6">
        <button
          className={`px-6 py-3 font-semibold focus:outline-none ${
            activeTab === 'dashboard'
              ? 'border-b-2 border-[#8bc34a] text-[#8bc34a]'
              : 'text-gray-500'
          }`}
          onClick={() => setActiveTab('dashboard')}
        >
          Dashboard
        </button>
        <button
          className={`px-6 py-3 font-semibold focus:outline-none ${
            activeTab === 'custom'
              ? 'border-b-2 border-[#8bc34a] text-[#8bc34a]'
              : 'text-gray-500'
          }`}
          onClick={() => setActiveTab('custom')}
        >
          Custom AI Dashboard
        </button>
      </div>

      {/* Tab Content */}
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'dashboard' ? (
          <>
            {/* Dashboard Controls and Grid */}
            <div className="mb-6">
              <h2 className="text-3xl font-bold text-gray-900 dark:text-white">Dashboard</h2>
              <p className="text-gray-600 dark:text-gray-300 mt-1">
                Welcome back! Here's your financial overview.
              </p>
            </div>
            <DashboardGrid />
          </>
        ) : (
          <CustomChartsSection />
        )}
      </div>
    </div>
  );
};

export default DashboardTabs;