import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { GripVertical, Target } from 'lucide-react';
import {
  fetchGoalsRequest
} from '../../../../logic/redux/goalSlice';

// --- Card Design Components ---
const Card = ({ children, className = '', darkMode }) => (
  <div className={`${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg shadow-sm border ${darkMode ? 'border-gray-700' : 'border-gray-200'} ${className}`}>
    {children}
  </div>
);

const CardHeader = ({ children, className = '', darkMode }) => (
  <div className={`px-6 py-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} ${className}`}>
    {children}
  </div>
);

const CardContent = ({ children, className = '' }) => (
  <div className={`px-6 py-4 ${className}`}>
    {children}
  </div>
);

const CardTitle = ({ children, className = '', darkMode }) => (
  <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} ${className}`}>
    {children}
  </h3>
);
// --- End Card Design ---

const formatCurrency = (amount) =>
  new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(Number(amount) || 0);

const formatDate = (date) => {
  if (!date) return '';
  return new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
};

const FinancialGoals = ({ darkMode }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  
  const handleCardClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Card clicked!');
    navigate('/dashboard/goals');
  };

  // Updated selector to match the Goals.jsx pattern
  const goal = useSelector(state => {
    if (state.goal && state.goal.goals) {
      return state.goal;
    }
    for (const key of Object.keys(state)) {
      if (state[key] && Array.isArray(state[key].goals)) {
        return state[key];
      }
    }
    return { goals: [], loading: false };
  });
  
  const goals = goal?.goals || [];
  const loading = goal?.loading || false;
  const error = goal?.error || null;

  useEffect(() => {
    dispatch(fetchGoalsRequest());
  }, [dispatch]);

  return (
    <Card 
      className="dashboard-card cursor-pointer hover:shadow-md transition-shadow select-none" 
      onClick={handleCardClick}
      darkMode={darkMode}
    >
      <CardHeader 
        className="flex flex-row items-center justify-between space-y-0 pb-4"
        darkMode={darkMode}
      >
        <CardTitle darkMode={darkMode}>Financial Goals</CardTitle>
        <GripVertical 
          className="h-4 w-4 text-gray-400 cursor-grab" 
          onClick={(e) => e.stopPropagation()}
        />
      </CardHeader>
      <CardContent>
        {loading ? (
          <div className="flex justify-center items-center h-24">
            <div className="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-[#7fe029]"></div>
          </div>
        ) : error ? (
          <div className={`${darkMode ? 'bg-red-900 border-red-700 text-red-300' : 'bg-red-50 border-red-500 text-red-700'} border-l-4 p-4 rounded-md my-4`}>
            {error}
          </div>
        ) : !goals || goals.length === 0 ? (
          <div className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            No financial goals found.
          </div>
        ) : (
          <div className="space-y-4">
            {goals.map((goal) => {
              // Use the progressPercentage from data if available, otherwise calculate it
              const progress = goal.progressPercentage || 
                              (parseFloat(goal.currentAmount) / parseFloat(goal.goalAmount || goal.targetAmount)) * 100;
              
              // Calculate remaining amount using correct property names
              const targetAmount = goal.goalAmount || goal.targetAmount;
              const currentAmount = goal.currentAmount || 0;
              const remaining = parseFloat(targetAmount) - parseFloat(currentAmount);
              
              return (
                <div 
                  key={goal.id} 
                  className={`p-4 rounded-lg ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <Target className={`h-4 w-4 ${darkMode ? 'text-green-400' : 'text-green-600'}`} />
                      <h4 className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {goal.goalName || goal.name}
                      </h4>
                    </div>
                    <span className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      {formatDate(goal.targetDate)}
                    </span>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className={`${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                        Progress
                      </span>
                      <span className="font-medium">{progress.toFixed(1)}%</span>
                    </div>
                    <div className={`w-full rounded-full h-2 ${darkMode ? 'bg-gray-600' : 'bg-gray-200'}`}>
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${darkMode ? 'bg-green-500' : 'bg-green-600'}`}
                        style={{ width: `${Math.min(progress, 100)}%` }}
                      />
                    </div>
                    <div className={`flex justify-between text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      <span>{formatCurrency(currentAmount)}</span>
                      <span>{formatCurrency(targetAmount)}</span>
                    </div>
                    {remaining > 0 && (
                      <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {formatCurrency(remaining)} remaining
                      </p>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default FinancialGoals;