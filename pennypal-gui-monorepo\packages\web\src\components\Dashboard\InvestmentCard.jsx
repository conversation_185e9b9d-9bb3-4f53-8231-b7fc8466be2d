import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  AlertCircle,
  GripVertical,
  Wallet,
  ChevronRight
} from 'lucide-react';
import {
  syncInvestmentDataRequest,
  fetchUserInvestmentsRequest,
  fetchDailyInvestmentStocksRequest,
  fetchPortfolioSummaryRequest
} from '../../../../logic/redux/investmentsSlice';
import BankIcon from '../../components/v1/components/account/BankIcon';

// Shared Card Components with darkMode support
const Card = ({ className, children, onClick, clickable = false, darkMode }) => (
  <div
    className={`rounded-lg shadow-sm border ${darkMode ? 'bg-gray-900 border-gray-700' : 'bg-white border-gray-200'} ${
      clickable ? 'cursor-pointer hover:shadow-md transition-shadow duration-200' : ''
    } ${className}`}
    onClick={onClick}
  >
    {children}
  </div>
);

const CardHeader = ({ className, children, darkMode }) => (
  <div className={`p-6 ${className}`}>{children}</div>
);

const CardTitle = ({ className, children, darkMode }) => (
  <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} ${className}`}>{children}</h3>
);

const CardContent = ({ className, children, darkMode }) => (
  <div className={`p-6 pt-0 ${className}`}>{children}</div>
);

// PortfolioCard Component
const PortfolioCard = ({ investments, onClick, darkMode }) => {
  const totalValue = investments.reduce((sum, inv) => {
    const shares = parseFloat(inv.quantity || inv.shares || 0);
    const currentPrice = parseFloat(inv.current_price || inv.currentPrice || 0);
    return sum + (shares * currentPrice);
  }, 0);

  const totalCost = investments.reduce((sum, inv) => {
    const shares = parseFloat(inv.quantity || inv.shares || 0);
    const purchasePrice = parseFloat(inv.cost_basis || inv.purchasePrice || 0) / (shares || 1);
    return sum + (shares * purchasePrice);
  }, 0);

  const totalGain = totalValue - totalCost;
  const gainPercentage = totalCost > 0 ? ((totalGain / totalCost) * 100).toFixed(2) : '0.00';

  const getStockIcon = (ticker) => {
    const STOCK_ICON_MAP = {
      aapl: 'apple', tsla: 'tesla', msft: 'microsoft', googl: 'google',
      amzn: 'amazon', mcd: 'McDonalds', meta: 'facebook', fb: 'facebook',
      nflx: 'netflix', spot: 'spotify', pypl: 'PayPal', uber: 'uber',
      lyft: 'lyft', aal: 'american airlines', nvda: 'nvidia'
    };

    if (!ticker) return null;
    const normalizedTicker = ticker.toLowerCase();
    const iconName = STOCK_ICON_MAP[normalizedTicker];

    return iconName ? (
      <BankIcon
        institutionName={iconName}
        accountType="investment"
        size={50}
        sizeClass="sm"
        className="rounded-lg"
        trackingId={`stock-${ticker}`}
      />
    ) : (
      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-xs">
        {ticker.substring(0, 2)}
      </div>
    );
  };

  return (
    <Card className="dashboard-card" onClick={onClick} clickable={true} darkMode={darkMode}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4" darkMode={darkMode}>
        <CardTitle className="flex items-center gap-2" darkMode={darkMode}>
          Investment Portfolio
          <ChevronRight className="h-4 w-4 text-gray-400" />
        </CardTitle>
        <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
      </CardHeader>
      <CardContent darkMode={darkMode}>
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className={`text-center p-3 rounded-lg ${darkMode ? 'bg-green-900/20' : 'bg-green-50'}`}>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Value</p>
              <p className={`text-xl font-bold ${darkMode ? 'text-green-400' : 'text-green-600'}`}>
                ${totalValue.toLocaleString()}
              </p>
            </div>
            <div className={`text-center p-3 rounded-lg ${darkMode ? 'bg-green-900/20' : 'bg-green-50'}`}>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Gain</p>
              <p className={`text-xl font-bold ${totalGain >= 0 ? (darkMode ? 'text-green-400' : 'text-green-600') : (darkMode ? 'text-red-400' : 'text-red-600')}`}>
                {totalGain >= 0 ? '+' : ''}${totalGain.toLocaleString()} ({gainPercentage}%)
              </p>
            </div>
          </div>

          <div className="space-y-2">
            {investments.slice(0, 3).map((inv, i) => {
              const shares = parseFloat(inv.quantity || inv.shares || 0);
              const currentPrice = parseFloat(inv.current_price || inv.currentPrice || 0);
              const costBasis = parseFloat(inv.cost_basis || inv.purchasePrice || 0);
              const purchasePrice = shares > 0 ? costBasis / shares : 0;
              const currentValue = shares * currentPrice;
              const gain = currentValue - (shares * purchasePrice);
              const gainPercent = purchasePrice > 0 ? ((gain / (shares * purchasePrice)) * 100).toFixed(1) : '0.0';

              return (
                <div key={inv.id || i} className={`flex items-center justify-between p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                  <div className="flex items-center gap-3">
                    {getStockIcon(inv.ticker || inv.formattedTicker)}
                    <div>
                      <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>{inv.ticker || inv.formattedTicker}</p>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {shares} shares @ ${currentPrice.toFixed(2)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>${currentValue.toLocaleString()}</p>
                    <p className={`text-sm ${gain >= 0 ? (darkMode ? 'text-green-400' : 'text-green-600') : (darkMode ? 'text-red-400' : 'text-red-600')}`}>
                      {gain >= 0 ? '+' : ''}${gain.toFixed(0)} ({gainPercent}%)
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Main Component
const InvestmentCard = ({ darkMode }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {
    userInvestments,
    loading,
    error
  } = useSelector((state) => state.investment);

  useEffect(() => {
    dispatch(fetchPortfolioSummaryRequest());
    dispatch(fetchUserInvestmentsRequest());
    dispatch(fetchDailyInvestmentStocksRequest());
  }, [dispatch]);

  const safeUserInvestments = Array.isArray(userInvestments) ? userInvestments : [];

  // Navigation handler
  const handleNavigateToInvestments = () => {
    navigate('/dashboard/investmentsdashboard');
  };

  return (
    <div>
      <div className="max-w-7xl mx-auto">
        {error && (
          <div className={`mb-6 p-4 border rounded-xl flex items-center gap-3 ${darkMode ? 'bg-red-900/20 border-red-700' : 'bg-red-50 border-red-200'}`}>
            <AlertCircle className="w-5 h-5 text-red-500" />
            <span className={darkMode ? "text-red-300" : "text-red-700"}>{error}</span>
          </div>
        )}

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className={`animate-spin rounded-full h-8 w-8 border-b-2 ${darkMode ? 'border-green-400' : 'border-blue-500'}`}></div>
            <span className={`ml-3 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Loading investment data...</span>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-6">
            <PortfolioCard
              investments={safeUserInvestments}
              onClick={handleNavigateToInvestments}
              darkMode={darkMode}
            />
          </div>
        )}

        {!loading && safeUserInvestments.length === 0 && (
          <div className="text-center py-12">
            <Wallet className="w-16 h-16 mx-auto mb-4 text-gray-400" />
            <p className={`text-lg font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>No investments found</p>
            <p className={`text-gray-500 ${darkMode ? 'dark:text-gray-500' : ''}`}>
              Your investment portfolio will appear here once you sync your data.
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default InvestmentCard;
