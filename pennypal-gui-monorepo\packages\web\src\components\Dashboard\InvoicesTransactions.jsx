import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { getCurrentUserId } from '../../utils/AuthUtil'; // Adjust path as needed
import { fetchPaymentData } from 'logic/epics/paymentEpic';
import { useCacheStatus } from 'logic/hooks/useCacheStatus';
import { FiChevronRight, FiChevronDown } from 'react-icons/fi';
import PaymentLoader from '../load/PaymentLoader'; // Adjust path as needed
import { themeClasses } from '../../utils/tailwindUtils'; // Adjust path as needed

const InvoicesTransactions = ({ darkMode }) => {
  const dispatch = useDispatch();
  const userId = getCurrentUserId();
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('invoices'); // Default to 'invoices' tab

  // Simple cache status checks
  const subscriptionCacheStatus = useCacheStatus('paymentSubscription');
  const invoicesCacheStatus = useCacheStatus('paymentInvoices');
  const upcomingInvoiceCacheStatus = useCacheStatus('upcomingInvoice');

  // Get data from cache
  const invoices = invoicesCacheStatus.data || [];
  const invoiceUpcoming = upcomingInvoiceCacheStatus.data;

  // Get error from cache if any
  const error = subscriptionCacheStatus.error ||
               invoicesCacheStatus.error ||
               upcomingInvoiceCacheStatus.error;

  // Simple data fetching - let the cache epics handle the rest
  useEffect(() => {
    if (userId) {
      console.log('🔄 Fetching payment data for user:', userId);
      dispatch(fetchPaymentData(userId));
    }
  }, [userId, dispatch]);

  // Update loading state based on cache status
  useEffect(() => {
    const isLoading = subscriptionCacheStatus.isLoading ||
                     invoicesCacheStatus.isLoading ||
                     upcomingInvoiceCacheStatus.isLoading;

    const hasData = subscriptionCacheStatus.isLoaded ||
                   invoicesCacheStatus.isLoaded;

    setLoading(isLoading || (!hasData && userId));
  }, [
    subscriptionCacheStatus.isLoading,
    subscriptionCacheStatus.isLoaded,
    invoicesCacheStatus.isLoading,
    invoicesCacheStatus.isLoaded,
    upcomingInvoiceCacheStatus.isLoading,
    userId
  ]);

  if (loading) {
    return (
      <div className={`min-h-screen w-full p-5 flex flex-col justify-center items-center ${themeClasses.container(darkMode)}`}>
        <PaymentLoader darkMode={darkMode} />
        {/* <p className={`mt-2 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Loading invoices...
        </p> */}
      </div>
    );
  }

  if (error) {
    return (
      <div className={`w-full max-w-4xl mx-auto p-4 ${themeClasses.container(darkMode)}`}>
        <div className={`border px-4 py-3 rounded relative ${themeClasses.error(darkMode)}`} role="alert">
          <strong className={`font-bold ${darkMode ? 'text-red-400' : 'text-red-700'}`}>Error:</strong>
          <span className={`block sm:inline ${darkMode ? 'text-red-300' : 'text-red-700'}`}> {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-2 p-4 ${themeClasses.container(darkMode)}`}>
      <div className={`flex mb-4 border-b ${themeClasses.border(darkMode)}`}>
        {/* Invoices Paid Tab */}
        <button
          className={`flex items-center space-x-2 px-4 py-2 rounded-t ${
            activeTab === 'invoices' ? themeClasses.tabActive(darkMode) : themeClasses.tabInactive(darkMode)
          }`}
          onClick={() => setActiveTab('invoices')}
        >
          <span>Invoices Paid</span>
        </button>

        {/* Upcoming Invoice Tab */}
        <button
          className={`flex items-center space-x-2 px-4 py-2 rounded-t ${
            activeTab === 'upcoming' ? themeClasses.tabActive(darkMode) : themeClasses.tabInactive(darkMode)
          }`}
          onClick={() => setActiveTab('upcoming')}
        >
          <span>Upcoming Invoice</span>
        </button>
      </div>

      <div className="mt-4">
        {activeTab === 'invoices' && (
          <div>
            {invoices.length === 0 ? (
              <p>No paid invoices available.</p>
            ) : (
              <table className={`min-w-full rounded-t-xl rounded-b-xl overflow-hidden shadow-lg max-w-lg w-full text-sm mb-2 ${themeClasses.border(darkMode)}`}>
                <thead>
                  <tr className={`text-left border-b ${themeClasses.tableHeader(darkMode)} ${themeClasses.border(darkMode)}`}>
                    <th className="py-2 px-2">Date</th>
                    <th className="py-2 px-2">Amount Paid</th>
                    <th className="py-2 px-2">Reason</th>
                    <th className="py-2 px-2">Invoice</th>
                    <th className="py-2 px-2">PDF</th>
                  </tr>
                </thead>
                <tbody>
                  {invoices.map((invoice) => (
                    <tr
                      key={invoice.invoiceId}
                      className={`border-b ${themeClasses.border(darkMode)} ${themeClasses.container(darkMode)}`}
                    >
                      <td className="py-1 px-2">{invoice.createdAt.substring(0, 10)}</td>
                      <td className="py-1 px-2">$ {Number(invoice.amountPaid).toFixed(2)}</td>
                      <td className="py-1 px-2">{invoice.billingReason.replace('_', ' ')}</td>
                      <td className="py-1 px-2">
                        <a
                          href={invoice.invoiceUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className={`${themeClasses.link(darkMode)} underline`}
                        >
                          Invoice Link
                        </a>
                      </td>
                      <td className="py-1 px-2">
                        <a
                          href={invoice.invoicePdf}
                          target="_blank"
                          rel="noopener noreferrer"
                          title="Download PDF"
                          className={themeClasses.linkSecondary(darkMode)}
                        >
                          🧾
                        </a>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            )}
          </div>
        )}

        {activeTab === 'upcoming' && (
          <div className="transition-all duration-300 ease-in-out">
            {invoiceUpcoming == null ? (
              <p>No upcoming invoice</p>
            ) : (
              <table className={`w-full text-sm mb-2 ${themeClasses.border(darkMode)}`}>
                <thead>
                  <tr className={`text-left border-b  ${themeClasses.border(darkMode)}`}>
                    <th className="py-1">Description</th>
                    <th className="py-1 text-right">Amount</th>
                  </tr>
                </thead>
                <tbody>
                  {invoiceUpcoming.items.map((item, idx) => (
                    <tr
                      key={idx}
                      className={`border-b ${themeClasses.border(darkMode)} ${themeClasses.container(darkMode)}`}
                    >
                      <td className="py-1">{item.description}</td>
                      <td
                        className={`py-1 text-right ${
                          item.credit ? themeClasses.amountCredit(darkMode) : themeClasses.amountDebit(darkMode)
                        }`}
                      >
                        {item.credit ? '-' : '+'}$ {Math.abs(parseFloat(item.amount)).toFixed(2)}
                      </td>
                    </tr>
                  ))}
                </tbody>
                <tfoot>
                  <tr className={`font-bold ${themeClasses.container(darkMode)}`}>
                    <td className="py-1">
                      Total Due on {invoiceUpcoming.nextPaymentDate.substring(0, 10)}
                    </td>
                    <td className="py-1 text-right">
                      $ {parseFloat(invoiceUpcoming.totalAmount).toFixed(2)} {invoiceUpcoming.currency}
                    </td>
                  </tr>
                </tfoot>
              </table>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default InvoicesTransactions;