// import React, { useEffect, useState } from 'react';
// import { useSelector, useDispatch } from 'react-redux';
// import { fetchAccountDetails } from '../../../../logic/redux/accountsDashboardSlice';
// import { 
//   GripVertical, 
//   Wallet, 
//   PiggyBank, 
//   TrendingUp as TrendingUpIcon, 
//   CreditCard, 
//   TrendingUp, 
//   TrendingDown, 
//   DollarSign 
// } from 'lucide-react';

// const NetworkGraphCard = ({ accounts: propAccounts }) => {
//   const dispatch = useDispatch();
//   const reduxAccounts = useSelector(state => state.accounts.accounts);
//   const isLoading = useSelector(state => state.accounts.isLoading);
  
//   // Use prop accounts if provided, otherwise use Redux accounts
//   const accounts = propAccounts || reduxAccounts;

//   useEffect(() => {
//     // Only fetch if no prop accounts and no Redux accounts
//     if (!propAccounts && (!reduxAccounts || reduxAccounts.length === 0)) {
//       dispatch(fetchAccountDetails());
//     }
//   }, [dispatch, propAccounts, reduxAccounts]);

//   const [timeView, setTimeView] = useState('monthly');
//   const [animationStep, setAnimationStep] = useState(0);

//   useEffect(() => {
//     const interval = setInterval(() => {
//       setAnimationStep(prev => (prev + 1) % 100);
//     }, 50);
//     return () => clearInterval(interval);
//   }, []);

//   const centerX = 400;
//  const centerY = 270;
//   const primaryRadius =  240;
//   const secondaryRadius = 180;

//   // Enhanced filter logic for account categorization
//   const assetAccounts = accounts.filter(acc => {
//     const category = (acc.accountCategory || '').toLowerCase();
//     const type = (acc.accountType || acc.type || '').toLowerCase();
//     const name = (acc.accountName || acc.name || '').toLowerCase();
    
//     return ['savings', 'checking', 'investment', 'depository', 'cash management', 'cash'].some(assetType =>
//       category.includes(assetType) || type.includes(assetType) || name.includes(assetType)
//     );
//   });

//   const liabilityAccounts = accounts.filter(acc => {
//     const category = (acc.accountCategory || '').toLowerCase();
//     const type = (acc.accountType || acc.type || '').toLowerCase();
//     const name = (acc.accountName || acc.name || '').toLowerCase();
    
//     return ['loan', 'credit', 'mortgage'].some(liabilityType =>
//       category.includes(liabilityType) || type.includes(liabilityType) || name.includes(liabilityType)
//     );
//   });

//   // Enhanced node creation with better positioning
//   const createNodes = (accountList, radius, startAngle = 0) => {
//     return accountList.map((account, index) => {
//       const angle = startAngle + (index / Math.max(accountList.length, 1)) * Math.PI * 1.2;
//      const horizontalRadius = radius * 1.6;
// const verticalRadius = radius * 1.1;
//       const x = centerX + horizontalRadius * Math.cos(angle);
//       const y = centerY + verticalRadius * Math.sin(angle);
//       const balance = Math.abs(parseFloat(account.balance || 0));
      
//       return {
//         ...account,
//         x,
//         y,
//         size: Math.max(30, Math.min(50, balance / 600)),
//         balance,
//         isPositive: parseFloat(account.balance || 0) >= 0,
//         displayName: account.accountName || account.name || 'Unknown Account',
//         institutionInfo: account.institutionId || account.accountType || account.type || 'Unknown'
//       };
//     });
//   };

//   const assetNodes = createNodes(assetAccounts, primaryRadius, 0);
//   const liabilityNodes = createNodes(liabilityAccounts, secondaryRadius, Math.PI);
//   const allNodes = [...assetNodes, ...liabilityNodes];

//   const getNodeColor = (account) => {
//     const type = (account.accountType || account.type || '').toLowerCase();
//     const colors = {
//       checking: { main: '#8bc34a', glow: '#c8e6c9', icon: Wallet },
//       savings: { main: '#4caf50', glow: '#a5d6a7', icon: PiggyBank },
//       investment: { main: '#66bb6a', glow: '#a5d6a7', icon: TrendingUpIcon },
//       credit: { main: '#f44336', glow: '#ffcdd2', icon: CreditCard },
//       loan: { main: '#ff9800', glow: '#ffcc80', icon: CreditCard },
//       mortgage: { main: '#e91e63', glow: '#f8bbd9', icon: CreditCard },
//       default: { main: '#9e9e9e', glow: '#e0e0e0', icon: Wallet }
//     };
    
//     return colors[type] || colors.default;
//   };

//   // Show loading spinner if loading
//   if (!propAccounts && isLoading) {
//     return (
//       <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 text-center text-gray-500 dark:text-gray-400">
//         Loading accounts...
//       </div>
//     );
//   }

//   // If no accounts, show empty state
//   if (!accounts || accounts.length === 0) {
//     return (
//       <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 text-center text-gray-500 dark:text-gray-400">
//         No accounts found.
//       </div>
//     );
//   }

//   // Calculate totals
//   const totalAssets = assetNodes.reduce((sum, node) => sum + node.balance, 0);
//   const totalLiabilities = liabilityNodes.reduce((sum, node) => sum + node.balance, 0);
//   const netWorth = totalAssets - totalLiabilities;

//   return (
//     <div className="dashboard-card bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
//       {/* Header */}
//       <div className="flex flex-row items-center justify-between space-y-0 pb-4 px-6 pt-6">
//         <span className="text-lg font-semibold text-gray-900 dark:text-gray-100">Financial Network</span>
//         <div className="flex items-center gap-2">
//           {/* <div className="flex bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
//             <button
//               onClick={() => setTimeView('monthly')}
//               className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
//                 timeView === 'monthly'
//                   ? 'bg-[#8bc34a] hover:bg-[#6ec122] text-white'
//                   : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
//               }`}
//             >
//               Monthly
//             </button>
//             <button
//               onClick={() => setTimeView('yearly')}
//               className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
//                 timeView === 'yearly'
//                   ? 'bg-[#8bc34a] hover:bg-[#6ec122] text-white'
//                   : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
//               }`}
//             >
//               Yearly
//             </button>
//           </div> */}
//           <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
//         </div>
//       </div>

//       <div className="px-6 pb-6">
//         {/* Net Worth Summary */}
//         <div className="flex justify-between items-center mb-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-lg border border-green-200/50 dark:border-green-700/50">
//           <div className="flex items-center gap-3">
//             <div className="p-2 bg-white dark:bg-gray-800 rounded-full shadow-sm">
//               <DollarSign className="h-5 w-5 text-green-600" />
//             </div>
//             <div>
//               <p className="text-sm font-medium text-gray-600 dark:text-gray-300">Net Worth</p>
//               <p className="text-xs text-gray-500 dark:text-gray-400">Assets - Liabilities</p>
//             </div>
//           </div>
//           <div className="flex items-center gap-2">
//             <span className={`font-bold text-xl ${
//               netWorth >= 0 ? 'text-green-600' : 'text-red-600'
//             }`}>
//               ${netWorth.toLocaleString()}
//             </span>
//             {netWorth >= 0
//               ? <TrendingUp className="h-5 w-5 text-green-600" />
//               : <TrendingDown className="h-5 w-5 text-red-600" />}
//           </div>
//         </div>

//         {/* Network Graph Visualization */}
//         <div className="w-full h-[600px] relative overflow-hidden bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
//           <svg width="100%" height="100%" viewBox="0 0 800 600" className="drop-shadow-sm">
//             <defs>
//               <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
//                 <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#f0f0f0" strokeWidth="0.5" opacity="0.3"/>
//               </pattern>
              
//               <radialGradient id="centerGradient" cx="50%" cy="50%" r="40%">
//                 <stop offset="0%" stopColor="#8bc34a" stopOpacity="0.2"/>
//                 <stop offset="100%" stopColor="#8bc34a" stopOpacity="0"/>
//               </radialGradient>
              
//               <filter id="glow">
//                 <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
//                 <feMerge> 
//                   <feMergeNode in="coloredBlur"/>
//                   <feMergeNode in="SourceGraphic"/>
//                 </feMerge>
//               </filter>
//             </defs>

//             {/* Background grid */}
//             <rect width="100%" height="100%" fill="url(#grid)" />

//             {/* Center glow effect */}
//             <circle cx={centerX} cy={centerY} r="120" fill="url(#centerGradient)" />

//             {/* Connection lines with animation */}
//             {allNodes.map((node, index) => {
//               const distance = Math.sqrt(Math.pow(node.x - centerX, 2) + Math.pow(node.y - centerY, 2));
//               const colors = getNodeColor(node);
              
//               return (
//                 <g key={`connection-${index}`}>
//                   {/* Background line */}
//                   <line
//                     x1={centerX}
//                     y1={centerY}
//                     x2={node.x}
//                     y2={node.y}
//                     stroke="#e0e0e0"
//                     strokeWidth="3"
//                     opacity="0.4"
//                   />
                  
//                   {/* Animated colored line */}
//                   <line
//                     x1={centerX}
//                     y1={centerY}
//                     x2={node.x}
//                     y2={node.y}
//                     stroke={colors.main}
//                     strokeWidth="4"
//                     opacity="0.6"
//                     strokeDasharray={`${distance/10},${distance/20}`}
//                     strokeDashoffset={animationStep * -2}
//                     filter="url(#glow)"
//                   />
                  
//                   {/* Animated pulse dot */}
//                   <circle
//                     cx={centerX + (node.x - centerX) * ((animationStep % 100) / 100)}
//                     cy={centerY + (node.y - centerY) * ((animationStep % 100) / 100)}
//                     r="3"
//                     fill={colors.main}
//                     opacity={Math.sin((animationStep * Math.PI) / 50)}
//                   />
//                 </g>
//               );
//             })}
            
//             {/* Center node (Net Worth) */}
//             <g filter="url(#glow)">
//               <circle cx={centerX} cy={centerY} r="28" fill="none" stroke="#8bc34a" strokeWidth="4" opacity="0.6" />
//               <circle cx={centerX} cy={centerY} r="20" fill="#8bc34a" stroke="#fff" strokeWidth="3" />
//               <circle cx={centerX} cy={centerY} r="12" fill="#fff" />
//             </g>
            
//             {/* Account nodes */}
//             {allNodes.map((node) => {
//               const colors = getNodeColor(node);
              
//               return (
//                 <g key={node.id || node.accountId} className="group cursor-pointer">
//                   {/* Glow effect */}
//                   <circle
//                     cx={node.x}
//                     cy={node.y}
//                     r={node.size + 12}
//                     fill={colors.glow}
//                     opacity="0.4"
//                     filter="url(#glow)"
//                   />
                  
//                   {/* Main circle */}
//                   <circle
//                     cx={node.x}
//                     cy={node.y}
//                     r={node.size}
//                     fill={colors.main}
//                     stroke="#fff"
//                     strokeWidth="4"
//                     filter="url(#glow)"
//                   />
                  
//                   {/* Inner circle */}
//                   <circle
//                     cx={node.x}
//                     cy={node.y}
//                     r={node.size - 8}
//                     fill="#fff"
//                     opacity="0.9"
//                   />
                  
//                   {/* Status indicator */}
//                   <circle
//                     cx={node.x + node.size - 10}
//                     cy={node.y - node.size + 10}
//                     r="8"
//                     fill={node.isPositive ? "#4caf50" : "#f44336"}
//                     stroke="#fff"
//                     strokeWidth="2"
//                   />
                  
//                   {/* Account name */}
//                   <text
//                     x={node.x}
//                     y={node.y + node.size + 20}
//                     textAnchor="middle"
//                     className="text-sm fill-current text-gray-700 dark:text-gray-300 font-semibold"
//                     fontSize="12"
//                   >
//                     {node.displayName.split(' ')[0]}
//                   </text>
                  
//                   {/* Balance */}
//                   <text
//                     x={node.x}
//                     y={node.y + node.size + 35}
//                     textAnchor="middle"
//                     className="text-xs fill-current text-gray-600 dark:text-gray-400 font-medium"
//                     fontSize="10"
//                   >
//                     ${(node.balance / 1000).toFixed(1)}k
//                   </text>
//                 </g>
//               );
//             })}
//           </svg>
//         </div>
        
//         {/* Account Lists */}
//         <div className="mt-6 space-y-4">
//           {/* Assets */}
//           {assetNodes.length > 0 && (
//             <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 border border-green-200/50 dark:border-green-700/50">
//               <div className="flex items-center gap-2 mb-3">
//                 <TrendingUp className="h-4 w-4 text-green-600" />
//                 <h4 className="text-sm font-semibold text-green-700 dark:text-green-300">Assets</h4>
//                 <span className="text-xs text-green-600 bg-green-100 dark:bg-green-800 px-2 py-1 rounded-full">
//                   ${totalAssets.toLocaleString()}
//                 </span>
//               </div>
//               <div className="grid grid-cols-1 gap-2">
//                 {assetNodes.map((node, index) => {
//                   const colors = getNodeColor(node);
//                   const IconComponent = colors.icon;
                  
//                   return (
//                     <div key={node.id || node.accountId || index} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-green-100 dark:border-green-800/50">
//                       <div className="flex items-center gap-3">
//                         <div
//                           className="w-8 h-8 rounded-full flex items-center justify-center"
//                           style={{ backgroundColor: colors.main }}
//                         >
//                           <IconComponent className="h-4 w-4 text-white" />
//                         </div>
//                         <div>
//                           <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
//                             {node.displayName}
//                           </span>
//                           <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
//                             {node.institutionInfo}
//                           </p>
//                         </div>
//                       </div>
//                       <span className="text-sm font-bold text-green-600">
//                         ${node.balance.toLocaleString()}
//                       </span>
//                     </div>
//                   );
//                 })}
//               </div>
//             </div>
//           )}
          
//           {/* Liabilities */}
//           {liabilityNodes.length > 0 && (
//             <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 border border-red-200/50 dark:border-red-700/50">
//               <div className="flex items-center gap-2 mb-3">
//                 <TrendingDown className="h-4 w-4 text-red-600" />
//                 <h4 className="text-sm font-semibold text-red-700 dark:text-red-300">Liabilities</h4>
//                 <span className="text-xs text-red-600 bg-red-100 dark:bg-red-800 px-2 py-1 rounded-full">
//                   ${totalLiabilities.toLocaleString()}
//                 </span>
//               </div>
//               <div className="grid grid-cols-1 gap-2">
//                 {liabilityNodes.map((node, index) => {
//                   const colors = getNodeColor(node);
//                   const IconComponent = colors.icon;
                  
//                   return (
//                     <div key={node.id || node.accountId || index} className="flex items-center justify-between p-3 bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-red-100 dark:border-red-800/50">
//                       <div className="flex items-center gap-3">
//                         <div
//                           className="w-8 h-8 rounded-full flex items-center justify-center"
//                           style={{ backgroundColor: colors.main }}
//                         >
//                           <IconComponent className="h-4 w-4 text-white" />
//                         </div>
//                         <div>
//                           <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
//                             {node.displayName}
//                           </span>
//                           <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
//                             {node.institutionInfo}
//                           </p>
//                         </div>
//                       </div>
//                       <span className="text-sm font-bold text-red-600">
//                         ${node.balance.toLocaleString()}
//                       </span>
//                     </div>
//                   );
//                 })}
//               </div>
//             </div>
//           )}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default NetworkGraphCard;
import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { fetchAccountDetails } from '../../../../logic/redux/accountsDashboardSlice';
import {
  GripVertical,
  Wallet,
  PiggyBank,
  TrendingUp as TrendingUpIcon,
  CreditCard,
  TrendingUp,
  TrendingDown,
  DollarSign,
  ChevronDown,
  ChevronUp,
  Eye,
  BarChart3
} from 'lucide-react';

const NetworkGraphCard = ({ accounts: propAccounts, darkMode }) => {
  const dispatch = useDispatch();
  const reduxAccounts = useSelector(state => state.accounts.accounts);
  const isLoading = useSelector(state => state.accounts.isLoading);

  // Use prop accounts if provided, otherwise use Redux accounts
  const accounts = propAccounts || reduxAccounts;

  useEffect(() => {
    if (!propAccounts && (!reduxAccounts || reduxAccounts.length === 0)) {
      dispatch(fetchAccountDetails());
    }
  }, [dispatch, propAccounts, reduxAccounts]);

  const [activeTab, setActiveTab] = useState('networth');
  const [animationStep, setAnimationStep] = useState(0);
  const [assetsExpanded, setAssetsExpanded] = useState(false);
  const [liabilitiesExpanded, setLiabilitiesExpanded] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      setAnimationStep(prev => (prev + 1) % 100);
    }, 50);
    return () => clearInterval(interval);
  }, []);

  const centerX = 400;
  const centerY = 270;
  const primaryRadius = 240;
  const secondaryRadius = 180;

  const assetAccounts = accounts.filter(acc => {
    const category = (acc.accountCategory || '').toLowerCase();
    const type = (acc.accountType || acc.type || '').toLowerCase();
    const name = (acc.accountName || acc.name || '').toLowerCase();
    return ['savings', 'checking', 'investment', 'depository', 'cash management', 'cash'].some(assetType =>
      category.includes(assetType) || type.includes(assetType) || name.includes(assetType)
    );
  });

  const liabilityAccounts = accounts.filter(acc => {
    const category = (acc.accountCategory || '').toLowerCase();
    const type = (acc.accountType || acc.type || '').toLowerCase();
    const name = (acc.accountName || acc.name || '').toLowerCase();
    return ['loan', 'credit', 'mortgage'].some(liabilityType =>
      category.includes(liabilityType) || type.includes(liabilityType) || name.includes(liabilityType)
    );
  });

  const createNodes = (accountList, radius, startAngle = 0, nodeType = 'asset') => {
    return accountList.map((account, index) => {
      const angle = startAngle + (index / Math.max(accountList.length, 1)) * Math.PI * 1.2;
      const horizontalRadius = radius * 1.6;
      const verticalRadius = radius * 1.1;
      const x = centerX + horizontalRadius * Math.cos(angle);
      const y = centerY + verticalRadius * Math.sin(angle);
      const balance = Math.abs(parseFloat(account.balance || 0));
      return {
        ...account,
        x,
        y,
        size: Math.max(30, Math.min(50, balance / 600)),
        balance,
        isPositive: parseFloat(account.balance || 0) >= 0,
        displayName: account.accountName || account.name || 'Unknown Account',
        institutionInfo: account.institutionId || account.accountType || account.type || 'Unknown',
        uniqueId: `${nodeType}-${account.id || account.accountId || index}-${Date.now()}`
      };
    });
  };

  const assetNodes = createNodes(assetAccounts, primaryRadius, 0, 'asset');
  const liabilityNodes = createNodes(liabilityAccounts, secondaryRadius, Math.PI, 'liability');
  const allNodes = [...assetNodes, ...liabilityNodes];

  const createGroupedNodes = () => {
    const totalAssets = assetNodes.reduce((sum, node) => sum + node.balance, 0);
    const totalLiabilities = liabilityNodes.reduce((sum, node) => sum + node.balance, 0);
    const groupedNodes = [];
    if (totalAssets > 0) {
      groupedNodes.push({
        x: centerX - 150,
        y: centerY,
        size: 60,
        balance: totalAssets,
        isPositive: true,
        displayName: 'Total Assets',
        institutionInfo: `${assetNodes.length} accounts`,
        uniqueId: 'grouped-assets',
        type: 'assets-group',
        color: '#4caf50'
      });
    }
    if (totalLiabilities > 0) {
      groupedNodes.push({
        x: centerX + 150,
        y: centerY,
        size: 60,
        balance: totalLiabilities,
        isPositive: false,
        displayName: 'Total Liabilities',
        institutionInfo: `${liabilityNodes.length} accounts`,
        uniqueId: 'grouped-liabilities',
        type: 'liabilities-group',
        color: '#f44336'
      });
    }
    return groupedNodes;
  };

  const groupedNodes = createGroupedNodes();

  const getNodeColor = (account) => {
    const type = (account.accountType || account.type || '').toLowerCase();
    const colors = {
      checking: { main: '#8bc34a', glow: '#c8e6c9', icon: Wallet },
      savings: { main: '#4caf50', glow: '#a5d6a7', icon: PiggyBank },
      investment: { main: '#66bb6a', glow: '#a5d6a7', icon: TrendingUpIcon },
      credit: { main: '#f44336', glow: '#ffcdd2', icon: CreditCard },
      loan: { main: '#ff9800', glow: '#ffcc80', icon: CreditCard },
      mortgage: { main: '#e91e63', glow: '#f8bbd9', icon: CreditCard },
      default: { main: '#9e9e9e', glow: '#e0e0e0', icon: Wallet }
    };
    return colors[type] || colors.default;
  };

  if (!propAccounts && isLoading) {
    return (
      <div className={`rounded-lg shadow-sm border p-8 text-center ${darkMode ? 'bg-gray-800 border-gray-700 text-gray-400' : 'bg-white border-gray-200 text-gray-500'}`}>
        Loading accounts...
      </div>
    );
  }

  if (!accounts || accounts.length === 0) {
    return (
      <div className={`rounded-lg shadow-sm border p-8 text-center ${darkMode ? 'bg-gray-800 border-gray-700 text-gray-400' : 'bg-white border-gray-200 text-gray-500'}`}>
        No accounts found.
      </div>
    );
  }

  const totalAssets = assetNodes.reduce((sum, node) => sum + node.balance, 0);
  const totalLiabilities = liabilityNodes.reduce((sum, node) => sum + node.balance, 0);
  const netWorth = totalAssets - totalLiabilities;

  const renderChart = () => {
    const nodesToRender = activeTab === 'all' ? allNodes : groupedNodes;
    return (
      <div className={`w-full h-[600px] relative overflow-hidden rounded-lg border ${darkMode ? 'bg-gradient-to-br from-gray-900 to-gray-800 border-gray-700' : 'bg-gradient-to-br from-gray-50 to-gray-100 border-gray-200'}`}>
        <svg width="100%" height="100%" viewBox="0 0 800 600" className="drop-shadow-sm">
          <defs>
            <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M 20 0 L 0 0 0 20" fill="none" stroke={darkMode ? "#374151" : "#f0f0f0"} strokeWidth="0.5" opacity="0.3"/>
            </pattern>
            <radialGradient id="centerGradient" cx="50%" cy="50%" r="40%">
              <stop offset="0%" stopColor="#8bc34a" stopOpacity="0.2"/>
              <stop offset="100%" stopColor="#8bc34a" stopOpacity="0"/>
            </radialGradient>
            <filter id="glow">
              <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          <circle cx={centerX} cy={centerY} r="120" fill="url(#centerGradient)" />
          {nodesToRender.map((node, index) => {
            const distance = Math.sqrt(Math.pow(node.x - centerX, 2) + Math.pow(node.y - centerY, 2));
            const colors = activeTab === 'all' ? getNodeColor(node) : { main: node.color || '#9e9e9e' };
            return (
              <g key={`connection-${node.uniqueId}`}>
                <line
                  x1={centerX}
                  y1={centerY}
                  x2={node.x}
                  y2={node.y}
                  stroke={darkMode ? "#374151" : "#e0e0e0"}
                  strokeWidth="3"
                  opacity="0.4"
                />
                <line
                  x1={centerX}
                  y1={centerY}
                  x2={node.x}
                  y2={node.y}
                  stroke={colors.main}
                  strokeWidth="4"
                  opacity="0.6"
                  strokeDasharray={`${distance/10},${distance/20}`}
                  strokeDashoffset={animationStep * -2}
                  filter="url(#glow)"
                />
                <circle
                  cx={centerX + (node.x - centerX) * ((animationStep % 100) / 100)}
                  cy={centerY + (node.y - centerY) * ((animationStep % 100) / 100)}
                  r="3"
                  fill={colors.main}
                  opacity={Math.sin((animationStep * Math.PI) / 50)}
                />
              </g>
            );
          })}
          <g filter="url(#glow)">
            <circle cx={centerX} cy={centerY} r="28" fill="none" stroke="#8bc34a" strokeWidth="4" opacity="0.6" />
            <circle cx={centerX} cy={centerY} r="20" fill="#8bc34a" stroke="#fff" strokeWidth="3" />
            <circle cx={centerX} cy={centerY} r="12" fill="#fff" />
          </g>
          {nodesToRender.map((node) => {
            const colors = activeTab === 'all' ? getNodeColor(node) : { main: node.color || '#9e9e9e', glow: node.color + '40' || '#9e9e9e40' };
            return (
              <g key={`node-${node.uniqueId}`} className="group cursor-pointer">
                <circle
                  cx={node.x}
                  cy={node.y}
                  r={node.size + 12}
                  fill={colors.glow}
                  opacity="0.4"
                  filter="url(#glow)"
                />
                <circle
                  cx={node.x}
                  cy={node.y}
                  r={node.size}
                  fill={colors.main}
                  stroke="#fff"
                  strokeWidth="4"
                  filter="url(#glow)"
                />
                <circle
                  cx={node.x}
                  cy={node.y}
                  r={node.size - 8}
                  fill="#fff"
                  opacity="0.9"
                />
                <circle
                  cx={node.x + node.size - 10}
                  cy={node.y - node.size + 10}
                  r="8"
                  fill={node.isPositive ? "#4caf50" : "#f44336"}
                  stroke="#fff"
                  strokeWidth="2"
                />
                <text
                  x={node.x}
                  y={node.y + node.size + 20}
                  textAnchor="middle"
                  className={`text-sm font-semibold ${darkMode ? 'fill-gray-300' : 'fill-gray-700'}`}
                  fontSize="12"
                >
                  {node.displayName.split(' ')[0]}
                </text>
                <text
                  x={node.x}
                  y={node.y + node.size + 35}
                  textAnchor="middle"
                  className={`text-xs font-medium ${darkMode ? 'fill-gray-400' : 'fill-gray-600'}`}
                  fontSize="10"
                >
                  ${(node.balance / 1000).toFixed(1)}k
                </text>
              </g>
            );
          })}
        </svg>
      </div>
    );
  };

  return (
    <div className={`dashboard-card rounded-lg shadow-sm border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>
      <div className="flex flex-row items-center justify-between space-y-0 pb-4 px-6 pt-6">
        <span className={`text-lg font-semibold ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Financial Network</span>
        <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
      </div>
      <div className="px-6 pb-6">
        <div className={`flex justify-between items-center mb-6 p-4 rounded-lg border ${darkMode ? 'bg-gradient-to-r from-green-900/20 to-blue-900/20 border-green-700/50' : 'bg-gradient-to-r from-green-50 to-blue-50 border-green-200/50'}`}>
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-full shadow-sm ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
              <DollarSign className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <p className={`text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Net Worth</p>
              <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>Assets - Liabilities</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span className={`font-bold text-xl ${netWorth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              ${netWorth.toLocaleString()}
            </span>
            {netWorth >= 0
              ? <TrendingUp className="h-5 w-5 text-green-600" />
              : <TrendingDown className="h-5 w-5 text-red-600" />}
          </div>
        </div>
        <div className={`flex rounded-lg p-1 mb-6 ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}>
          <button
            onClick={() => setActiveTab('networth')}
            className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors flex items-center justify-center gap-2 ${
              activeTab === 'networth'
                ? 'bg-[#8bc34a] hover:bg-[#6ec122] text-white'
                : `${darkMode ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`
            }`}
          >
            <BarChart3 className="h-4 w-4" />
            Net Worth
          </button>
          <button
            onClick={() => setActiveTab('all')}
            className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors flex items-center justify-center gap-2 ${
              activeTab === 'all'
                ? 'bg-[#8bc34a] hover:bg-[#6ec122] text-white'
                : `${darkMode ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`
            }`}
          >
            <Eye className="h-4 w-4" />
            All Accounts
          </button>
        </div>
        {renderChart()}
        <div className="mt-6 space-y-4">
          {assetNodes.length > 0 && (
            <div className={`rounded-lg border overflow-hidden ${darkMode ? 'bg-green-900/20 border-green-700/50' : 'bg-green-50 border-green-200/50'}`}>
              <button
                onClick={() => setAssetsExpanded(!assetsExpanded)}
                className={`w-full p-4 flex items-center justify-between transition-colors ${darkMode ? 'hover:bg-green-800/30' : 'hover:bg-green-100'}`}
              >
                <div className="flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  <h4 className={`text-sm font-semibold ${darkMode ? 'text-green-300' : 'text-green-700'}`}>Assets</h4>
                  <span className={`text-xs px-2 py-1 rounded-full ${darkMode ? 'bg-green-800 text-green-600' : 'bg-green-100 text-green-600'}`}>
                    {assetNodes.length} accounts
                  </span>
                  <span className={`text-xs px-2 py-1 rounded-full ${darkMode ? 'bg-green-800 text-green-600' : 'bg-green-100 text-green-600'}`}>
                    ${totalAssets.toLocaleString()}
                  </span>
                </div>
                {assetsExpanded ? (
                  <ChevronUp className="h-4 w-4 text-green-600" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-green-600" />
                )}
              </button>
              {assetsExpanded && (
                <div className={`px-4 pb-4 space-y-2 border-t ${darkMode ? 'border-green-700/50' : 'border-green-200/50'}`}>
                  <div className="pt-2" />
                  {assetNodes.map((node) => {
                    const colors = getNodeColor(node);
                    const IconComponent = colors.icon;
                    return (
                      <div key={`asset-list-${node.uniqueId}`} className={`flex items-center justify-between p-3 rounded-lg shadow-sm border ${darkMode ? 'bg-gray-800 border-green-800/50' : 'bg-white border-green-100'}`}>
                        <div className="flex items-center gap-3">
                          <div
                            className="w-8 h-8 rounded-full flex items-center justify-center"
                            style={{ backgroundColor: colors.main }}
                          >
                            <IconComponent className="h-4 w-4 text-white" />
                          </div>
                          <div>
                            <span className={`text-sm font-medium ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
                              {node.displayName}
                            </span>
                            <p className={`text-xs capitalize ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              {node.institutionInfo}
                            </p>
                          </div>
                        </div>
                        <span className="text-sm font-bold text-green-600">
                          ${node.balance.toLocaleString()}
                        </span>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}
          {liabilityNodes.length > 0 && (
            <div className={`rounded-lg border overflow-hidden ${darkMode ? 'bg-red-900/20 border-red-700/50' : 'bg-red-50 border-red-200/50'}`}>
              <button
                onClick={() => setLiabilitiesExpanded(!liabilitiesExpanded)}
                className={`w-full p-4 flex items-center justify-between transition-colors ${darkMode ? 'hover:bg-red-800/30' : 'hover:bg-red-100'}`}
              >
                <div className="flex items-center gap-2">
                  <TrendingDown className="h-4 w-4 text-red-600" />
                  <h4 className={`text-sm font-semibold ${darkMode ? 'text-red-300' : 'text-red-700'}`}>Liabilities</h4>
                  <span className={`text-xs px-2 py-1 rounded-full ${darkMode ? 'bg-red-800 text-red-600' : 'bg-red-100 text-red-600'}`}>
                    {liabilityNodes.length} accounts
                  </span>
                  <span className={`text-xs px-2 py-1 rounded-full ${darkMode ? 'bg-red-800 text-red-600' : 'bg-red-100 text-red-600'}`}>
                    ${totalLiabilities.toLocaleString()}
                  </span>
                </div>
                {liabilitiesExpanded ? (
                  <ChevronUp className="h-4 w-4 text-red-600" />
                ) : (
                  <ChevronDown className="h-4 w-4 text-red-600" />
                )}
              </button>
              {liabilitiesExpanded && (
                <div className={`px-4 pb-4 space-y-2 border-t ${darkMode ? 'border-red-700/50' : 'border-red-200/50'}`}>
                  <div className="pt-2" />
                  {liabilityNodes.map((node) => {
                    const colors = getNodeColor(node);
                    const IconComponent = colors.icon;
                    return (
                      <div key={`liability-list-${node.uniqueId}`} className={`flex items-center justify-between p-3 rounded-lg shadow-sm border ${darkMode ? 'bg-gray-800 border-red-800/50' : 'bg-white border-red-100'}`}>
                        <div className="flex items-center gap-3">
                          <div
                            className="w-8 h-8 rounded-full flex items-center justify-center"
                            style={{ backgroundColor: colors.main }}
                          >
                            <IconComponent className="h-4 w-4 text-white" />
                          </div>
                          <div>
                            <span className={`text-sm font-medium ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
                              {node.displayName}
                            </span>
                            <p className={`text-xs capitalize ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                              {node.institutionInfo}
                            </p>
                          </div>
                        </div>
                        <span className="text-sm font-bold text-red-600">
                          ${node.balance.toLocaleString()}
                        </span>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default NetworkGraphCard;