// import React, { useEffect } from 'react';
// import { useSelector, useDispatch } from 'react-redux';
// import { useNavigate } from 'react-router-dom';
// import { fetchTransactionsStart } from '../../../../logic/redux/transactionSlice';

// const RecentTransactions = ({ darkMode }) => {
//   const dispatch = useDispatch();
//   const navigate = useNavigate();
  
//   // Get allTransactions from Redux store
//   const { allTransactions, loading, error } = useSelector((state) => state.transactions);
  
//   useEffect(() => {
//     // Fetch transactions if they haven't been loaded yet
//     if (allTransactions.length === 0 && !loading) {
//       dispatch(fetchTransactionsStart());
//     }
//   }, [dispatch, allTransactions.length, loading]);

//   // Get the 10 most recent transactions
//   const recentTransactions = React.useMemo(() => {
//     if (!allTransactions || allTransactions.length === 0) return [];
    
//     // Sort transactions by date (newest first)
//     return [...allTransactions]
//       .sort((a, b) => {
//         const dateA = new Date(a.transactionDate);
//         const dateB = new Date(b.transactionDate);
//         return dateB - dateA; // Sort in descending order (newest first)
//       })
//       .slice(0, 10); // Take only the first 10
//   }, [allTransactions]);

//   // Format date from ISO string to MM/DD
//   const formatDate = (dateString) => {
//     if (!dateString) return '';
//     const date = new Date(dateString);
//     return `${date.getMonth() + 1}/${date.getDate()}`;
//   };

//   // Handle click on the component to navigate to transactions page
//   const handleComponentClick = () => {
//     navigate('/dashboard/transactions');
//   };

//   if (loading) {
//     return <div className="h-full flex items-center justify-center">Loading transactions...</div>;
//   }

//   if (error) {
//     return <div className="h-full flex items-center justify-center text-red-500">Error loading transactions</div>;
//   }

//   return (
//     <div className={`h-full flex flex-col ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>
//       <div className="flex justify-between items-center mb-4">
//         <div className={`text-xl font-roboto ${darkMode ? 'text-gray-100' : 'text-gray-900'}`}>Recent Transactions</div>
//         <button 
//           onClick={handleComponentClick}
//           className={`text-sm ${darkMode ? 'text-blue-400 hover:text-blue-300' : 'text-blue-500 hover:text-blue-700'} focus:outline-none`}
//         >
//           View all
//         </button>
//       </div>
//       {recentTransactions.length === 0 ? (
//         <div className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>No transactions available</div>
//       ) : (
//         <div className="overflow-auto flex-grow">
//           <table className="min-w-full">
//             <thead className={`${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
//               <tr>
//                 <th className={`py-2 px-3 text-left text-xs font-medium uppercase tracking-wider ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Date</th>
//                 <th className={`py-2 px-3 text-left text-xs font-medium uppercase tracking-wider ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Description</th>
//                 <th className={`py-2 px-3 text-right text-xs font-medium uppercase tracking-wider ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Amount</th>
//               </tr>
//             </thead>
//             <tbody className={`divide-y ${darkMode ? 'divide-gray-700' : 'divide-gray-200'}`}>
//               {recentTransactions.map((tx) => (
//                 <tr 
//                   key={tx.transactionId || tx.id}
//                   onClick={handleComponentClick}
//                   className={`cursor-pointer ${darkMode ? 'hover:bg-gray-800' : 'hover:bg-gray-50'}`}
//                 >
//                   <td className={`py-2 px-3 text-sm ${darkMode ? 'text-gray-100': 'text-gray-800' }`}>
//                     {formatDate(tx.transactionDate)}
//                   </td>
//                   <td className={`py-2 px-3 text-sm ${darkMode ? 'text-gray-100': 'text-gray-800' }`}>
//                     {tx.description || tx.category || 'Unnamed Transaction'}
//                   </td>
//                   <td className={`py-2 px-3 text-sm text-right ${Number(tx.transactionAmount) >= 0 ? 'text-green-500' : 'text-red-500'}`}>
//                     ${Math.abs(Number(tx.transactionAmount || tx.amount)).toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
//                   </td>
//                 </tr>
//               ))}
//             </tbody>
//           </table>
//         </div>
//       )}
//     </div>
//   );
// };

// export default RecentTransactions;
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { fetchTransactionsStart } from '../../../../logic/redux/transactionSlice';
import { GripVertical, ArrowDownRight, ArrowUpRight } from 'lucide-react';
import BankIcon from '../Accounts/BankIcon';
import { useCacheAwareness } from '../../../../logic/components/CacheAwareWrapper';
import { getCurrentUserId } from '../../utils/AuthUtil';

// --- Card Design Components ---
const Card = ({ children, className = '', darkMode, ...props }) => (
  <div className={`rounded-lg shadow-sm border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} ${className}`} {...props}>
    {children}
  </div>
);

const CardHeader = ({ children, className = '', darkMode }) => (
  <div className={`px-6 py-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} ${className}`}>
    {children}
  </div>
);

const CardContent = ({ children, className = '', darkMode }) => (
  <div className={`px-6 py-4 ${className}`}>
    {children}
  </div>
);

const CardTitle = ({ children, className = '', darkMode }) => (
  <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} ${className}`}>
    {children}
  </h3>
);

const RecentTransactions = ({ darkMode }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const userId = getCurrentUserId();

  // Get allTransactions from Redux store
  const { allTransactions, loading, error } = useSelector((state) => state.transactions);

  // Cache awareness for transactions
  const {
    isReady: cacheReady,
    isCacheLoaded,
    isCacheLoading
  } = useCacheAwareness(['transactions'], false);

  useEffect(() => {
    if (allTransactions.length === 0 && !loading) {
      dispatch(fetchTransactionsStart());
    }
  }, [dispatch, allTransactions.length, loading]);

  // Get the 10 most recent transactions
  const recentTransactions = React.useMemo(() => {
    if (!allTransactions || allTransactions.length === 0) return [];
    return [...allTransactions]
      .sort((a, b) => new Date(b.transactionDate) - new Date(a.transactionDate))
      .slice(0, 5);
  }, [allTransactions]);

  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getMonth() + 1}/${date.getDate()}`;
  };

  const formatCurrency = (amount) => {
    return Math.abs(Number(amount)).toLocaleString(undefined, { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    });
  };

  const handleComponentClick = () => {
    navigate('/dashboard/transactions');
  };

  const handleTransactionClick = (transaction) => {
    navigate('/dashboard/transactions');
  };

  if (loading) {
    return (
      <Card className="dashboard-card" darkMode={darkMode}>
        <CardHeader darkMode={darkMode}>
          <CardTitle darkMode={darkMode}>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent darkMode={darkMode}>
          <div className="h-full flex items-center justify-center py-8">
            <div className={`animate-spin rounded-full h-8 w-8 border-b-2 ${darkMode ? 'border-blue-400' : 'border-blue-600'}`}></div>
            <span className={`ml-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Loading transactions...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="dashboard-card" darkMode={darkMode}>
        <CardHeader darkMode={darkMode}>
          <CardTitle darkMode={darkMode}>Recent Transactions</CardTitle>
        </CardHeader>
        <CardContent darkMode={darkMode}>
          <div className={`h-full flex items-center justify-center py-8 ${darkMode ? 'text-red-400' : 'text-red-500'}`}>
            Error loading transactions
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      className="dashboard-card hover:shadow-md transition-shadow cursor-pointer"
      darkMode={darkMode}
      onClick={handleComponentClick}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4" darkMode={darkMode}>
        <CardTitle className="text-lg font-semibold" darkMode={darkMode}>Recent Transactions</CardTitle>
        <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
      </CardHeader>
      <CardContent darkMode={darkMode}>
        {recentTransactions.length === 0 ? (
          <div className={`text-center py-8 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            No transactions available
          </div>
        ) : (
          <div className="space-y-3">
            {recentTransactions.map((transaction) => {
              const isIncome = Number(transaction.transactionAmount || transaction.amount) >= 0;
              const amount = Math.abs(Number(transaction.transactionAmount || transaction.amount));
              
              return (
                <div 
                  key={transaction.transactionId || transaction.id} 
                  className={`flex items-center justify-between py-3 border-b ${darkMode ? 'border-gray-700' : 'border-gray-100'} last:border-b-0 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-900 rounded-lg px-2 transition-colors`}
                  onClick={() => handleTransactionClick(transaction)}
                >
                  <div className="flex items-center space-x-3">
                    <BankIcon
                      institutionName={transaction.merchant || transaction.description}
                      accountType="bank"
                      size={50}
                      sizeClass="sm"
                      className="flex-shrink-0"
                    />
                    <div>
                      <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                        {transaction.description || transaction.category || 'Unnamed Transaction'}
                      </p>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        {transaction.category || 'General'}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-semibold ${
                      isIncome
                        ? 'text-green-400 dark:text-green-400'
                        : 'text-red-400 dark:text-red-400'
                    }`}>
                      {isIncome ? '+' : '-'}${formatCurrency(amount)}
                    </p>
                    <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      {formatDate(transaction.transactionDate)}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
export default RecentTransactions;