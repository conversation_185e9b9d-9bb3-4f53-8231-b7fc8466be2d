import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectRecurringTransactions,
  selectLoading,
  selectError,
  fetchRecurringTransactionsStart
} from '../../../../logic/redux/recurringTransactionsSlice';
import { GripVertical } from 'lucide-react';
import BankIcon from '../Accounts/BankIcon';

// --- Card Design with darkMode support ---
const Card = ({ children, className = '', darkMode }) => (
  <div className={`rounded-lg shadow-sm border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} ${className}`}>
    {children}
  </div>
);

const CardHeader = ({ children, className = '', darkMode }) => (
  <div className={`px-6 py-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} ${className}`}>
    {children}
  </div>
);

const CardContent = ({ children, className = '', darkMode }) => (
  <div className={`px-6 py-4 ${className}`}>
    {children}
  </div>
);

const CardTitle = ({ children, className = '', darkMode }) => (
  <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} ${className}`}>
    {children}
  </h3>
);
// --- End Card Design ---

const RecurringTransactionsCard = ({ darkMode }) => {
  const [expanded, setExpanded] = useState(false);
  const dispatch = useDispatch();

  const allTransactions = useSelector(selectRecurringTransactions);
  const loading = useSelector(selectLoading);
  const error = useSelector(selectError);

  useEffect(() => {
    dispatch(fetchRecurringTransactionsStart());
  }, [dispatch]);

  const visibleTransactions = expanded ? allTransactions : allTransactions.slice(0, 4);

  const totalMonthly = allTransactions.reduce((sum, tx) => sum + parseFloat(tx.amount || 0), 0);

  const formatCurrency = (amount) =>
    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    }).format(date);
  };

  return (
    <Card className="dashboard-card" darkMode={darkMode}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4" darkMode={darkMode}>
        <CardTitle darkMode={darkMode}>Recurring Charges</CardTitle>
        <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
      </CardHeader>
      <CardContent darkMode={darkMode}>
        <div className="space-y-4">
          <div className={`text-center p-4 rounded-lg ${darkMode ? 'bg-green-900/20' : 'bg-green-50'}`}>
            <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>Monthly Total</p>
            <p className={`text-2xl font-bold ${darkMode ? 'text-green-400' : 'text-green-600'}`}>
              {formatCurrency(totalMonthly)}
            </p>
          </div>
          {loading ? (
            <div className="flex justify-center items-center h-24">
              <div className={`animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 ${darkMode ? 'border-green-400' : 'border-[#7fe029]'}`}></div>
            </div>
          ) : error ? (
            <div className={`bg-red-50 border-l-4 border-red-500 p-4 rounded-md my-4 ${darkMode ? 'text-red-300' : 'text-red-700'}`}>
              {error}
            </div>
          ) : (
            <div className="space-y-3">
              {visibleTransactions.map((tx, index) => (
                <div key={`${tx.id}-${index}`} className={`flex items-center justify-between p-3 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                  <div className="flex items-center space-x-3">
                    <BankIcon
                      institutionName={tx.merchant || tx.name}
                      accountType="bank"
                      size={50}
                      sizeClass="sm"
                      className="flex-shrink-0"
                    />
                    <div>
                      <p className={`font-medium ${darkMode ? 'text-white' : 'text-gray-900'}`}>{tx.merchant || tx.name}</p>
                      <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        Next: {formatDate(tx.date || tx.nextDate)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-semibold ${darkMode ? 'text-white' : 'text-gray-900'}`}>{formatCurrency(tx.amount)}</p>
                    <p className={`text-xs capitalize ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>{tx.frequency}</p>
                  </div>
                </div>
              ))}
              {allTransactions.length > 4 && (
                <div className="flex justify-center">
                  <button
                    onClick={() => setExpanded((v) => !v)}
                    className="mt-2 px-4 py-1 rounded-full"
                    style={{ backgroundColor: '#7fe029', color: '#fff' }}
                  >
                    {expanded ? 'Show Less' : `Show All (${allTransactions.length})`}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default RecurringTransactionsCard;