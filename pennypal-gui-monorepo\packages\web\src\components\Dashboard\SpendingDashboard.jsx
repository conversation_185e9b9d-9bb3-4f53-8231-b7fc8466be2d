import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Tooltip } from 'recharts';
import { GripVertical } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { fetchMonthlyExpenses, fetchYearlyExpenses } from '../../../../logic/epics/spendingDashboardEpic';
import { getCurrentUserId } from '../../utils/AuthUtil';

// --- Card Design ---
const Card = ({ children, className = '', darkMode, ...props }) => (
  <div
    className={`rounded-lg shadow-sm border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} ${className}`}
    {...props}
  >
    {children}
  </div>
);

const CardHeader = ({ children, className = '', darkMode }) => (
  <div className={`px-6 py-4 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'} ${className}`}>
    {children}
  </div>
);

const CardContent = ({ children, className = '', darkMode }) => (
  <div className={`px-6 py-4 ${className}`}>
    {children}
  </div>
);

const CardTitle = ({ children, className = '', darkMode }) => (
  <h3 className={`text-lg font-semibold ${darkMode ? 'text-white' : 'text-gray-900'} ${className}`}>
    {children}
  </h3>
);
// --- End Card Design ---

const COLORS = {
  primary: '#7fe029',
  primaryLight: '#eaf7e0',
  primaryDark: '#5db01e',
  white: '#ffffff',
  lightGray: '#f8f9fa',
  textPrimary: '#333333',
  textSecondary: '#666666',
};

const SPENDING_COLORS = [
  '#FF8042', '#FFBB28', '#00C49F', '#0088FE', '#8884d8',
  '#82ca9d', '#a569bd', '#ffa07a', '#20b2aa', '#f08080'
];

const renderCenterLabel = ({ viewBox, value, darkMode }) => {
  const { cx, cy } = viewBox;
  return (
    <g>
      <text
        x={cx}
        y={cy - 10}
        textAnchor="middle"
        dominantBaseline="central"
        fontSize="22"
        fontWeight="bold"
        fill={darkMode ? '#f3f4f6' : COLORS.textPrimary}
      >
        ${value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
      </text>
      <text
        x={cx}
        y={cy + 16}
        textAnchor="middle"
        dominantBaseline="central"
        fontSize="13"
        fill={darkMode ? '#94a3b8' : COLORS.textSecondary}
      >
        Total
      </text>
    </g>
  );
};

const SpendingDashboard = ({ darkMode }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [period, setPeriod] = useState('month');
  const { monthlyExpenses, yearlyExpenses, isLoading, error } = useSelector(
    (state) => state.spendingDashboard
  );
  const userId = getCurrentUserId();

  const expenses = period === 'month' ? monthlyExpenses : yearlyExpenses;

  useEffect(() => {
    if (period === 'month') {
      dispatch(fetchMonthlyExpenses(userId));
    } else {
      dispatch(fetchYearlyExpenses(userId));
    }
  }, [period, userId, dispatch]);

  const processChartData = () => {
    if (!expenses || expenses.length === 0) return [];
    const categoryMap = {};
    expenses.forEach(item => {
      const categoryName = item.categoryName;
      categoryMap[categoryName] = (categoryMap[categoryName] || 0) + item.actualExpense;
    });
    return Object.entries(categoryMap)
      .filter(([, value]) => value > 0)
      .map(([name, value]) => ({ name, value }))
      .sort((a, b) => b.value - a.value);
  };

  const chartData = processChartData();
  const totalSpending = chartData.reduce((sum, item) => sum + item.value, 0);

  const handleCardClick = () => {
    navigate('/dashboard/budget');
  };

  return (
    <Card
      onClick={handleCardClick}
      className="dashboard-card hover:shadow-md transition-shadow cursor-pointer"
      darkMode={darkMode}
    >
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4" darkMode={darkMode}>
        <CardTitle darkMode={darkMode}>Spending by Category</CardTitle>
        <div
          className="flex items-center gap-2"
          onClick={(e) => e.stopPropagation()}
        >
          <div className={`flex rounded-lg p-1 ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
            <button
              onClick={() => setPeriod('month')}
              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                period === 'month'
                  ? 'bg-[#8bc34a] hover:bg-[#6ec122] text-white'
                  : `${darkMode ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`
              }`}
            >
              Monthly
            </button>
            <button
              onClick={() => setPeriod('year')}
              className={`px-3 py-1 text-xs font-medium rounded-md transition-colors ${
                period === 'year'
                  ? 'bg-[#8bc34a] hover:bg-[#6ec122] text-white'
                  : `${darkMode ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'}`
              }`}
            >
              Yearly
            </button>
          </div>
          <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />
        </div>
      </CardHeader>
      <CardContent darkMode={darkMode}>
        {isLoading ? (
          <div className={`flex-1 flex items-center justify-center ${darkMode ? 'text-gray-400' : ''}`}>
            <p>Loading expenses...</p>
          </div>
        ) : error ? (
          <div className={`flex-1 flex items-center justify-center ${darkMode ? 'text-red-400' : 'text-red-500'}`}>
            <p>Error: {error}</p>
          </div>
        ) : chartData.length === 0 ? (
          <div className={`flex-1 flex items-center justify-center ${darkMode ? 'text-gray-400' : ''}`}>
            <p>No expense data available for this {period}</p>
          </div>
        ) : (
          <div className="flex flex-col md:flex-row items-center gap-6">
            <div className="w-full md:w-1/2 flex justify-center">
              <ResponsiveContainer width={240} height={240}>
                <PieChart>
                  <Pie
                    data={chartData}
                    cx="50%"
                    cy="50%"
                    innerRadius={70}
                    outerRadius={100}
                    paddingAngle={2}
                    dataKey="value"
                    label={false}
                  >
                    {chartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={SPENDING_COLORS[index % SPENDING_COLORS.length]} />
                    ))}
                  </Pie>
                  {renderCenterLabel({
                    viewBox: { cx: 120, cy: 120 },
                    value: totalSpending,
                    darkMode
                  })}
                  <Tooltip
                    formatter={(value) => [
                      `$${value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`,
                      'Amount'
                    ]}
                    contentStyle={{
                      backgroundColor: darkMode ? '#1e293b' : COLORS.white,
                      borderColor: darkMode ? '#475569' : COLORS.primaryLight,
                      borderRadius: '8px',
                      color: darkMode ? '#f3f4f6' : COLORS.textPrimary
                    }}
                    labelStyle={{
                      color: darkMode ? '#f3f4f6' : COLORS.textPrimary
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
            <div className="w-full md:w-1/2">
              <div className="space-y-2">
                {chartData.map((category, index) => (
                  <div key={index} className="flex justify-between items-center py-2">
                    <div className="flex items-center">
                      <div
                        className="w-3 h-3 rounded-full mr-2"
                        style={{ backgroundColor: SPENDING_COLORS[index % SPENDING_COLORS.length] }}
                      ></div>
                      <span className={`text-sm ${darkMode ? 'text-gray-200' : ''}`}>{category.name}</span>
                    </div>
                    <div className="flex items-center">
                      <span className={`text-sm font-medium ${darkMode ? 'text-gray-200' : ''}`}>
                        ${category.value.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
                      </span>
                      <span className={`text-xs ml-2 ${darkMode ? 'text-gray-400' : ''}`}>
                        ({Math.round((category.value / totalSpending) * 100)}%)
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SpendingDashboard;