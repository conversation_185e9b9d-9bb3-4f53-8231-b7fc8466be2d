import React, { useEffect, useRef, useState ,useCallback} from 'react';
import { useSelector, useDispatch, } from 'react-redux';
import { FileText, Search, Eye, X, Upload, Download, Filter, ArrowRight, ArrowLeft, Trash2 ,ShoppingBag,User,Clock,Calendar,CreditCard,Hash ,CalendarCheck,CalendarX,Building,MapPin,Code,ChevronDown,Info,CalendarClock,Flag,FileX,DollarSign,Users, Briefcase, CheckSquare, Home,} from 'lucide-react';
import { QRCodeSVG } from 'qrcode.react';
import { Pie<PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts';
import { logEvent } from '../../utils/EventLogger';
import { getCurrentUserId } from '../../utils/AuthUtil';
import { useCacheStatus } from '../../../../logic/hooks/useCacheStatus';
import {
  fetchDocumentsRequest,
  fetchDocumentsSuccess,
  fetchDocumentsFailure,
  setSearchTerm,
  setFilterType,
  setSelectedDocument,
  setShowUploadModal,
  setNewDocumentName,
  setNewDocumentType,
  setViewMode,
  setActiveFolder,
  setSelectedFile,
  setQrData,
  selectDocumentsByFolder,
  selectCurrentFolderDocuments,
  uploadDocumentRequest,
  deleteDocumentRequest,
  generateQrDataRequest,
  selectFilteredDocuments,
  selectDocumentCounts,
  selectStorageUsage,
  selectAllFolders,
  setActivePreviewTab,
  setPreviewStep,
  setPreviewData,
  setIsProcessing,
  fetchDocumentDetailsRequest,
  fetchDocumentDetailsSuccess,
  // addTransactionRequest,
  updatePreviewDataField,
  // selectCurrentFolderDocuments,
} from '../../../../logic/redux/documentSlice';
import PaymentLoader from '../load/PaymentLoader';

import {
  addTransactionRequest,
  updateNewTransaction,
  resetNewTransaction,
  fetchAccountsRequest
} from '../../../../logic/redux/transactionSlice';

const API_URL = import.meta.env.VITE_API_URL;

const DocumentManagement = ({ darkMode }) => {
  const dispatch = useDispatch();
  const {
    searchTerm,
    filterType,
    // selectedDocument,
    showUploadModal,
    newDocumentName,
    newDocumentType,
    viewMode,
    activeFolder,
    selectedFile,
    qrData,
    isGeneratingQr,
    maxStorage,
  } = useSelector((state) => state.documents);

  const filteredDocuments = useSelector(selectFilteredDocuments);
  const documentCounts = useSelector(selectDocumentCounts);
  const { usedStorage, remainingStorage } = useSelector(selectStorageUsage);
  const allFolders = useSelector(selectAllFolders);
  const currentFolderDocuments = useSelector(selectCurrentFolderDocuments);
  const documentsByFolder = useSelector(selectDocumentsByFolder);
  const [showPreviewPage, setShowPreviewPage] = useState(false);
  const fileInputRef = useRef(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [fileObject, setFileObject] = useState(null);
  const previewData = useSelector((state) => state.documents.previewData);
  const previewStep = useSelector((state) => state.documents.previewStep);
  const activePreviewTab = useSelector((state) => state.documents.activePreviewTab);
  const isProcessing = useSelector((state) => state.documents.isProcessing);
  const [activeViewerTab, setActiveViewerTab] = useState('original');
  const selectedDocument = useSelector((state) => state.documents.currentDocument);
  const [activeDataTab, setActiveDataTab] = useState('receipt');
const [showRawJson, setShowRawJson] = useState(false);
const [showAddTransactionModal, setShowAddTransactionModal] = useState(false);
const newTransaction = useSelector((state) => state.transactions.newTransaction);
const accounts = useSelector((state) => state.transactions.accounts); // Get accounts from state
  const loadingAccounts = useSelector((state) => state.transactions.loadingAccounts);
const categories = useSelector(state => state.budget.categories);
const subcategories = useSelector(state => state.budget.subcategories);
  const loading = useSelector((state) => state.documents.loading);
const cache = useSelector(state => state.cache);
const [showAIChat, setShowAIChat] = useState(false);
  const uploading = useSelector((state) => state.documents.uploading); // Document upload

  // Fetch accounts when component mounts or modal opens
 useEffect(() => {
  if (showAddTransactionModal) {
    const userId = getCurrentUserId(); // Get from cookie / token
    dispatch(fetchAccountsRequest({ userId }));
  }
}, [showAddTransactionModal, dispatch]);

useEffect(() => {
  dispatch({ type: 'budget/fetchCategories' });
  dispatch({ type: 'budget/fetchSubcategories' });
}, [dispatch]);

const handleAddTransaction = (e) => {
  e.preventDefault();
   logEvent('Transaction', 'AddTransaction', {
      date: newTransaction.date,
      amount: newTransaction.amount,
      category: newTransaction.category
    });

  const { date, description, category, account, amount } = newTransaction;

  if (!date || !description || !category || !account || !amount) {
    alert('Please fill all required fields');
    return;
  }

  // Create new transaction object
  const newTx = {
    transactionDate: date,
    description,
    category,
    account,
    transactionAmount: parseFloat(amount),
    id: Date.now() // temporary ID
  };

  // Update preview data immediately
  if (previewData?.azureResponse?.matchingTransactions) {
    const updatedPreview = {
      ...previewData,
      azureResponse: {
        ...previewData.azureResponse,
        matchingTransactions: [
          ...previewData.azureResponse.matchingTransactions,
          newTx
        ]
      }
    };
    dispatch(setPreviewData(updatedPreview));
  }

  // Send to server
  dispatch(addTransactionRequest(newTransaction));
  dispatch(resetNewTransaction());
  setShowAddTransactionModal(false);
};
  // Simple cache status check
  const cacheStatus = useCacheStatus('userReceipts');
  // const documentDetails = useSelector(state => state.documents.documentDetails);
  useEffect(() => {
    // Use cached data if available, otherwise fetch
    if (filteredDocuments && filteredDocuments.length > 0) {
      return; // Already have data, don't fetch again
    }

    if (cacheStatus.isLoaded) {
      console.log('✅ Using cached user receipts in Documents component');
      // Documents component expects the data in documents.documents format
      // So we need to dispatch the success action with cached data
      if (cacheStatus.data && cacheStatus.data.length >= 0) {
        dispatch(fetchDocumentsSuccess(cacheStatus.data));
      }
    } else if (!cacheStatus.isLoading && !loading) {
      console.log('🔄 User receipts not cached, fetching documents');
      dispatch(fetchDocumentsRequest());
    }
  }, [dispatch, cacheStatus.isLoaded, cacheStatus.isLoading, filteredDocuments?.length, loading]);

  const storageData = [
    { name: 'Used', value: usedStorage, color: darkMode ? '#a5d6a7' : '#8bc34a' },
    { name: 'Free', value: remainingStorage, color: darkMode ? '#374151' : '#c5e1a5' }
  ];
  // Create preview when file is selected
  useEffect(() => {
    if (fileObject && fileObject instanceof Blob) {
      const url = URL.createObjectURL(fileObject);
      setPreviewUrl(url);

      return () => {
        URL.revokeObjectURL(url);
      };
    } else {
      setPreviewUrl(null);
    }
  }, [fileObject]);

  const handleViewDocument = (doc) => {
     logEvent('DocumentViewer', 'ViewDocument', { 
      documentId: doc.id, 
      documentType: doc.type 
    });
    dispatch(setSelectedDocument(doc));
    dispatch(fetchDocumentDetailsRequest({ documentId: doc.id }));

  };

  const handleCloseDocument = useCallback(() => {
        logEvent('DocumentViewer', 'CloseDocument');

    console.log('Close button clicked'); // Debug log
    dispatch(fetchDocumentDetailsSuccess(null)); 
    dispatch(setSelectedDocument(null));
  }, [dispatch]);

  const handleFolderSelect = (folder) => {
        logEvent('DocumentManagement', 'SelectFolder', { folder });
    dispatch(setActiveFolder(folder));
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
     logEvent('DocumentUpload', 'FileSelected', { 
      fileName: file?.name, 
      fileSize: file?.size 
    });
    if (file) {
      // Store metadata in Redux
      dispatch(setSelectedFile({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      }));

      // Store actual File object in local state
      setFileObject(file);

      if (!newDocumentName) {
        dispatch(setNewDocumentName(file.name.split('.')[0]));
      }
    } else {
      dispatch(setSelectedFile(null)); // Explicitly handle null case
      setFileObject(null);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
  };

  const handleDrop = (e) => {
    e.preventDefault();
    const file = e.dataTransfer.files[0];
    if (file) {
      dispatch(setSelectedFile({
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      }));
      setFileObject(file);
      if (!newDocumentName) {
        dispatch(setNewDocumentName(file.name.split('.')[0]));
      }
    } else {
      dispatch(setSelectedFile(null));
      setFileObject(null);
    }
  };

  const handleSelectDocument = (documentId) => {
    dispatch(fetchDocumentDetailsRequest({ documentId }));
  };
  const handleUploadDocument = () => {
    if (!newDocumentName || !fileObject) return;

      logEvent('DocumentUpload', 'UploadInitiated', {
      documentName: newDocumentName,
      documentType: newDocumentType,
      fileSize: fileObject.size
    });
    const fileSizeBytes = fileObject.size;
    const fileSizeMB = fileSizeBytes / (1024 * 1024);

    if (usedStorage + fileSizeMB > maxStorage) {
       logEvent('DocumentUpload', 'UploadFailed', { 
        reason: 'InsufficientStorage',
        remainingStorage: remainingStorage
      });
      alert(`Not enough storage space. You have ${remainingStorage.toFixed(2)}MB remaining.`);
      return;
    }

    const formData = new FormData();
    formData.append('file', fileObject);
    formData.append('docName', newDocumentName);
    formData.append('docType', newDocumentType);
    formData.append('size', fileSizeBytes.toString()); // Ensure string conversion
    formData.append('category', activeFolder === 'All Documents' ? 'Uncategorized' : activeFolder);
    formData.append('qrData', qrData);

    dispatch(uploadDocumentRequest(formData));
  };

  const generateQrData = () => {
        logEvent('DocumentUpload', 'GenerateQRCode');
    dispatch(generateQrDataRequest());
  };

  useEffect(() => {
    if (!qrData) {
      generateQrData();
    }
  }, [qrData]);

  const handleDownload = (doc) => {
  const url = doc.scannedCopyPath || doc.filePath;

  fetch(url)
    .then(res => res.blob())
    .then(blob => {
      const link = document.createElement('a');
      link.href = window.URL.createObjectURL(blob);
      link.download = `${doc.docName || 'document'}.pdf`;
      document.body.appendChild(link);
      link.click();
      link.remove();
    })
    .catch(err => {
      console.error('Download failed:', err);
      alert('Failed to download file.');
    });
};

 if (loading) {
    return <PaymentLoader />;
  }
  
  return (
    <div 
    className={`flex flex-col p-6 min-h-screen font-sans ${darkMode ? 'bg-gray-900 text-white' : 'bg-white text-black'}`} 
    style={{
      // backgroundImage: darkMode ? "radial-gradient(#ffffff15 1px, transparent 1px)" : "radial-gradient(#8bc34a15 1px, transparent 1px)",
      backgroundSize: "20px 20px",
      fontFamily: "Roboto, sans-serif"
    }}>
      <header className="mb-8">
  <div className={`text-2xl mb-6 flex items-center justify-between ${darkMode ? 'text-gray-100' : 'text-gray-800'}`}>
    <span>BookKeeping</span>
    <button
      className="p-2 rounded-full transition-all duration-200 hover:scale-110 bg-white border border-slate-200 shadow-lg"
      onClick={() => setShowAIChat(true)}
      title="AI Assistant"
    >
      <svg
        className="w-6 h-6 text-purple-500"
        fill="currentColor"
        viewBox="0 0 24 24"
      >
        <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
        <path d="M19.5 8.5L20.5 11L23 12L20.5 13L19.5 15.5L18.5 13L16 12L18.5 11L19.5 8.5Z" />
        <path d="M4.5 16.5L5.5 19L8 20L5.5 21L4.5 23.5L3.5 21L1 20L3.5 19L4.5 16.5Z" />
      </svg>
    </button>
  </div>
        <div className="flex items-center mt-2">
          {/* <div className="h-1 w-16 rounded-full" style={{ backgroundColor: "#8bc34a" }}></div> */}
          {/* <p className="text-gray-600 ml-3">View, search and organize your documents</p> */}
        </div>
      </header>
      {/* Storage Usage Chart */}
 

      {/* Document Stats Cards - Fixed layout and slider alignment */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-8">
      <div className={`p-5 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 flex flex-col justify-between ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
      <p className={`text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Total</p>
      <p className={`text-2xl font-bold ${darkMode ? 'text-blue-300' : 'text-blue-600'}`}>{documentCounts.total}</p>
      <div className={`mt-4 h-1 w-12 rounded-full ${darkMode ? 'bg-blue-900' : 'bg-blue-200'}`}></div>
        </div>
          <div className={`p-5 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 flex flex-col justify-between ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <p className={`text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Invoices</p>
<p className={`text-2xl font-bold ${darkMode ? 'text-purple-300' : 'text-purple-600'}`}>{documentCounts.invoice}</p>
<div className={`mt-4 h-1 w-12 rounded-full ${darkMode ? 'bg-purple-900' : 'bg-purple-200'}`}></div>
        </div>
          <div className={`p-5 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 flex flex-col justify-between ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <p className={`text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Receipts</p>
          <p className={`text-2xl font-bold ${darkMode ? 'text-pink-300' : 'text-pink-600'}`}>{documentCounts.receipt}</p>
          <div className={`mt-4 h-1 w-12 rounded-full ${darkMode ? 'bg-pink-900' : 'bg-pink-200'}`}></div>
        </div>
          <div className={`p-5 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 flex flex-col justify-between ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <p className={`text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>ID Documents</p>
          <p className={`text-2xl font-bold ${darkMode ? 'text-yellow-300' : 'text-yellow-600'}`}>{documentCounts.id}</p>
          <div className={`mt-4 h-1 w-12 rounded-full ${darkMode ? 'bg-yellow-900' : 'bg-yellow-200'}`}></div>
        </div>
          <div className={`p-5 rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 flex flex-col justify-between ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
          <p className={`text-sm font-medium mb-1 ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Other</p>
          <p className={`text-2xl font-bold ${darkMode ? 'text-red-300' : 'text-red-600'}`}>{documentCounts.other}</p>
          <div className={`mt-4 h-1 w-12 rounded-full ${darkMode ? 'bg-red-900' : 'bg-red-200'}`}></div>
        </div>
      </div>
<div className={`rounded-xl shadow-sm p-6 mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>       
  <div className="flex flex-col md:flex-row items-center justify-between">
    <div className="mb-4 md:mb-0 w-full"> {/* Added w-full here */}
      <h2 className={`text-xl font-semibold mb-2 ${darkMode ? 'text-green-300' : 'text-[#558b2f]'}`}></h2>          
      <div className="flex items-center w-full"> {/* Added w-full here */}
        <div className={`h-2 w-full rounded-full overflow-hidden ${darkMode ? 'bg-gray-700' : 'bg-gray-100'}`}> {/* Changed md:w-64 to w-full */}            
          <div
            className="h-full rounded-full"
            style={{
              width: `${(usedStorage / maxStorage) * 100}%`,
              backgroundColor: darkMode ? "#a5d6a7" : "#8bc34a" // Lighter green in dark mode
            }}
          ></div>
        </div>
        <span className={`ml-3 text-sm font-medium whitespace-nowrap ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          {usedStorage.toFixed(2)}MB / {maxStorage}MB
        </span>
      </div>
    </div>
        </div>
      </div>
      {/* Controls - Improved layout for view toggle buttons */}
      <div className={`rounded-xl shadow-sm p-5 mb-8 ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>     
           <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          {/* Search field - Full width on mobile, fixed width on desktop */}
          <div className="relative w-full md:w-64">
          <Search className={`w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 ${darkMode ? 'text-gray-300' : 'text-gray-400'}`} />
            <input
              type="text"
              placeholder="Search documents..."
              value={searchTerm}
              onChange={(e) => dispatch(setSearchTerm(e.target.value))}
              className={`pl-9 pr-3 py-2.5 w-full border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-200 text-black'}`}
            />
          </div>

          {/* View mode and filter controls */}
          <div className="flex flex-wrap items-center gap-3 justify-between md:justify-end">
            {/* View mode toggle buttons - Final working version */}
  <div className={`flex items-center  p-1 rounded-lg border  ${darkMode ? 'bg-gray-700 border-gray-700' : 'bg-gray-100 border-gray-200'}`}>
              <button
                onClick={() => {
                  console.log('Setting view to list'); // Debugging
                  dispatch(setViewMode('list'));
                }}
      className={`px-4 py-2 rounded-md transition-all duration-200 font-medium ${
        viewMode === 'list' 
        ? darkMode ? 'bg-[#558b2f] text-white shadow-md' : 'bg-[#8bc34a] text-white shadow-md'
        : darkMode ? 'bg-gray-700 text-gray-300 border-gray-600 hover:bg-gray-600' : 'bg-white text-gray-800 border-gray-300 hover:bg-gray-50'
                  }`}
                style={{
                  // border: '1px solid #e2e8f0',
                  backgroundColor: viewMode === 'list' ? '#8bc34a' : '',
                  color: viewMode === 'list' ? 'white' : 'black'
                }}
              >
                List View
              </button>
              <button
                onClick={() => {
                  console.log('Setting view to folder'); // Debugging
                  dispatch(setViewMode('folder'));
                }}
      className={`px-4 py-2 rounded-md transition-all duration-200 font-medium ${
        viewMode === 'folder' 
          ? darkMode ? 'bg-[#558b2f] text-white shadow-md' : 'bg-[#8bc34a] text-white shadow-md'
        : darkMode ? 'bg-gray-700 text-gray-300 border-gray-600 hover:bg-gray-600' : 'bg-white text-gray-800 border-gray-300 hover:bg-gray-50'
                  }`}
                style={{
                  backgroundColor: viewMode === 'folder' ? '#8bc34a' : '',
                  color: viewMode === 'folder' ? 'white' : 'black'
                }}
              >
                Folder View
              </button>
            </div>

            {/* Filter dropdown */}
            <div className="flex items-center">
              <Filter className="w-4 h-4 text-gray-500 mr-2" />
              <select
                value={filterType}
                onChange={(e) => dispatch(setFilterType(e.target.value))}
                className={`border rounded-lg px-3 py-2.5 focus:outline-none focus:ring-2 focus:ring-green-500 ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-200 text-black'}`}
              >
                <option value="all">All Types</option>
                <option value="invoice">Invoices</option>
                <option value="receipt">Receipts</option>
                <option value="id">ID Documents</option>
                <option value="contract">Contracts</option>
                <option value="contract">Tax Documents</option>

              </select>
            </div>
            

{showAIChat && (
  <>
    <div
      className="fixed inset-0 bg-black/10 z-40"
      onClick={() => setShowAIChat(false)}
    />
    <div
      className="fixed top-0 right-0 h-full w-96 border-l shadow-2xl z-50 transform transition-transform duration-300 ease-in-out"
      style={{
        backgroundColor: "#fff",
        borderColor: "#e2e8f0"
      }}
    >
      <div className="flex justify-between items-center p-4 border-b border-gray-200">
        <div className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-purple-500">
            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
              <path d="M19.5 8.5L20.5 11L23 12L20.5 13L19.5 15.5L18.5 13L16 12L18.5 11L19.5 8.5Z" />
              <path d="M4.5 16.5L5.5 19L8 20L5.5 21L4.5 23.5L3.5 21L1 20L3.5 19L4.5 16.5Z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-semibold">AI Assistant</h3>
            <p className="text-xs text-gray-600">
              Document insights & advice
            </p>
          </div>
        </div>
        <button
          onClick={() => setShowAIChat(false)}
          className="p-2 rounded-lg transition-all duration-200 hover:scale-110 text-gray-600 hover:text-gray-800 hover:bg-gray-100"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <div className="flex-1 p-4 overflow-y-auto bg-gray-50/30">
        <div className="space-y-4">
          <div className="flex items-start gap-2">
            <div className="p-1.5 rounded-full bg-purple-500 flex-shrink-0 mt-1">
              <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
              </svg>
            </div>
            <div className="rounded-xl p-3 bg-white text-gray-800 shadow-sm">
              <p className="text-sm">👋 Hi! I can help you with:</p>
              <ul className="mt-2 space-y-1 text-xs">
                <li>• Document search</li>
                <li>• Receipt/invoice analysis</li>
                <li>• Data extraction</li>
                <li>• Compliance tips</li>
              </ul>
              <p className="mt-2 text-xs">What can I help you with?</p>
            </div>
          </div>
          <div className="grid grid-cols-1 gap-2">
            {[
              "Analyze document",
              "Search receipts",
              "Extract data",
              "Compliance advice"
            ].map((suggestion) => (
              <button
                key={suggestion}
                className="px-3 py-2 rounded-lg text-sm transition-all duration-200 hover:scale-[1.02] text-left bg-white hover:bg-gray-50 text-gray-700 border border-gray-200 shadow-sm"
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      </div>
      <div className="p-4 border-t border-gray-200">
        <div className="space-y-3">
          <textarea
            placeholder="Ask about your documents..."
            className="w-full p-3 rounded-xl resize-none transition-all duration-200 text-sm bg-white border border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 focus:outline-none"
            rows={2}
          />
          <button className="w-full py-2.5 bg-purple-500 hover:bg-purple-600 text-white rounded-xl transition-all duration-200 hover:scale-[1.02] shadow-lg text-sm font-medium">
            Send Message
          </button>
        </div>
      </div>
    </div>
  </>
)}

            {/* Upload button */}
            <button
              onClick={() => dispatch(setShowUploadModal(true))}
              className="text-white px-4 py-2.5 rounded-lg flex items-center shadow-sm hover:shadow-md transition-all duration-200"
              style={{ backgroundColor: darkMode ? "#558b2f" : "#8bc34a" }}            >
              <Upload className="w-4 h-4 mr-2" />
              Upload
            </button>
          </div>
        </div>
      </div>

      {/* Document List or Folder View */}
      <div className={`rounded-xl shadow-sm p-6 flex-grow overflow-hidden ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
      <h2 className={`text-xl font-semibold mb-6 flex items-center ${darkMode ? 'text-green-300' : 'text-[#558b2f]'}`}>
          <FileText className="w-5 h-5 mr-2" style={{ color: darkMode ? '#a5d6a7' : '#8bc34a' }} />
          {activeFolder === 'All Documents' ? ' Documents' : activeFolder}
        </h2>

        {filteredDocuments.length === 0 ? (
          <div className="text-center py-16">
            <div className="mx-auto w-24 h-24 rounded-full bg-gray-50 flex items-center justify-center mb-6">
              <FileText className="w-12 h-12 text-gray-300" />
            </div>
            <p className="text-gray-500 text-lg font-medium mb-2">No documents found</p>
            <p className="text-gray-400 mb-8">Try a different search or upload a new document</p>
            <button
              onClick={() => dispatch(setShowUploadModal(true))}
              className="inline-flex items-center px-5 py-2.5 rounded-lg text-white mx-auto"
              style={{ backgroundColor: "#8bc34a" }}
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload Your First Document
            </button>
          </div>
        ) : viewMode === 'list' ? (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
              <tr className={`border-b ${darkMode ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-200 hover:bg-gray-50'}`}>                  
                <th className={`px-4 py-2 text-left text-sm font-semibold ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Document Name</th>
              <th className={`px-4 py-2 text-left text-sm font-semibold ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Type</th>
              <th className={`px-4 py-2 text-left text-sm font-semibold ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Date</th>
              <th className={`px-4 py-2 text-left text-sm font-semibold ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Category</th>
              <th className={`px-4 py-2 text-left text-sm font-semibold ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Amount</th>
              <th className={`px-4 py-2 text-left text-sm font-semibold ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredDocuments.map((doc) => (
                  <tr key={doc.id} className={`border-b ${darkMode ? 'border-gray-700 hover:bg-gray-700' : 'border-gray-200 hover:bg-gray-50'}`}>
                    <td className="px-4 py-3">
                      <div className="flex items-center">
                      <div className={`p-1 rounded mr-2 ${darkMode ? 'bg-gray-700' : 'bg-[#f1f8e9]'}`}>
                          <FileText className="w-4 h-4 text-green-600" style={{ color: darkMode ? '#a5d6a7' : '#8bc34a' }} />
                        </div>
                        <span className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>{doc.docName || doc.name}</span>
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <span className="px-2 py-1 rounded-full text-xs font-medium inline-block"
                        style={{
                          backgroundColor: darkMode 
                            ? (doc.type === 'invoice' ? '#2e7d32' : doc.type === 'receipt' ? '#33691e' : doc.type === 'id' ? '#558b2f' : '#424242')
                            : (doc.type === 'invoice' ? '#e8f5e9' : doc.type === 'receipt' ? '#f1f8e9' : doc.type === 'id' ? '#f9fbe7' : '#f5f5f5'),
                          color: darkMode ? 'white' : (doc.type === 'invoice' ? '#558b2f' : doc.type === 'receipt' ? '#689f38' : doc.type === 'id' ? '#7cb342' : '#616161')
                        }}>
                        {doc.docType || doc.type}
                      </span>
                    </td>
                    <td className={`px-4 py-3 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {doc.uploadDate ? new Date(doc.uploadDate).toLocaleDateString() : doc.date}
                    </td>
               <td className={`px-4 py-3 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>{doc.category}</td>
                    <td className={`px-4 py-3 text-sm text-canter font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
  {doc.amount > 0 ? `$${doc.amount.toFixed(2)}` : 
   doc.transTotal ? `$${parseFloat(doc.transTotal).toFixed(2)}` : '-'}
</td>
                    <td className="px-4 py-3 text-center">
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleViewDocument(doc)}
                          className={`p-2 rounded-full transition-colors ${darkMode ? 'hover:bg-gray-600' : 'hover:bg-green-100'}`}
                          style={{ color: darkMode ? '#a5d6a7' : '#8bc34a' }}
                          aria-label="View document"
                        >
                          <Eye className="w-5 h-5" />
                        </button>
                        <button
  onClick={() => {
    if(window.confirm('Are you sure you want to delete this document?')) {
      dispatch(deleteDocumentRequest(doc.id))
    }
  }}
  className={`p-2 rounded-full transition-colors ${darkMode ? 'hover:bg-gray-600' : 'hover:bg-red-100'}`}
                  style={{ color: darkMode ? '#f87171' : '#ef5350' }}
>
  <Trash2 className="w-4 h-4 mr-1" /> Delete
</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          // Folder View - Improved card alignment and consistency
          <div>
          {/* Folder navigation */}
          <div className="flex overflow-x-auto pb-4 mb-6 space-x-3 border-b">
            {Object.keys(documentsByFolder).map(folder => (
              <button
                key={folder}
                onClick={() => dispatch(setActiveFolder(folder))}
                className="px-5 py-2.5 rounded-lg text-sm whitespace-nowrap transition-all duration-200 flex items-center"
                style={{
                  backgroundColor: folder === activeFolder ? '#8bc34a' : '#f1f8e9',
                  color: folder === activeFolder ? 'white' : '#558b2f'
                }}
              >
                {folder === 'All Documents' ?
                  <FileText className="w-4 h-4 mr-2" /> :
                  <div className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: folder === activeFolder ? 'white' : '#8bc34a' }}></div>
                }
                {folder}
                <span className="ml-2 bg-white bg-opacity-20 px-2 py-0.5 rounded-full text-xs">
                  {documentsByFolder[folder]?.length || 0}
                </span>
              </button>
            ))}
          </div>

          {/* Documents in grid - Improved card alignment */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
            {currentFolderDocuments.length === 0 ? (
              <div className="col-span-full text-center py-10 bg-gray-50 rounded-xl">
                <FileText className="w-10 h-10 mx-auto text-gray-300 mb-3" />
                <p className="text-gray-500">No documents in this folder</p>
              </div>
            ) : (
              currentFolderDocuments.map(doc => (
                <div
                  key={doc.id}
                    className={`flex flex-col rounded-xl border h-full overflow-hidden hover:shadow-md transition-all duration-300 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-[#e8f5e9]'}`}                    style={{ borderColor: '#e8f5e9' }}
                >
                    <div className={`flex items-center p-3 border-b ${darkMode ? 'bg-gray-900 border-gray-700' : 'bg-[#fcfdf9] border-[#f1f8e9]'}`}>
                    <div className={`p-2 rounded-lg mr-3 ${darkMode ? 'bg-gray-700' : 'bg-[#f1f8e9]'}`}>
                    <FileText className="w-5 h-5" style={{ color: darkMode ? '#a5d6a7' : '#8bc34a' }} />                      </div>
                    <div className="overflow-hidden flex-grow">
                      <p className="text-sm font-medium truncate" title={doc.docName || doc.name}>
                        {doc.docName || doc.name}
                      </p>                      </div>
                  </div>

                  <div className="p-3 flex-grow">
                    <div className="flex items-center justify-between mb-2">
                      <span className="px-2 py-1 rounded-full text-xs font-medium"
                                     stystyle={{ 
                                      backgroundColor: darkMode 
                                        ? (doc.type === 'invoice' ? '#2e7d32' : doc.type === 'receipt' ? '#33691e' : doc.type === 'id' ? '#558b2f' : '#424242')
                                        : (doc.type === 'invoice' ? '#e8f5e9' : doc.type === 'receipt' ? '#f1f8e9' : doc.type === 'id' ? '#f9fbe7' : '#f5f5f5'),
                                      color: darkMode ? 'white' : (doc.type === 'invoice' ? '#558b2f' : doc.type === 'receipt' ? '#689f38' : doc.type === 'id' ? '#7cb342' : '#616161')
                        }}>
                        {doc.docType || doc.type}
                      </span>
            <span className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>          
                        {doc.uploadDate ? new Date(doc.uploadDate).toLocaleDateString() : doc.date}
                      </span>                      </div>
                    <div className="flex items-center mt-2">
                        <span className={`text-xs font-medium ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>Category:</span>
                        <span className={`text-xs ml-1 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>{doc.category}</span>                   
                    </div>
                      {doc.amount > 0 && (
                        <div className="text-sm font-medium mt-2" style={{ color: darkMode ? '#a5d6a7' : '#558b2f' }}>
                          ${doc.amount.toFixed(2)}</div>
)}
                  </div>

                    <div className={`flex border-t mt-auto ${darkMode ? 'border-gray-700' : 'border-[#f1f8e9]'}`}>
                    <button
                      onClick={() => handleViewDocument(doc)}
                        className={`flex-1 py-3 text-center transition-colors ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-green-50'}`}
                        style={{ color: darkMode ? '#a5d6a7' : '#8bc34a' }}
                    >
                      <Eye className="w-4 h-4 mr-1" /> View
                    </button>
                      <div className="w-px" style={{ backgroundColor: darkMode ? '#616161' : '#f1f8e9' }}></div>
                    <button
  onClick={() => {
    if(window.confirm('Are you sure you want to delete this document?')) {
      dispatch(deleteDocumentRequest(doc.id))
    }
  }}
   className={`flex-1 py-3 text-center transition-colors ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-red-50'}`}
      style={{ color: darkMode ? '#f87171' : '#ef5350' }}
>
  <Trash2 className="w-4 h-4 mr-1" /> Delete
</button>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>


      {/* Document Viewer Modal - Fixed close button color */}
      {selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
          <div className="bg-white rounded-xl w-full max-w-3xl overflow-hidden shadow-2xl">
            <div className="flex justify-between items-center p-5 border-b" style={{ backgroundColor: '#fcfdf9', borderColor: '#f1f8e9' }}>
              <h3 className="font-semibold text-lg flex items-center">
                <div className="p-2 rounded-lg mr-3" style={{ backgroundColor: '#f1f8e9' }}>
                  <FileText className="w-5 h-5" style={{ color: darkMode ? '#a5d6a7' : '#8bc34a' }} />
                </div>
                {selectedDocument.name}
              </h3>
              <button
                onClick={handleCloseDocument}
                className="absolute top-4 right-4 p-2 rounded-full bg-white text-gray-700 hover:text-gray-900 hover:bg-gray-100 transition-colors shadow-sm"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="p-6">
              <div className="border rounded-xl p-10 bg-gray-50 flex items-center justify-center min-h-64"
                style={{ borderColor: '#f1f8e9' }}>
                <div className="text-center">
                  <div className="relative mx-auto w-24 h-28 mb-4">
                    <div className="absolute top-0 left-0 w-20 h-24 border-2 rounded-lg transform rotate-[-5deg]"
                      style={{ borderColor: '#dcedc8', backgroundColor: '#f9fbe7' }}></div>
                    <div className="absolute top-0 right-0 w-20 h-24 border-2 rounded-lg transform rotate-[5deg] z-10"
                      style={{ borderColor: '#dcedc8', backgroundColor: '#f1f8e9' }}></div>
                    <FileText className="absolute top-4 left-4 w-16 h-16 text-gray-300 z-20" />
                  </div>
                  <p className="text-gray-500 mb-2 font-medium">Document Preview</p>
                  <p className="text-sm text-gray-400 mb-6">Preview not available in this demo</p>
                  <button className="px-5 py-2.5 text-white rounded-lg flex items-center mx-auto shadow-sm hover:shadow-md transition-all duration-200"
                    style={{ backgroundColor: "#8bc34a" }}>
                    <Download className="w-4 h-4 mr-2" />
                    Download Document
                  </button>
                </div>
              </div>

              <div className="mt-6 grid grid-cols-2 gap-4">
                <div className="p-4 rounded-xl border" style={{ borderColor: '#f1f8e9', backgroundColor: '#fcfdf9' }}>
                  <p className="text-sm font-medium text-gray-500 mb-1">Document Name</p>
                  <p className="text-lg font-medium" style={{ color: '#558b2f' }}>{selectedDocument.docName || selectedDocument.name}</p>
                </div>
                <div className="p-4 rounded-xl border" style={{ borderColor: '#f1f8e9', backgroundColor: '#fcfdf9' }}>
                  <p className="text-sm font-medium text-gray-500 mb-1">Document Type</p>
                  <p className="text-lg font-medium" style={{ color: '#558b2f' }}>{selectedDocument.docType || selectedDocument.type}</p>                </div>
                <div className="p-4 rounded-xl border" style={{ borderColor: '#f1f8e9', backgroundColor: '#fcfdf9' }}>
                  <p className="text-sm font-medium text-gray-500 mb-1">Date</p>
                  <p className="text-lg font-medium" style={{ color: '#558b2f' }}>
                    {selectedDocument.uploadDate ? new Date(selectedDocument.uploadDate).toLocaleDateString() : selectedDocument.date}
                  </p>               
                   </div>
                <div className="p-4 rounded-xl border" style={{ borderColor: '#f1f8e9', backgroundColor: '#fcfdf9' }}>
                  <p className="text-sm font-medium text-gray-500 mb-1">Category</p>
                  <p className="text-lg font-medium" style={{ color: '#558b2f' }}>{selectedDocument.category}</p>                </div>
                <div className="p-4 rounded-xl border" style={{ borderColor: '#f1f8e9', backgroundColor: '#fcfdf9' }}>
                  <p className="text-sm font-medium text-gray-500 mb-1">Amount</p>
                  <p className="text-lg font-medium" style={{ color: '#558b2f' }}>
                  {selectedDocument?.amount != null && selectedDocument.amount > 0
  ? `$${selectedDocument.amount.toFixed(2)}`
  : 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Upload Modal - Fixed close button color */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4 backdrop-blur-sm overflow-y-auto">
<div className={`rounded-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto shadow-2xl ${darkMode ? 'bg-gray-800 text-white' : 'bg-white text-black'}`}>      
  <div className={`flex justify-between items-center p-5 border-b sticky top-0 z-10 ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-[#f1f8e9]'}`}>
              <h3 className="font-semibold text-lg flex items-center">
        <Upload className="w-5 h-5 mr-2" style={{ color: darkMode ? '#a5d6a7' : '#8bc34a' }} />
                {previewStep === 'preview' ? 'Document Preview' : 'Upload New Document'}
              </h3>
              <button
                onClick={() => {
                  dispatch(setShowUploadModal(false));
                  dispatch(setSelectedFile(null));
                  dispatch(setQrData(''));
                  dispatch(setPreviewStep('upload'));
                }}
          className={`p-2 rounded-full transition-colors shadow-sm ${darkMode ? 'bg-gray-700 text-white hover:bg-gray-600' : 'bg-white text-gray-700 hover:bg-gray-100'}`}
                style={{ color: 'black' }}        >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="p-6">
              {previewStep === 'upload' ? (
                <div className="space-y-6">
                  {/* Upload Form */}
                  <div className="mb-4">
            <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>Document Type</label>
                    <select
                      value={newDocumentType}
                      onChange={(e) => {
                        dispatch(setNewDocumentType(e.target.value));
                        if (e.target.value === 'id' && !qrData) {
                          generateQrData();
                        }
                      }}
                className={`w-full px-3 py-2.5 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-200 text-black'}`}              >
                      <option value="receipt">Receipt</option>
                      <option value="invoice">Invoice</option>
                      <option value="id">ID Document</option>
                      <option value="contract">Contract</option>
                      <option value = "tax">Tax Document</option>
                    </select>
                  </div>

                  <div className="mb-4">
            <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-1`}>Document Name</label>          
                <input
                      type="text"
                      value={newDocumentName}
                      onChange={(e) => dispatch(setNewDocumentName(e.target.value))}
                      placeholder="Enter document name"
                className={`w-full px-3 py-2.5 border rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 ${darkMode ? 'bg-gray-700 border-gray-600 text-white' : 'bg-white border-gray-200 text-black'}`}              />
                  </div>

<div className={`mb-4 p-4 border rounded-lg ${darkMode ? 'bg-gray-900 border-gray-700' : 'bg-[#fcfdf9] border-[#f1f8e9]'}`}>
          <label className={`block text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} mb-2`}></label>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Document QR Code</label>
                    {isGeneratingQr ? (
                      <div className="flex items-center justify-center h-32">
                        <div className="animate-pulse text-gray-500">Generating QR code...</div>
                      </div>
                    ) : (
                      <div className="flex flex-col items-center">
                        <div className="p-2 bg-white rounded border" style={{ borderColor: '#e8f5e9' }}>
                          <QRCodeSVG
                            value={qrData}
                            size={128}
                            level="H"
  fgColor={darkMode ? "#ffffff" : "#558b2f"} 
  bgColor={darkMode ? "#374151" : "#ffffff"}
                          />
                        </div>
                        <button
                          onClick={generateQrData}
        className="mt-2 text-xs text-green-600 hover:text-green-800 border border-gray-300 px-2 py-1 rounded"
                          style={{ color: 'black' }}      >
                          Regenerate QR
                        </button>
                      </div>
                    )}
                  </div>
                  {/* File Upload Section */}
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileSelect}
                    className="hidden"
                    accept=".pdf,.png,.jpg,.jpeg"
                  />
                  <div
className={`border-2 border-dashed rounded-xl p-8 mb-6 transition-colors cursor-pointer ${darkMode ? 'border-gray-600 hover:border-green-700' : 'border-gray-200 hover:border-green-200'}`}              onClick={() => fileInputRef.current?.click()}
                    onDragOver={handleDragOver}
                    onDrop={handleDrop}
                  >
                    <div className="text-center">
              <div className={`w-16 h-16 mx-auto rounded-full flex items-center justify-center mb-3 ${darkMode ? 'bg-gray-700' : 'bg-[#f1f8e9]'}`}>
              <Upload className="w-8 h-8" style={{ color: darkMode ? '#a5d6a7' : '#8bc34a' }} />
                      </div>
                      {selectedFile ? (
                        <>
                  <p className={`mt-2 text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'} truncate`}>{selectedFile.name}</p>
                  <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'} mt-1`}>
                            {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              dispatch(setSelectedFile(null));
                            }}
                      className={`mt-2 text-xs ${darkMode ? 'text-red-400 border-gray-600 hover:text-red-300' : 'text-red-500 border-gray-300 hover:text-red-700'} border px-2 py-1 rounded`}                      style={{ color: 'black' }}                 
                          >
                            Remove file
                          </button>
                        </>
                      ) : (
                        <>
                   <p className={`mt-2 text-sm font-medium ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Click to upload or drag and drop</p>
                   <p className={`text-xs ${darkMode ? 'text-gray-400' : 'text-gray-500'} mt-1`}>PDF, PNG, JPG up to 10MB</p>
                        </>
                      )}
                    </div>
                  </div>

                  <div className="flex justify-end space-x-3">
                    <button
                      onClick={() => dispatch(setShowUploadModal(false))}
                className={`px-4 py-2.5 border rounded-lg transition-colors ${darkMode ? 'text-gray-300 border-gray-600 hover:bg-gray-700' : 'text-gray-700 border-gray-200 hover:bg-gray-50'}`}                style={{ color: 'black' }}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleUploadDocument}
                      className="px-5 py-2.5 text-white rounded-lg shadow-sm hover:shadow-md transition-all"
                style={{ backgroundColor: darkMode ? "#558b2f" : "#8bc34a" }}
                      disabled={!newDocumentName || !selectedFile || isProcessing}
                    >
                      {uploading ? (
                        <span className="flex items-center">
                          <svg className="animate-spin h-4 w-4 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          uploading...
                        </span>
                      ) : (
                        'Preview & Upload'
                      )}
                    </button>
                  </div>
                </div>
              ) : (
                /* Preview Tabs */
                <div className="flex flex-col h-full">
<div className={`flex border-b ${darkMode ? 'border-gray-700' : 'border-[#f1f8e9]'}`}>              <button
                      onClick={() => dispatch(setActivePreviewTab('original'))}
                className={`px-4 py-3 text-sm font-medium border-b-2 ${activePreviewTab === 'original' ? (darkMode ? 'text-green-400 border-green-400' : 'text-green-600 border-green-500') : (darkMode ? 'text-gray-400 border-transparent' : 'text-gray-800 border-transparent')}`}
                style={{ 
                  border: '1px solid #e2e8f0',
                  marginRight: '4px',
                  color: 'black'
                }}
                    >
                      Original
                    </button>
                    <button
                      onClick={() => dispatch(setActivePreviewTab('scanned'))}
                className={`px-4 py-3 text-sm font-medium border-b-2 ${
                  activePreviewTab === 'scanned' 
                  ? (darkMode ? 'text-green-400 border-green-400' : 'text-green-600 border-green-500') : (darkMode ? 'text-gray-400 border-transparent' : 'text-gray-800 border-transparent')}`}
                style={{ 
                  border: '1px solid #e2e8f0',
                  marginRight: '4px',
                  color: 'black'
                }}
                    >
                      Scanned Copy
                    </button>
                    <button
                      onClick={() => dispatch(setActivePreviewTab('data'))}
                className={`px-4 py-3 text-sm font-medium border-b-2 ${
                  activePreviewTab === 'data' 
                  ? (darkMode ? 'text-green-400 border-green-400' : 'text-green-600 border-green-500') : (darkMode ? 'text-gray-400 border-transparent' : 'text-gray-800 border-transparent')}`}
                style={{ 
                  border: '1px solid #e2e8f0',
                  color: 'black'
                }}
                    >
                      Extracted Data
                    </button>
                  </div>

                  <div className="flex-grow p-4 overflow-auto">
                    {activePreviewTab === 'original' && (
                <div className={`flex items-center justify-center h-full ${darkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>                  {previewUrl ? (
                          <img
                            src={previewUrl}
                            alt="Original document"
                            className="max-w-full max-h-96 object-contain shadow-md"
                            style={{ border: '1px solid #e8f5e9' }}
                          />
                        ) : (
                          <div className="text-center text-gray-500">
                            <FileText className="w-16 h-16 mx-auto mb-4" />
                            <p>Original document preview not available</p>
                          </div>
                        )}
                      </div>
                    )}

                    {activePreviewTab === 'scanned' && (
                      <div className="flex items-center justify-center h-full">
                        <div className="relative">
                          {previewUrl ? (
                            <>
                              <div className="relative">
                                {/* Scanned document with visual effects */}
                                <div
                                  className="bg-white p-2 shadow-lg"
                                  style={{
                                    transform: 'rotate(1deg)',
                                    border: '1px solid #e0e0e0',
                                    background: 'linear-gradient(0deg, rgba(0,0,0,0.03) 0%, rgba(0,0,0,0) 2%)'
                                  }}
                                >
                                  <img
                                    src={previewUrl}
                                    alt="Scanned document"
                                    className="max-w-full max-h-96 object-contain"
                                    style={{
                                      filter: 'contrast(1.1) grayscale(0.3)',
                                      border: '1px solid #f0f0f0',
                                      padding: '4px',
                                      backgroundColor: '#f9f9f9'
                                    }}
                                  />
                                </div>

                                {/* Scan lines overlay */}
                                <div
                                  className="absolute inset-0 pointer-events-none"
                                  style={{
                                    background: `
                  repeating-linear-gradient(
                    0deg,
                    rgba(0,0,0,0.03),
                    rgba(0,0,0,0.03) 1px,
                    transparent 1px,
                    transparent 4px
                  )`,
                                    mixBlendMode: 'multiply'
                                  }}
                                ></div>

                                {/* Scan artifacts */}
                                <div className="absolute inset-0 pointer-events-none overflow-hidden">
                                  <div
                                    className="absolute w-full h-0.5 bg-black opacity-5"
                                    style={{ top: '20%', animation: 'scanline 6s linear infinite' }}
                                  ></div>
                                  <div
                                    className="absolute w-full h-0.5 bg-black opacity-5"
                                    style={{ top: '45%', animation: 'scanline 4s linear infinite 1s' }}
                                  ></div>
                                  <div
                                    className="absolute w-full h-0.5 bg-black opacity-5"
                                    style={{ top: '70%', animation: 'scanline 5s linear infinite 2s' }}
                                  ></div>
                                </div>

                                {/* Scanned document label */}
                                <div className="absolute -bottom-3 -right-3 bg-white px-3 py-1 rounded text-xs border flex items-center shadow-sm"
                                  style={{
                                    borderColor: '#e0e0e0',
                                    transform: 'rotate(3deg)'
                                  }}>
                                  <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                                  SCANNED COPY
                                </div>
                              </div>

                              {/* Add some noise for authenticity */}
                              <style>
                                {`
              @keyframes scanline {
                0% { transform: translateY(-100%); }
                100% { transform: translateY(100vh); }
              }
            `}
                              </style>
                            </>
                          ) : (
                            <div className="text-center text-gray-500">
                              <div className="relative mx-auto w-32 h-40 mb-4 bg-gray-100 rounded flex items-center justify-center">
                                <FileText className="w-12 h-12 text-gray-300" />
                                <div className="absolute inset-0" style={{
                                  background: `
                repeating-linear-gradient(
                  0deg,
                  rgba(0,0,0,0.05),
                  rgba(0,0,0,0.05) 1px,
                  transparent 1px,
                  transparent 4px
                )`,
                                  mixBlendMode: 'multiply'
                                }}></div>
                              </div>
                              <p>Scanned copy preview not available</p>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

{activePreviewTab === 'data' && (
  <div className="h-full overflow-auto bg-gray-50 p-4 rounded-lg">
  <div className="bg-white rounded-lg shadow-sm overflow-hidden">
    {previewData?.docType === 'receipt' ? (
      <>
        {/* Tabs for Receipt and Matching Transactions */}
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveDataTab('receipt')}
            className={`px-4 py-3 text-sm font-medium ${
              activeDataTab === 'receipt'
                ? 'text-[#8bc34a] border-b-2 border-[#8bc34a]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          
          >
            Receipt Details
          </button>
          <button
            onClick={() => setActiveDataTab('transactions')}
            className={`px-4 py-3 text-sm font-medium ${
              activeDataTab === 'transactions'
                ? 'text-[#8bc34a] border-b-2 border-[#8bc34a]'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          
          >
            Matching Transactions
          </button>
        </div>

        {/* Receipt Details Tab */}
        {activeDataTab === 'receipt' && (
          <div className="p-4">
            {previewData?.azureResponse ? (
              <div className="receipt-container bg-white max-w-md mx-auto border border-gray-200 rounded-xl">
                {/* Receipt Header */}
                <div className="bg-[#9ccc65] rounded-t-xl p-4 flex items-center justify-between">
                  <div className="flex items-center">
                    <svg
                      className="w-5 h-5 mr-2 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth="2"
                        d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"
                      />
                    </svg>
                    <h2 className="text-xl font-bold text-white">Your Receipt</h2>
                  </div>
                </div>

                {/* Merchant Information */}
                <div className="text-center border-b pb-4" style={{ borderColor: '#e6e6e6' }}>
          <input
            type="text"
            value={previewData.azureResponse.MerchantName || 'Unknown Merchant'}
            onChange={(e) =>
              dispatch(updatePreviewDataField({ path: 'azureResponse.MerchantName', value: e.target.value }))
            }
            className="text-xl font-bold mb-1 w-full text-center border-none focus:ring-2 focus:ring-[#8bc34a]"
          />
          <input
            type="text"
            value={previewData.azureResponse.MerchantAddress || 'N/A'}
            onChange={(e) =>
              dispatch(updatePreviewDataField({ path: 'azureResponse.MerchantAddress', value: e.target.value }))
            }
            className="text-gray-500 w-full text-center border-none focus:ring-2 focus:ring-[#8bc34a]"
          />
          {previewData.azureResponse.MerchantPhoneNumber && (
            <input
              type="text"
              value={previewData.azureResponse.MerchantPhoneNumber}
              onChange={(e) =>
                dispatch(updatePreviewDataField({ path: 'azureResponse.MerchantPhoneNumber', value: e.target.value }))
              }
              className="text-gray-500 w-full text-center border-none focus:ring-2 focus:ring-[#8bc34a]"
            />
          )}
        </div>

        {/* Receipt Details */}
        <div className="grid grid-cols-2 gap-4 p-4">
          <div className="flex justify-center items-center">
            <svg
              className="w-5 h-5 text-gray-500 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
              />
            </svg>
            <div>
              <p className="text-sm text-gray-500">Date</p>
              <input
                type="date"
                value={
                  previewData.azureResponse.TransactionDate
                    ? new Date(previewData.azureResponse.TransactionDate).toISOString().split('T')[0]
                    : ''
                }
                onChange={(e) =>
                  dispatch(
                    updatePreviewDataField({ path: 'azureResponse.TransactionDate', value: e.target.value })
                  )
                }
                className="font-medium border-none focus:ring-2 focus:ring-[#8bc34a]"
              />
            </div>
          </div>
          <div className="flex justify-center items-center">
            <svg
              className="w-5 h-5 text-gray-500 mr-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
            <div>
              <p className="text-sm text-gray-500">Time</p>
              <input
                type="time"
                value={previewData.azureResponse.TransactionTime || ''}
                onChange={(e) =>
                  dispatch(
                    updatePreviewDataField({ path: 'azureResponse.TransactionTime', value: e.target.value })
                  )
                }
                className="font-medium border-none focus:ring-2 focus:ring-[#8bc34a]"
              />
            </div>
          </div>
        </div>

        {/* Items List */}
        <div>
          <div
            className="grid grid-cols-12 gap-2 rounded-t-lg p-3 font-medium text-white"
style={{ backgroundColor: darkMode ? '#558b2f' : '#8bc34a' }}          >
            <div className="col-span-6 text-left">Item</div>
            <div className="col-span-2 text-center">Qty</div>
            <div className="col-span-2 text-right">Price</div>
            <div className="col-span-2 text-right">Total</div>
          </div>
          <div className="border-x border-b rounded-b-lg" style={{ borderColor: '#e6e6e6' }}>
            {previewData.azureResponse.Items?.length > 0 ? (
              <>
                {previewData.azureResponse.Items.map((item, index) => (
                  <div
                    key={index}
                    className="grid grid-cols-12 gap-2 p-3 border-b"
                    style={{
                      borderColor: '#e6e6e6',
                      backgroundColor: index % 2 === 0 ? 'white' : '#f9fbe7',
                    }}
                  >
                    <div className="col-span-6 text-left">
                      <input
                        type="text"
                        value={item.Name || 'Unknown Item'}
                        onChange={(e) =>
                          dispatch(
                            updatePreviewDataField({
                              path: `azureResponse.Items[${index}].Name`,
                              value: e.target.value,
                            })
                          )
                        }
                        className="font-medium w-full border-none focus:ring-2 focus:ring-[#8bc34a]"
                      />
                    </div>
                    <div className="col-span-2 text-center">
                      <input
                        type="number"
                        value={item.Quantity || ''}
                        onChange={(e) =>
                          dispatch(
                            updatePreviewDataField({
                              path: `azureResponse.Items[${index}].Quantity`,
                              value: parseFloat(e.target.value) || 0,
                            })
                          )
                        }
                        className="w-full text-center border-none focus:ring-2 focus:ring-[#8bc34a]"
                      />
                    </div>
                    <div className="col-span-2 text-right">
                      <input
                        type="text"
                        value={item.Price || item.TotalPrice || '$0.00'}
                        onChange={(e) =>
                          dispatch(
                            updatePreviewDataField({
                              path: `azureResponse.Items[${index}].Price`,
                              value: e.target.value,
                            })
                          )
                        }
                        className="w-full text-right border-none focus:ring-2 focus:ring-[#8bc34a]"
                      />
                    </div>
                    <div className="col-span-2 text-right">
                      <input
                        type="text"
                        value={item.TotalPrice || '$0.00'}
                        onChange={(e) =>
                          dispatch(
                            updatePreviewDataField({
                              path: `azureResponse.Items[${index}].TotalPrice`,
                              value: e.target.value,
                            })
                          )
                        }
                        className="w-full text-right font-medium border-none focus:ring-2 focus:ring-[#8bc34a]"
                      />
                    </div>
                  </div>
                ))}
                <div className="p-3 space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="text-gray-600">Subtotal:</div>
                    <input
                      type="text"
                      value={previewData.azureResponse.Subtotal || '$0.00'}
                      onChange={(e) =>
                        dispatch(
                          updatePreviewDataField({ path: 'azureResponse.Subtotal', value: e.target.value })
                        )
                      }
                      className="font-medium text-right border-none focus:ring-2 focus:ring-[#8bc34a]"
                    />
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="text-gray-600">Tax:</div>
                    <input
                      type="text"
                      value={previewData.azureResponse.Tax || '$0.00'}
                      onChange={(e) =>
                        dispatch(
                          updatePreviewDataField({ path: 'azureResponse.Tax', value: e.target.value })
                        )
                      }
                      className="font-medium text-right border-none focus:ring-2 focus:ring-[#8bc34a]"
                    />
                  </div>
                  <div
                    className="flex justify-between items-center border-t pt-2"
                    style={{ borderColor: '#e6e6e6' }}
                  >
                    <div className="font-bold">Total:</div>
                    <input
                      type="text"
                      value={previewData.azureResponse.Total || '$0.00'}
                      onChange={(e) =>
                        dispatch(
                          updatePreviewDataField({ path: 'azureResponse.Total', value: e.target.value })
                        )
                      }
                      className="font-bold text-right border-none focus:ring-2 focus:ring-[#8bc34a]"
                    />
                  </div>
                </div>
              </>
            ) : (
              <div className="p-4 text-center text-gray-500">No items found</div>
            )}
          </div>
        </div>
      </div>
    ) : (
      <p className="text-center text-gray-500 py-8">No receipt data available</p>
    )}
  </div>
)}

    {/* Matching Transactions Tab */}
    {activeDataTab === 'transactions' && (
      <div className="p-4">
        {/* Add Transaction Button */}
    <div className="flex justify-end mb-4">
      <button
        onClick={() => setShowAddTransactionModal(true)}
        className="bg-[#8bc34a] text-white px-4 py-2 rounded-lg hover:bg-[#689f38] flex items-center"
        style={{
          border: '1px solid #e2e8f0',
          color: 'black'
        }}
      >
        <svg
          className="w-5 h-5 mr-2"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M12 4v16m8-8H4"
          />
        </svg>
        Add Transaction
      </button>
    </div>
    {Array.isArray(previewData?.azureResponse?.matchingTransactions) &&
              previewData.azureResponse.matchingTransactions.length > 0 ? (
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                  {/* Grid Header */}
                  <div className="grid grid-cols-12 gap-4 bg-gray-100 p-3 font-semibold text-sm text-gray-700 border-b border-[#8bc34a]">
                    <div className="col-span-2">Date</div>
                    <div className="col-span-4">Description</div>
                    <div className="col-span-2">Category</div>
                    <div className="col-span-2">Account</div>
                    <div className="col-span-2 text-right">Amount</div>
                  </div>
                  
                  {/* Grid Rows */}
                  <div className="divide-y divide-gray-200">
                  {previewData.azureResponse.matchingTransactions
  .filter(
    (transaction) =>
      transaction &&
      transaction.transactionDate &&
      transaction.description &&
      transaction.transactionAmount !== undefined &&
      !isNaN(new Date(transaction.transactionDate).getTime())
  )
  .map((transaction, index) => (
                      <div
                        key={`${transaction.id || index}-${transaction.transactionDate}`}
                        className="grid grid-cols-12 gap-4 p-3 text-sm text-gray-600 hover:bg-gray-50"
                      >
                        <div className="col-span-2">
                          {new Date(transaction.transactionDate).toLocaleDateString() || 'N/A'}
                        </div>
                        <div className="col-span-4">{transaction.description || 'Unknown Transaction'}</div>
                        <div className="col-span-2">{transaction.category || 'N/A'}</div>
                        <div className="col-span-2">{transaction.account || 'N/A'}</div>
                        <div className="col-span-2 text-right">
                          ${transaction.transactionAmount?.toFixed(2) || '0.00'}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <p className="text-center text-gray-500 py-8">No matching transactions found</p>
              )}
            </div>
          )}

          {/* Add Transaction Modal */}
          {showAddTransactionModal && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-lg p-6 w-full max-w-md">
                <h3 className="text-lg font-bold mb-6 text-gray-800">Add New Transaction</h3>
                <form onSubmit={handleAddTransaction} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Date</label>
                    <input
                      type="date"
                      value={newTransaction.date}
                      onChange={(e) =>
                        dispatch(updateNewTransaction({ date: e.target.value }))
                      }
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#8bc34a] focus:border-[#8bc34a]"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <input
                      type="text"
                      value={newTransaction.description}
                      onChange={(e) =>
                        dispatch(updateNewTransaction({ description: e.target.value }))
                      }
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#8bc34a] focus:border-[#8bc34a]"
                      required
                    />
                  </div>
                  
                 <div className="grid grid-cols-5 gap-4 items-center">
  <div className="flex items-center col-span-2">
    
    <span className="font-semibold text-gray-700 text-sm">Category</span>
  </div>

  <div className="col-span-3">
    <select
      value={newTransaction.category || ''}
      onChange={(e) =>
        dispatch(updateNewTransaction({ category: e.target.value }))
      }
      className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#f57c00] focus:border-[#f57c00]"
      required
    >
      <option value="">Select a category & subcategory</option>
      {categories.map((cat) => (
        <optgroup key={cat.id} label={cat.category}>
          {subcategories
            .filter((sub) => sub.categoryId === cat.id)
            .map((sub) => (
              <option key={sub.id} value={sub.subCategory}>
                {sub.subCategory}
              </option>
            ))}
        </optgroup>
      ))}
    </select>
  </div>
</div>

                  
                  <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Account
                </label>
                <select
                  value={newTransaction.account}
                  onChange={(e) =>
                    dispatch(updateNewTransaction({ account: e.target.value }))
                  }
                  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#8bc34a] focus:border-[#8bc34a]"
                  required
                  disabled={loadingAccounts}
                >
                <option value="">Select an account</option>
  {accounts.map((account) => (
    <option key={account.accountId} value={account.accountId}>
      {`${account.accountName} (${account.accountMask})`}
    </option>
  ))}
</select>
                {loadingAccounts && <p className={`text-sm ${darkMode ? 'text-gray-300' : 'text-gray-500'}`}>Loading accounts...</p>}
              </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Amount</label>
                    <input
                      type="number"
                      step="0.01"
                      value={newTransaction.amount}
                      onChange={(e) =>
                        dispatch(updateNewTransaction({ amount: e.target.value }))
                      }
                      className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#8bc34a] focus:border-[#8bc34a]"
                      required
                    />
                  </div>
                  
                  <div className="flex justify-end space-x-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowAddTransactionModal(false)}
                      className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                      style={{
                        border: '1px solid #e2e8f0',
                        color: 'black'
                      }}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-4 py-2 bg-[#8bc34a] text-white rounded-md hover:bg-[#689f38]"
                      style={{
                        border: '1px solid #e2e8f0',
                        color: 'black'
                      }}
                    >
                      
                      Save Transaction
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </>
  
) : (
  /* Existing code for other document types remains unchanged */
previewData?.docType === 'id' ? (
  <div className="p-6">
    <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl shadow-sm p-8">
      <div className="flex items-center mb-6">
        <div className="bg-blue-100 p-3 rounded-lg mr-4">
          <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 114 0v2m-4 0a2 2 0 104 0m-4 0V4a2 2 0 014 0v2" />
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-gray-800">ID Document Details</h3>
      </div>
      
      {previewData?.azureResponse?.fullAnalysis ? (
        <div className="space-y-6">
          {/* Personal Information Section */}
          <div className="border-l-4 border-blue-500 pl-4">
            <h4 className="text-lg font-semibold text-gray-700 mb-4">Personal Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">First Name</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.FirstName || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.FirstName',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Last Name</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.LastName || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.LastName',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Nationality</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.Nationality || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.Nationality',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Sex</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.Sex || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.Sex',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
            </div>
          </div>

          {/* Dates Section */}
          <div className="border-l-4 border-green-500 pl-4">
            <h4 className="text-lg font-semibold text-gray-700 mb-4">Important Dates</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Date of Birth</label>
                <input
                  type="date"
                  value={
                    previewData.azureResponse.fullAnalysis.DateOfBirth
                      ? (() => {
                          const dateStr = previewData.azureResponse.fullAnalysis.DateOfBirth;
                          const dateObj = new Date(dateStr);
                          return dateStr && !isNaN(dateObj) ? dateObj.toISOString().split('T')[0] : '';
                        })()
                      : ''
                  }
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.DateOfBirth',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Date of Expiration</label>
                <input
                  type="date"
                  value={
                    previewData.azureResponse.fullAnalysis.DateOfExpiration
                      ? (() => {
                          const dateStr = previewData.azureResponse.fullAnalysis.DateOfExpiration;
                          const dateObj = new Date(dateStr);
                          return dateStr && !isNaN(dateObj) ? dateObj.toISOString().split('T')[0] : '';
                        })()
                      : ''
                  }
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.DateOfExpiration',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <p className="text-gray-500 text-lg">No structured ID data available</p>
        </div>
      )}
    </div>
  </div>
    ) : previewData?.docType === 'invoice' ? (
  <div className="p-6">
    <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl shadow-sm p-8">
      <div className="flex items-center mb-6">
        <div className="bg-emerald-100 p-3 rounded-lg mr-4">
          <svg className="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-gray-800">Invoice Details</h3>
      </div>
      
      {previewData?.azureResponse?.fullAnalysis ? (
        <div className="space-y-6">
          {/* Invoice Information */}
          <div className="border-l-4 border-emerald-500 pl-4">
            <h4 className="text-lg font-semibold text-gray-700 mb-4">Invoice Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Invoice Number</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.InvoiceNumber || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.InvoiceNumber',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Invoice Date</label>
                <input
                  type="date"
                  value={
                    (() => {
                      const dateStr = previewData.azureResponse.fullAnalysis.EffectiveDate;
                      const dateObj = new Date(dateStr);
                      return dateStr && !isNaN(dateObj) ? dateObj.toISOString().split('T')[0] : '';
                    })()
                  }
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.EffectiveDate',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
            </div>
          </div>

          {/* Vendor Information */}
          <div className="border-l-4 border-blue-500 pl-4">
            <h4 className="text-lg font-semibold text-gray-700 mb-4">Vendor Information</h4>
            <div className="grid grid-cols-1 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Vendor Name</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.VendorName || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.VendorName',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Vendor Address</label>
                <textarea
                  rows={3}
                  value={previewData.azureResponse.fullAnalysis.VendorAddress || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.VendorAddress',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm resize-none"
                />
              </div>
            </div>
          </div>

          {/* Financial Information */}
          <div className="border-l-4 border-purple-500 pl-4">
            <h4 className="text-lg font-semibold text-gray-700 mb-4">Financial Details</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Total Amount</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.InvoiceTotal || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.InvoiceTotal',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm font-semibold"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Tax Amount</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.Tax || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.Tax',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <p className="text-gray-500 text-lg">No structured invoice data available</p>
        </div>
      )}
    </div>
  </div>
      ) : previewData?.docType === 'tax' ? (
  <div className="p-6">
    <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl shadow-sm p-8">
      <div className="flex items-center mb-6">
        <div className="bg-orange-100 p-3 rounded-lg mr-4">
          <svg className="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-gray-800">Tax Document Details</h3>
      </div>
      
      {previewData?.azureResponse?.fullAnalysis ? (
        <div className="space-y-6">
          {/* Tax Year Information */}
          <div className="border-l-4 border-orange-500 pl-4">
            <h4 className="text-lg font-semibold text-gray-700 mb-4">Tax Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Tax Year</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.TaxYear || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.TaxYear',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm font-semibold"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Social Security Number</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.SocialSecurityNumber || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.SocialSecurityNumber',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
            </div>
          </div>

          {/* Employee Information */}
          <div className="border-l-4 border-blue-500 pl-4">
            <h4 className="text-lg font-semibold text-gray-700 mb-4">Employee Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Employee Name</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.EmployeeName || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.EmployeeName',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Employer Name</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.EmployerName || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.EmployerName',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
            </div>
          </div>

          {/* Financial Information */}
          <div className="border-l-4 border-green-500 pl-4">
            <h4 className="text-lg font-semibold text-gray-700 mb-4">Financial Details</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Total Wages</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.Wages || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.Wages',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm font-semibold"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Federal Tax Withheld</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.FederalTaxWithheld || 'N/A'}
                  onChange={(e) =>
                    dispatch(
                      updatePreviewDataField({
                        path: 'azureResponse.fullAnalysis.FederalTaxWithheld',
                        value: e.target.value,
                      })
                    )
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
            </div>
          </div>
        </div>
      ) : (
        <div className="text-center py-12">
          <div className="bg-gray-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <p className="text-gray-500 text-lg">No structured tax data available</p>
        </div>
      )}
    </div>
  </div>
     ) : previewData?.docType === 'contract' ? (
  <div className="p-6">
    <div className="bg-gradient-to-br from-white to-gray-50 border border-gray-200 rounded-xl shadow-sm p-8">
      <div className="flex items-center mb-6">
        <div className="bg-green-100 p-3 rounded-lg mr-4">
          <svg className="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 className="text-2xl font-bold text-gray-800">Contract Document Details</h3>
      </div>

      {previewData?.azureResponse?.fullAnalysis ? (
        <div className="space-y-6">

          {/* Contract Basic Information */}
          <div className="border-l-4 border-green-500 pl-4">
            <h4 className="text-lg font-semibold text-gray-700 mb-4">Contract Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Contract Title</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.ContractTitle || 'N/A'}
                  onChange={(e) =>
                    dispatch(updatePreviewDataField({
                      path: 'azureResponse.fullAnalysis.ContractTitle',
                      value: e.target.value,
                    }))
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm font-semibold"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Contract Type</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.ContractType || 'N/A'}
                  onChange={(e) =>
                    dispatch(updatePreviewDataField({
                      path: 'azureResponse.fullAnalysis.ContractType',
                      value: e.target.value,
                    }))
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
            </div>
          </div>

          {/* Parties Information */}
          <div className="border-l-4 border-blue-500 pl-4">
            <h4 className="text-lg font-semibold text-gray-700 mb-4">Parties Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Parties Involved</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.PartiesInvolved || 'N/A'}
                  onChange={(e) =>
                    dispatch(updatePreviewDataField({
                      path: 'azureResponse.fullAnalysis.PartiesInvolved',
                      value: e.target.value,
                    }))
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Effective Date</label>
                <input
                  type="date"
                  value={
                    previewData.azureResponse.fullAnalysis.EffectiveDate
                      ? (() => {
                        const dateStr = previewData.azureResponse.fullAnalysis.EffectiveDate;
                        const dateObj = new Date(dateStr);
                        return dateStr && !isNaN(dateObj) ? dateObj.toISOString().split('T')[0] : '';
                      })()
                      : ''
                  }
                  onChange={(e) =>
                    dispatch(updatePreviewDataField({
                      path: 'azureResponse.fullAnalysis.EffectiveDate',
                      value: e.target.value,
                    }))
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
            </div>
          </div>

          {/* Contract Terms */}
          <div className="border-l-4 border-purple-500 pl-4">
            <h4 className="text-lg font-semibold text-gray-700 mb-4">Contract Terms</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Duration</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.Duration || 'N/A'}
                  onChange={(e) =>
                    dispatch(updatePreviewDataField({
                      path: 'azureResponse.fullAnalysis.Duration',
                      value: e.target.value,
                    }))
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm"
                />
              </div>
              <div className="space-y-2">
                <label className="block text-sm font-semibold text-gray-700">Contract Value</label>
                <input
                  type="text"
                  value={previewData.azureResponse.fullAnalysis.ContractValue || 'N/A'}
                  onChange={(e) =>
                    dispatch(updatePreviewDataField({
                      path: 'azureResponse.fullAnalysis.ContractValue',
                      value: e.target.value,
                    }))
                  }
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200 bg-white shadow-sm font-semibold"
                />
              </div>
            </div>
          </div>

          {/* ✅ Full Contract Content */}
          {previewData?.azureResponse?.contractContent && (
            <div className="border-t border-gray-300 pt-6 mt-6">
              <h4 className="text-lg font-semibold text-gray-800 mb-4">Full Contract Content</h4>
              <div className="text-gray-700 space-y-3 max-h-[500px] overflow-y-auto p-2 bg-white border rounded-lg shadow-sm">
                {previewData.azureResponse.contractContent
                  .split('\n')
                  .filter(line => line.trim() !== '')
                  .map((line, index) => (
                    <p
                      key={index}
                      className={
                        line === line.toUpperCase() && line.length < 60
                          ? "font-semibold text-gray-900 mt-4"
                          : "text-gray-700"
                      }
                    >
                      {line}
                    </p>
                  ))}
              </div>
            </div>
          )}

        </div>
      ) : (
        <p className="text-center text-gray-500 py-8">No structured contract data available</p>
      )}
    </div>
  </div>
) : (
  <div className="p-4">
    <div className="bg-white border border-gray-200 rounded-lg p-6 text-sm text-gray-500 italic">
      No structured layout available for this document type.
    </div>
  </div>
      ))}
    </div>
    {/* Existing showRawJson section remains unchanged */}
    <div className="mt-4">
      <button
        onClick={() => setShowRawJson(!showRawJson)}
        className="text-sm text-[#8bc34a] hover:text-[#689f38] flex items-center"
      >
        {showRawJson ? 'Hide raw JSON' : 'Show raw JSON'}
        <ChevronDown className={`w-4 h-4 ml-1 transition-transform ${showRawJson ? 'rotate-180' : ''}`} />
      </button>
      {showRawJson && (
        <div className="bg-gray-50 p-4 rounded-lg shadow-sm mt-2 border border-[#8bc34a]">
          <pre className="text-xs overflow-auto max-h-64 whitespace-pre-wrap">
            {JSON.stringify(previewData, null, 2)}
          </pre>
        </div>
      )}
    </div>
  </div>
)}

                  </div>

                  <div className="flex justify-between pt-4 mt-auto border-t" style={{ borderColor: '#f1f8e9' }}>
                    <button
                      onClick={() => dispatch(setPreviewStep('upload'))}
                      className="px-4 py-2.5 border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors flex items-center"
                      style={{ color: 'black' }}
                    >
                      <ArrowLeft className="w-4 h-4 mr-1" />
                      <span>Back</span>
                    </button>
                    <button
                      onClick={() => {
                        if (!previewData) {
                          console.error('Preview data is not available');
                          return;
                        }
                        dispatch({
                          type: 'documents/confirmUpload',
                          payload: { previewData }
                        });
                      }}
                      className="px-5 py-2.5 text-white rounded-lg shadow-sm hover:shadow-md transition-all"
                      style={{ backgroundColor: "#8bc34a" }}
                      disabled={isProcessing || !previewData}  // Disable if no previewData
                    >
                      {isProcessing ? (
                        <span className="flex items-center">
                          <svg className="animate-spin h-4 w-4 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Uploading...
                        </span>
                      ) : (
                        'Confirm & Upload'
                      )}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      {/* Document Viewer Modal - Updated to show QR code if available */}
      {selectedDocument && (
        <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
<div className={`rounded-xl w-full max-w-3xl max-h-[90vh] overflow-hidden shadow-2xl flex flex-col ${darkMode ? 'bg-gray-800 text-white' : 'bg-white text-black'}`}>
  <div className={`flex justify-between items-center p-5 border-b ${darkMode ? 'bg-gray-900 border-gray-700' : 'bg-[#fcfdf9] border-[#f1f8e9]'}`}>
              <h3 className="font-semibold text-lg flex items-center">
        <div className={`p-2 rounded-lg mr-3 ${darkMode ? 'bg-gray-700' : 'bg-[#f1f8e9]'}`}>
        <FileText className="w-5 h-5" style={{ color: darkMode ? '#a5d6a7' : '#8bc34a' }} />
                </div>
                {selectedDocument.name}
              </h3>
              <div className="flex items-center">
          {/* Download Button */}
          <button
            onClick={(e) => {
              e.stopPropagation(); // Prevent event bubbling
              handleDownload(selectedDocument); // Call your download function
            }}
            className="p-2 rounded-full bg-white text-gray-700 hover:text-gray-900 hover:bg-gray-100 transition-colors shadow-sm mr-3"
            title="Download Document"
          >
            <Download className="w-5 h-5" style={{ color: darkMode ? '#a5d6a7' : '#8bc34a' }} />
          </button>
              <button
               onClick={(e) => {
                e.stopPropagation(); // Prevent event bubbling
                handleCloseDocument();
              }}
                className="absolute top-4 right-4 p-2 rounded-full bg-white text-gray-700 hover:text-gray-900 hover:bg-gray-100 transition-colors shadow-sm"
              >
                <X className="w-5 h-5" />
              </button>
              </div>
            </div>

            {/* Add the tab interface */}
      <div className={`flex border-b ${darkMode ? 'border-gray-700' : 'border-[#f1f8e9]'}`}>        <button
                onClick={() => setActiveViewerTab('original')}
          className={`px-4 py-3 text-sm font-medium border-b-2 ${activeViewerTab === 'original' ? (darkMode ? 'text-green-400 border-green-400' : 'text-green-600 border-green-500') : (darkMode ? 'text-gray-400 border-transparent' : 'text-gray-800 border-transparent')}`}
          style={{ 
            border: '1px solid #e2e8f0',
            color: 'black'
          }}
              >
                Original
              </button>
              <button
                onClick={() => setActiveViewerTab('scanned')}
          className={`px-4 py-3 text-sm font-medium border-b-2 ${
            activeViewerTab === 'scanned' 
              ? (darkMode ? 'text-green-400 border-green-400' : 'text-green-600 border-green-500') : (darkMode ? 'text-gray-400 border-transparent' : 'text-gray-800 border-transparent')}`}
          style={{ 
            border: '1px solid #e2e8f0',
            color: 'black'
          }}
              >
                Scanned Copy
              </button>
              <button
                onClick={() => setActiveViewerTab('data')}
          className={`px-4 py-3 text-sm font-medium border-b-2 ${
            activeViewerTab === 'data' 
              ? (darkMode ? 'text-green-400 border-green-400' : 'text-green-600 border-green-500') : (darkMode ? 'text-gray-400 border-transparent' : 'text-gray-800 border-transparent')}`}
          style={{ 
            border: '1px solid #e2e8f0',
            color: 'black'
          }}
              >
                Extracted Data
              </button>
              {selectedDocument.qrData && (
                <button
                  onClick={() => setActiveViewerTab('verification')}
            className={`px-4 py-3 text-sm font-medium border-b-2 ${
              activeViewerTab === 'verification' 
                ? (darkMode ? 'text-green-400 border-green-400' : 'text-green-600 border-green-500') : (darkMode ? 'text-gray-400 border-transparent' : 'text-gray-800 border-transparent')}`}
                >
                  Verification
                </button>
              )}
            </div>

            <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 100px)' }}>
              {activeViewerTab === 'original' && (
  <div className={`border rounded-xl p-10 flex items-center justify-center min-h-64 ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-[#f1f8e9]'}`}    
                style={{ borderColor: '#f1f8e9' }}>
                  {selectedDocument?.savedFilePath ? (
                    <div className="text-center">
                      <div className="relative mx-auto w-64 h-80 mb-4">
                        <img
                          src={`${API_URL}/pennypal${selectedDocument.savedFilePath.startsWith('/uploads/')
                              ? selectedDocument.savedFilePath
                              : `/uploads/${selectedDocument.savedFilePath}`
                            }`} alt="Original Document"
                          className="w-full h-full object-contain border rounded-lg"
                          style={{ borderColor: '#dcedc8' }}
                        />
                      </div>
                      <p className="text-gray-500 mb-2 font-medium">Original Document</p>

                    </div>
                  ) : (
                    <div className="text-center">
                      {/* Fallback UI when no file is available */}
                      <div className="relative mx-auto w-24 h-28 mb-4">
                        <div className="absolute top-0 left-0 w-20 h-24 border-2 rounded-lg transform rotate-[-5deg]"
                          style={{ borderColor: '#dcedc8', backgroundColor: '#f9fbe7' }}></div>
                        <div className="absolute top-0 right-0 w-20 h-24 border-2 rounded-lg transform rotate-[5deg] z-10"
                          style={{ borderColor: '#dcedc8', backgroundColor: '#f1f8e9' }}></div>
                        <FileText className="absolute top-4 left-4 w-16 h-16 text-gray-300 z-20" />
                      </div>
                      <p className="text-gray-500 mb-2 font-medium">Original Document</p>
                      <p className="text-sm text-gray-400 mb-6">No document available</p>
                    </div>
                  )}
                </div>
              )}
{activeViewerTab === 'scanned' && (
  <div className="border rounded-xl p-10 bg-gray-50 flex items-center justify-center min-h-64"
    style={{ borderColor: '#f1f8e9' }}>
    {selectedDocument?.scannedCopyPath ? (
      <div className="text-center">
        <div className="relative mx-auto w-64 h-80 mb-4">
          {/* Scanned document container with effects */}
          <div className="relative">
            {/* Paper texture and rotation */}
            <div 
              className="bg-white p-2 shadow-lg"
              style={{
                transform: 'rotate(1deg)',
                border: '1px solid #e0e0e0',
                background: 'linear-gradient(0deg, rgba(0,0,0,0.03) 0%, rgba(0,0,0,0) 2%)'
              }}
            >
              {/* Actual scanned image with filters */}
              <img
                src={`http://localhost:8080/pennypal${selectedDocument.scannedCopyPath.startsWith('/uploads/scanned-copy/')
                  ? selectedDocument.scannedCopyPath
                  : `/uploads/scanned-copy/${selectedDocument.scannedCopyPath}`
                }`} 
                alt="Scanned Copy"
                className="w-full h-full object-contain border"
                style={{ 
                  borderColor: '#e8e8e8',
                  filter: 'contrast(1.05) grayscale(0.2) brightness(1.02)',
                  backgroundColor: '#f9f9f9',
                  padding: '8px'
                }}
              />
            </div>

            {/* Scan line overlay effect */}
            <div
              className="absolute inset-0 pointer-events-none"
              style={{
                background: `
                  repeating-linear-gradient(
                    0deg,
                    rgba(0,0,0,0.03),
                    rgba(0,0,0,0.03) 1px,
                    transparent 1px,
                    transparent 4px
                  )`,
                mixBlendMode: 'multiply'
              }}
            ></div>

            {/* Scan artifact animations */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              <div
                className="absolute w-full h-0.5 bg-black opacity-5 animate-scanline-1"
                style={{ top: '20%' }}
              ></div>
              <div
                className="absolute w-full h-0.5 bg-black opacity-5 animate-scanline-2"
                style={{ top: '55%' }}
              ></div>
            </div>

            {/* Scanned document label */}
            <div className="absolute -bottom-3 -right-3 bg-white px-3 py-1 rounded text-xs border flex items-center shadow-sm"
              style={{
                borderColor: '#e0e0e0',
                transform: 'rotate(3deg)'
              }}>
              <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
              SCANNED COPY
            </div>
          </div>
        </div>
        <p className="text-gray-500 mb-2 font-medium">Scanned Copy</p>
      </div>
    ) : (
      <div className="text-center">
        {/* Fallback UI when no scanned copy is available */}
        <div className="relative mx-auto w-32 h-40 mb-4 bg-gray-100 rounded flex items-center justify-center">
          <FileText className="w-12 h-12 text-gray-300" />
          <div className="absolute inset-0" style={{
            background: `
              repeating-linear-gradient(
                0deg,
                rgba(0,0,0,0.05),
                rgba(0,0,0,0.05) 1px,
                transparent 1px,
                transparent 4px
              )`,
            mixBlendMode: 'multiply'
          }}></div>
        </div>
        <p className="text-gray-500 mb-2 font-medium">Scanned Copy</p>
        <p className="text-sm text-gray-400 mb-6">No scanned copy available</p>
      </div>
    )}
  </div>
)}
{activeViewerTab === 'data' && (
  <div className="border rounded-xl p-6 bg-gray-50" style={{ borderColor: '#f1f8e9' }}>
    {selectedDocument?.type === 'receipt' ? (
      <div className="space-y-6">
        {/* Receipt Header */}
        <div className="bg-[#9ccc65] rounded-t-xl p-4 flex items-center justify-between">
          <div className="flex items-center">
            <ShoppingBag className="w-5 h-5 mr-2 text-white" />
            <h2 className="text-xl font-bold text-white">Your Receipt</h2>
          </div>
          {/* <X className="w-5 h-5 text-white" /> */}
        </div>
      
        
        {/* Merchant Information */}
        <div className="text-center border-b pb-4" style={{ borderColor: '#e6e6e6' }}>
          <h3 className="text-xl font-bold mb-1">{selectedDocument?.merchantName || 'Grocery Mart'}</h3>
          <p className="text-gray-500">{selectedDocument?.merchantAddress || '123 Main Street, Anytown, USA'}</p>
          <p className="text-gray-500">{selectedDocument?.merchantPhno || '(555) 123-4567'}</p>
        </div>
        
        {/* Receipt Details */}
        <div className="grid grid-cols-2 gap-4">
  {/* Date */}
  <div className="flex justify-center items-center">
    <Calendar className="w-5 h-5 text-gray-500 mr-2" />
    <div >
      <p className="text-sm text-gray-500">Date</p>
      <p className="font-medium">
        {selectedDocument?.date ? new Date(selectedDocument.date).toLocaleDateString() : 'March 5, 2025'}
      </p>
    </div>
  </div>

  {/* Time */}
  <div className="flex justify-center items-center">
    <Clock className="w-5 h-5 text-gray-500 mr-2" />
    <div>
      <p className="text-sm text-gray-500">Time</p>
      <p className="font-medium">14:30</p>
    </div>
  </div>
        </div>
        
        {/* Items List */}
        <div>
          {/* Table Header */}
          <div className="grid grid-cols-12 gap-2 rounded-t-lg p-3 font-medium" style={{ backgroundColor: '#8bc34a', color: 'white' }}>
            <div className="col-span-6 text-left">Item</div>
            <div className="col-span-2 text-center">Qty</div>
            <div className="col-span-2 text-right">Price</div>
            <div className="col-span-2 text-right">Total</div>
          </div>
          
          {/* Table Body */}
          <div className="border-x border-b rounded-b-lg" style={{ borderColor: '#e6e6e6' }}>
            {selectedDocument?.items && selectedDocument.items.length > 0 ? (
              <>
                {selectedDocument.items.map((item, index) => (
                  <div 
                    key={index} 
                    className="grid grid-cols-12 gap-2 p-3 border-b" 
                    style={{ 
                      borderColor: '#e6e6e6',
                      backgroundColor: index % 2 === 0 ? 'white' : '#f9fbe7'
                    }}
                  >
                    <div className="col-span-6 text-left font-medium">{item?.item || 'N/A'}</div>
                    <div className="col-span-2 text-center">{item?.quantity || 1}</div>
                    <div className="col-span-2 text-right">${item?.price != null ? item.price.toFixed(2) : 'N/A'}</div>
                    <div className="col-span-2 text-right font-medium">
                      ${item?.price != null ? (item.price * (item?.quantity || 1)).toFixed(2) : 'N/A'}
                    </div>
                  </div>
                ))}
                
                {/* Summary */}
                <div className="p-3 space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="text-gray-600">Subtotal:</div>
                    <div className="font-medium">
                      ${selectedDocument?.transactionSubtotal != null ? selectedDocument.transactionSubtotal.toFixed(2) : '28.00'}
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="text-gray-600">Tax:</div>
                    <div className="font-medium">
                      ${selectedDocument?.transTax != null ? selectedDocument.transTax.toFixed(2) : '2.31'}
                    </div>
                  </div>
                  <div className="flex justify-between items-center border-t pt-2" style={{ borderColor: '#e6e6e6' }}>
                    <div className="font-bold">Total:</div>
                    <div className="font-bold">
                      ${selectedDocument?.transTotal != null ? selectedDocument.transTotal.toFixed(2) : '30.31'}
                    </div>
                  </div>
                </div>
              </>
            ) : (
              <div className="p-4 text-center text-gray-500">No items found</div>
            )}
          </div>
        </div>
        
      
      </div>
 ) : selectedDocument?.type === 'id' || selectedDocument?.type === 'invoice' ? (
  <div className="space-y-6">
    {/* Enhanced Header - changes based on document type */}
    <div className={`rounded-t-xl p-6 flex items-center justify-between shadow-lg ${
      selectedDocument?.type === 'id' ? 'bg-gradient-to-r from-lime-500 to-lime-600' : 'bg-gradient-to-r from-lime-500 to-lime-600'
    }`}>
      <div className="flex items-center space-x-3">
        <div className="bg-white/20 p-2 rounded-lg">
          {selectedDocument?.type === 'id' ? (
            <CreditCard className="w-6 h-6 text-white" />
          ) : (
            <FileText className="w-6 h-6 text-white" />
          )}
        </div>
        <div>
          <h2 className="text-2xl font-bold text-white">
            {selectedDocument?.type === 'id' ? 'ID Document' : 'Invoice Document'}
          </h2>
          <p className="text-white/80 text-sm">{selectedDocument?.docType}</p>
        </div>
      </div>
      <div className="bg-white/10 px-3 py-1 rounded-full">
        <span className="text-white text-sm font-medium uppercase tracking-wide">
          {selectedDocument?.type}
        </span>
      </div>
    </div>

    {/* Enhanced Document Summary Cards */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className={`p-6 rounded-xl shadow-md transition-all duration-200 hover:shadow-lg ${
        selectedDocument?.type === 'id' ? 
        'bg-gradient-to-br from-lime-50 to-lime-100 border-l-4 border-lime-500' : 
        'bg-gradient-to-br from-lime-50 to-lime-100 border-l-4 border-lime-500'
      }`}>
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg ${
            selectedDocument?.type === 'id' ? 'bg-lime-500' : 'bg-lime-500'
          }`}>
            <FileText className="w-5 h-5 text-white" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-600 mb-1">Document Name</p>
            <p className={`text-lg font-bold truncate ${
              selectedDocument?.type === 'id' ? 'text-lime-700' : 'text-lime-700'
            }`} title={selectedDocument?.name}>
              {selectedDocument?.name || 'N/A'}
            </p>
          </div>
        </div>
      </div>

      <div className={`p-6 rounded-xl shadow-md transition-all duration-200 hover:shadow-lg ${
        selectedDocument?.type === 'id' ? 
        'bg-gradient-to-br from-lime-50 to-lime-100 border-l-4 border-lime-500' : 
        'bg-gradient-to-br from-lime-50 to-lime-100 border-l-4 border-lime-500'
      }`}>
        <div className="flex items-center space-x-3">
          <div className={`p-2 rounded-lg ${
            selectedDocument?.type === 'id' ? 'bg-lime-500' : 'bg-lime-500'
          }`}>
            <FileText className="w-5 h-5 text-white" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-600 mb-1">Document Type</p>
            <p className={`text-lg font-bold truncate ${
              selectedDocument?.type === 'id' ? 'text-lime-700' : 'text-lime-700'
            }`}>
              {selectedDocument?.docType || selectedDocument?.type || 'Document'}
            </p>
          </div>
        </div>
      </div>
    </div>

    {/* Enhanced Extracted Fields Section */}
    {selectedDocument?.extractedFields && Object.keys(selectedDocument.extractedFields).length > 0 ? (
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        <div className={`px-6 py-4 ${
          selectedDocument?.type === 'id' ? 
          'bg-gradient-to-r from-lime-50 to-lime-100 border-b border-lime-200' : 
          'bg-gradient-to-r from-lime-50 to-lime-100 border-b border-lime-200'
        }`}>
          <h3 className={`font-bold text-xl flex items-center ${
            selectedDocument?.type === 'id' ? 'text-lime-800' : 'text-lime-800'
          }`}>
            <FileText className="w-5 h-5 mr-2" />
            {selectedDocument?.type === 'id' ? 'Personal Information' : 'Invoice Details'}
          </h3>
        </div>
        
        <div className="p-6">
          {selectedDocument?.type === 'id' ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* ID Document Fields with enhanced styling */}
              {selectedDocument.extractedFields.firstName && (
                <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="bg-lime-100 p-2 rounded-lg">
                      <User className="w-4 h-4 text-lime-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-500 mb-1">First Name</p>
                      <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.firstName}</p>
                    </div>
                  </div>
                </div>
              )}

              {selectedDocument.extractedFields.lastName && (
                <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="bg-lime-100 p-2 rounded-lg">
                      <User className="w-4 h-4 text-lime-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-500 mb-1">Last Name</p>
                      <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.lastName}</p>
                    </div>
                  </div>
                </div>
              )}

              {selectedDocument.extractedFields.dateOfBirth && (
                <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="bg-lime-100 p-2 rounded-lg">
                      <Calendar className="w-4 h-4 text-lime-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-500 mb-1">Date of Birth</p>
                      <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.dateOfBirth}</p>
                    </div>
                  </div>
                </div>
              )}

              {selectedDocument.extractedFields.sex && (
                <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="bg-lime-100 p-2 rounded-lg">
                      <User className="w-4 h-4 text-lime-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-500 mb-1">Gender</p>
                      <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.sex}</p>
                    </div>
                  </div>
                </div>
              )}

              {selectedDocument.extractedFields.documentNumber && (
                <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="bg-lime-100 p-2 rounded-lg">
                      <Hash className="w-4 h-4 text-lime-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-500 mb-1">Document Number</p>
                      <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.documentNumber}</p>
                    </div>
                  </div>
                </div>
              )}

              {selectedDocument.extractedFields.expiryDate && (
                <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="bg-lime-100 p-2 rounded-lg">
                      <CalendarClock className="w-4 h-4 text-lime-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-500 mb-1">Expiry Date</p>
                      <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.expiryDate}</p>
                    </div>
                  </div>
                </div>
              )}

              {selectedDocument.extractedFields.country && (
                <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="bg-lime-100 p-2 rounded-lg">
                      <Flag className="w-4 h-4 text-lime-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-500 mb-1">Country</p>
                      <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.country}</p>
                    </div>
                  </div>
                </div>
              )}

              {selectedDocument.extractedFields.placeOfBirth && (
                <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="bg-lime-100 p-2 rounded-lg">
                      <MapPin className="w-4 h-4 text-lime-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-500 mb-1">Place of Birth</p>
                      <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.placeOfBirth}</p>
                    </div>
                  </div>
                </div>
              )}

              {selectedDocument.extractedFields.documentType && (
                <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <div className="flex items-center space-x-3">
                    <div className="bg-lime-100 p-2 rounded-lg">
                      <FileText className="w-4 h-4 text-lime-600" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-500 mb-1">Document Type</p>
                      <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.documentType}</p>
                    </div>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Enhanced Invoice Fields */}
                {selectedDocument.extractedFields.invoiceNumber && (
                  <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="bg-lime-100 p-2 rounded-lg">
                        <Hash className="w-4 h-4 text-lime-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-500 mb-1">Invoice Number</p>
                        <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.invoiceNumber}</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {selectedDocument.extractedFields.invoiceDate && (
                  <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="bg-lime-100 p-2 rounded-lg">
                        <Calendar className="w-4 h-4 text-lime-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-500 mb-1">Invoice Date</p>
                        <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.invoiceDate}</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {selectedDocument.extractedFields.dueDate && (
                  <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="bg-lime-100 p-2 rounded-lg">
                        <CalendarClock className="w-4 h-4 text-lime-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-500 mb-1">Due Date</p>
                        <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.dueDate}</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {selectedDocument.extractedFields.vendorName && (
                  <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="bg-lime-100 p-2 rounded-lg">
                        <Building className="w-4 h-4 text-lime-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-500 mb-1">Vendor Name</p>
                        <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.vendorName}</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {selectedDocument.extractedFields.vendorAddress && (
                  <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="bg-lime-100 p-2 rounded-lg">
                        <MapPin className="w-4 h-4 text-lime-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-500 mb-1">Vendor Address</p>
                        <p className="font-semibold text-gray-900 break-words">{selectedDocument.extractedFields.vendorAddress}</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {selectedDocument.extractedFields.customerName && (
                  <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="bg-lime-100 p-2 rounded-lg">
                        <User className="w-4 h-4 text-lime-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-500 mb-1">Customer Name</p>
                        <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.customerName}</p>
                      </div>
                    </div>
                  </div>
                )}
                
                {selectedDocument.extractedFields.total && (
                  <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                    <div className="flex items-center space-x-3">
                      <div className="bg-lime-100 p-2 rounded-lg">
                        <DollarSign className="w-4 h-4 text-lime-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-500 mb-1">Total Amount</p>
                        <p className="font-bold text-lg text-lime-600">{selectedDocument.extractedFields.total}</p>
                      </div>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Enhanced Line Items Table */}
              {selectedDocument.extractedFields.items && selectedDocument.extractedFields.items.length > 0 && (
                <div className="bg-white rounded-xl border border-lime-200 shadow-sm overflow-hidden">
                  <div className="bg-gradient-to-r from-lime-50 to-lime-100 px-6 py-4 border-b border-lime-200">
                    <h4 className="font-bold text-lg text-lime-800 flex items-center">
                      <FileText className="w-5 h-5 mr-2" />
                      Line Items
                    </h4>
                  </div>
                  <div className="overflow-x-auto">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Description</th>
                          <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Quantity</th>
                          <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Unit Price</th>
                          <th className="px-6 py-4 text-left text-xs font-bold text-gray-600 uppercase tracking-wider">Amount</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {selectedDocument.extractedFields.items.map((item, index) => (
                          <tr key={index} className="hover:bg-lime-50 transition-colors duration-150">
                            <td className="px-6 py-4 text-sm text-gray-900 max-w-xs">
                              <div className="font-medium">{item.description || 'N/A'}</div>
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                              {item.quantity || 'N/A'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-semibold text-gray-900">
                              {item.unitPrice || 'N/A'}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm font-bold text-lime-600">
                              {item.amount || 'N/A'}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    ) : (
      <div className="p-8 text-center bg-gray-50 rounded-xl border-2 border-dashed border-gray-300">
        <FileX className="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Data Available</h3>
        <p className="text-gray-500">No extracted fields available for this document.</p>
      </div>
    )}
  </div>
  ) : selectedDocument?.type === 'tax' ? (
  <div className="space-y-6">
    {/* Enhanced Header */}
    <div className="bg-gradient-to-r from-lime-500 to-lime-600 rounded-t-xl p-6 flex items-center justify-between shadow-lg">
      <div className="flex items-center space-x-3">
        <div className="bg-white/20 p-2 rounded-lg">
          <FileText className="w-6 h-6 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-white">Tax Document</h2>
          <p className="text-white/80 text-sm">{selectedDocument?.docType}</p>
        </div>
      </div>
      <div className="bg-white/10 px-3 py-1 rounded-full">
        <span className="text-white text-sm font-medium uppercase tracking-wide">
          {selectedDocument?.type}
        </span>
      </div>
    </div>

    {/* Enhanced Document Summary Cards */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="p-6 rounded-xl shadow-md transition-all duration-200 hover:shadow-lg bg-gradient-to-br from-lime-50 to-lime-100 border-l-4 border-lime-500">
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-lg bg-lime-500">
            <FileText className="w-5 h-5 text-white" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-600 mb-1">Document Name</p>
            <p className="text-lg font-bold truncate text-lime-700" title={selectedDocument?.name}>
              {selectedDocument?.name || 'N/A'}
            </p>
          </div>
        </div>
      </div>

      <div className="p-6 rounded-xl shadow-md transition-all duration-200 hover:shadow-lg bg-gradient-to-br from-lime-50 to-lime-100 border-l-4 border-lime-500">
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-lg bg-lime-500">
            <FileText className="w-5 h-5 text-white" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-600 mb-1">Document Type</p>
            <p className="text-lg font-bold truncate text-lime-700">
              {selectedDocument?.docType || 'Tax Document'}
            </p>
          </div>
        </div>
      </div>
    </div>

    {/* Enhanced Extracted Fields Section */}
    {selectedDocument?.extractedFields && Object.keys(selectedDocument.extractedFields).length > 0 ? (
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 bg-gradient-to-r from-lime-50 to-lime-100 border-b border-lime-200">
          <h3 className="font-bold text-xl flex items-center text-lime-800">
            <FileText className="w-5 h-5 mr-2" />
            Tax Information
          </h3>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {selectedDocument.extractedFields.taxYear && (
              <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-3">
                  <div className="bg-lime-100 p-2 rounded-lg">
                    <Calendar className="w-4 h-4 text-lime-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-500 mb-1">Tax Year</p>
                    <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.taxYear}</p>
                  </div>
                </div>
              </div>
            )}

            {selectedDocument.extractedFields.employeeName && (
              <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-3">
                  <div className="bg-lime-100 p-2 rounded-lg">
                    <User className="w-4 h-4 text-lime-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-500 mb-1">Employee Name</p>
                    <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.employeeName}</p>
                  </div>
                </div>
              </div>
            )}

            {selectedDocument.extractedFields.employerName && (
              <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-3">
                  <div className="bg-lime-100 p-2 rounded-lg">
                    <Building className="w-4 h-4 text-lime-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-500 mb-1">Employer Name</p>
                    <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.employerName}</p>
                  </div>
                </div>
              </div>
            )}

            {selectedDocument.extractedFields.wagesTips && (
              <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-3">
                  <div className="bg-lime-100 p-2 rounded-lg">
                    <DollarSign className="w-4 h-4 text-lime-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-500 mb-1">Wages & Tips</p>
                    <p className="font-bold text-lg text-lime-600">${parseFloat(selectedDocument.extractedFields.wagesTips).toFixed(2)}</p>
                  </div>
                </div>
              </div>
            )}

            {selectedDocument.extractedFields.federalIncomeTax && (
              <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-3">
                  <div className="bg-lime-100 p-2 rounded-lg">
                    <DollarSign className="w-4 h-4 text-lime-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-500 mb-1">Federal Income Tax</p>
                    <p className="font-bold text-lg text-lime-600">${parseFloat(selectedDocument.extractedFields.federalIncomeTax).toFixed(2)}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    ) : (
      <div className="p-8 text-center bg-gray-50 rounded-xl border-2 border-dashed border-gray-300">
        <FileX className="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Data Available</h3>
        <p className="text-gray-500">No extracted fields available for this document.</p>
      </div>
    )}
  </div>
  ) : selectedDocument?.type === 'contract' ? (
   <div className="space-y-6">
    {/* Enhanced Header */}
    <div className="bg-gradient-to-r from-lime-500 to-lime-600 rounded-t-xl p-6 flex items-center justify-between shadow-lg">
      <div className="flex items-center space-x-3">
        <div className="bg-white/20 p-2 rounded-lg">
          <FileText className="w-6 h-6 text-white" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-white">Contract Document</h2>
          <p className="text-white/80 text-sm">{selectedDocument?.docType}</p>
        </div>
      </div>
      <div className="bg-white/10 px-3 py-1 rounded-full">
        <span className="text-white text-sm font-medium uppercase tracking-wide">
          {selectedDocument?.type}
        </span>
      </div>
    </div>

    {/* Enhanced Document Summary Cards */}
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="p-6 rounded-xl shadow-md transition-all duration-200 hover:shadow-lg bg-gradient-to-br from-lime-50 to-lime-100 border-l-4 border-lime-500">
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-lg bg-lime-500">
            <FileText className="w-5 h-5 text-white" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-600 mb-1">Document Name</p>
            <p className="text-lg font-bold truncate text-lime-700" title={selectedDocument?.name}>
              {selectedDocument?.name || 'N/A'}
            </p>
          </div>
        </div>
      </div>

      <div className="p-6 rounded-xl shadow-md transition-all duration-200 hover:shadow-lg bg-gradient-to-br from-lime-50 to-lime-100 border-l-4 border-lime-500">
        <div className="flex items-center space-x-3">
          <div className="p-2 rounded-lg bg-lime-500">
            <FileText className="w-5 h-5 text-white" />
          </div>
          <div className="flex-1 min-w-0">
            <p className="text-sm font-medium text-gray-600 mb-1">Document Type</p>
            <p className="text-lg font-bold truncate text-lime-700">
              {selectedDocument?.docType || 'Contract Document'}
            </p>
          </div>
        </div>
      </div>
    </div>

    {/* Enhanced Extracted Fields Section */}
    {selectedDocument?.extractedFields && Object.keys(selectedDocument.extractedFields).length > 0 ? (
      <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 bg-gradient-to-r from-lime-50 to-lime-100 border-b border-lime-200">
          <h3 className="font-bold text-xl flex items-center text-lime-800">
            <FileText className="w-5 h-5 mr-2" />
            Contract Information
          </h3>
        </div>
        
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {selectedDocument.extractedFields.contractTitle && (
              <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-3">
                  <div className="bg-lime-100 p-2 rounded-lg">
                    <FileText className="w-4 h-4 text-lime-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-500 mb-1">Contract Title</p>
                    <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.contractTitle}</p>
                  </div>
                </div>
              </div>
            )}

            {selectedDocument.extractedFields.partiesInvolved && (
              <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-3">
                  <div className="bg-lime-100 p-2 rounded-lg">
                    <Users className="w-4 h-4 text-lime-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-500 mb-1">Parties Involved</p>
                    <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.partiesInvolved}</p>
                  </div>
                </div>
              </div>
            )}

            {selectedDocument.extractedFields.effectiveDate && (
              <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-3">
                  <div className="bg-lime-100 p-2 rounded-lg">
                    <Calendar className="w-4 h-4 text-lime-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-500 mb-1">Effective Date</p>
                    <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.effectiveDate}</p>
                  </div>
                </div>
              </div>
            )}

            {selectedDocument.extractedFields.contractType && (
              <div className="bg-white p-4 rounded-xl border border-lime-200 shadow-sm hover:shadow-md transition-all duration-200">
                <div className="flex items-center space-x-3">
                  <div className="bg-lime-100 p-2 rounded-lg">
                    <FileText className="w-4 h-4 text-lime-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-500 mb-1">Contract Type</p>
                    <p className="font-semibold text-gray-900 truncate">{selectedDocument.extractedFields.contractType}</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    ) : (
        <div className="p-8 text-center bg-gray-50 rounded-xl border-2 border-dashed border-gray-300">
        <FileX className="w-16 h-16 mx-auto mb-4 text-gray-400" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">No Data Available</h3>
        <p className="text-gray-500">No extracted fields available for this document.</p>
      </div>
    )}

    {/* Raw JSON */}
    {/* <div className="border rounded-lg" style={{ borderColor: '#e3f2fd' }}>
      <details className="group">
        <summary className="flex justify-between items-center p-3 cursor-pointer list-none bg-gray-50 rounded-lg">
          <div className="flex items-center">
            <Code className="w-5 h-5 mr-2 text-gray-500" />
            <span className="font-medium text-gray-700">Show Raw JSON</span>
          </div>
          <ChevronDown className="w-5 h-5 text-gray-500 group-open:rotate-180 transition-transform" />
        </summary>
        <div className="p-4 bg-white border-t" style={{ borderColor: '#e3f2fd' }}>
          {selectedDocument?.rawAnalysis ? (
            <pre className="p-4 rounded-xl border bg-gray-50 overflow-auto" style={{
              borderColor: '#e3f2fd',
              maxHeight: '300px',
              fontFamily: 'monospace',
              fontSize: '0.875rem'
            }}>
              {JSON.stringify(selectedDocument.rawAnalysis, null, 2)}
            </pre>
          ) : (
            <div className="p-4 text-center text-gray-500">
              {selectedDocument?.analysisError || 'No analysis data available'}
            </div>
          )}
        </div>
      </details>
    </div> */}

    {/* Close Button */}
    
  </div>
) : (
  <div className="text-center p-4">
    <p className="text-gray-500">Unsupported document type</p>
  </div>
)}
        </div>
      )}
              {activeViewerTab === 'verification' && selectedDocument.qrData && (
                <div className="border rounded-xl p-10 bg-gray-50 flex items-center justify-center min-h-64"
                  style={{ borderColor: '#f1f8e9' }}>
                  <div className="text-center">
                    <div className="p-4 bg-white rounded border inline-block" style={{ borderColor: '#e8f5e9' }}>
                      <QRCodeSVG
                        value={selectedDocument.qrData}
                        size={160}
                        level="H"
                        fgColor="#558b2f"
                        bgColor="#ffffff"
                      />
                    </div>
                    <p className="mt-4 text-gray-500 mb-2 font-medium">Document ID: {selectedDocument.qrData}</p>
                    <p className="text-sm text-gray-400 mb-6">Scan QR code to verify this ID document</p>
                    <button className="px-5 py-2.5 text-white rounded-lg flex items-center mx-auto shadow-sm hover:shadow-md transition-all duration-200"
                      style={{ backgroundColor: "#8bc34a" }}>
                      <Download className="w-4 h-4 mr-2" />
                      Download Verification
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentManagement;
