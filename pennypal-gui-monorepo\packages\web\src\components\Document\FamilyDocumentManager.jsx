import React, { useState, useRef ,useEffect} from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { Plus, Upload, Calendar, User, FileText, Receipt, CreditCard, FolderOpen, X, ChevronLeft } from 'lucide-react';
import {
  uploadDocumentRequest,
  confirmUpload,
  setPreviewStep,
} from '../../../../logic/redux/documentSlice';
import {
  setSelectedTransaction,
  setOpenModal,
  toggleAddTransactionModal,
  // setIsMatchingTransactionAdd
} from '../../../../logic/redux/transactionSlice';
import {setIsMatchingTransactionAdd} from '../../../../logic/redux/receiptSlice';
const FamilyDocumentManager = () => {
  const [currentView, setCurrentView] = useState('dashboard');
  const [activeTab, setActiveTab] = useState('receipt');
  const isProcessing = useSelector((state) => state.documents.isProcessing);

  const [selectedMember, setSelectedMember] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [familyMembers, setFamilyMembers] = useState([
    {
      id: 1,
      firstName: 'Smita',
      lastName: 'Pattnaik',
      initials: 'SP',
      dob: '1985-03-15',
      role: 'Owner',
      itemCount: 8,
      bgColor: 'bg-purple-200',
      textColor: 'text-purple-800',
    },
    {
      id: 2,
      firstName: 'Srinivas',
      lastName: 'Aathiyan',
      initials: 'SA',
      dob: '1982-07-22',
      role: 'Family Member',
      itemCount: 7,
      bgColor: 'bg-orange-200',
      textColor: 'text-orange-800',
    },
  ]);
  const [newMember, setNewMember] = useState({
    firstName: '',
    lastName: '',
    dob: '',
  });
  const [idDocumentFiles, setIdDocumentFiles] = useState({ front: null, back: null });

  const documentCategories = [
    { id: 'receipts', name: 'Receipts', icon: Receipt, count: 0, bgColor: 'bg-blue-50', iconColor: 'text-blue-600' },
    { id: 'id-documents', name: 'ID Documents', icon: User, count: 2, bgColor: 'bg-green-50', iconColor: 'text-green-600' },
    { id: 'invoices', name: 'Invoices', icon: FileText, count: 1, bgColor: 'bg-purple-50', iconColor: 'text-purple-600' },
    { id: 'tax', name: 'Tax Documents', icon: FolderOpen, count: 3, bgColor: 'bg-yellow-50', iconColor: 'text-yellow-600' },
    { id: 'contracts', name: 'Contracts', icon: CreditCard, count: 1, bgColor: 'bg-red-50', iconColor: 'text-red-600' },
  ];

  const dispatch = useDispatch();
  // const { openModal, isAddTransactionOpen } = useSelector(state => state.transactions);
const openModal = useSelector((state) => state.transactions.openModal);
const isAddTransactionOpen = useSelector((state) => state.transactions.isAddTransactionOpen);

useEffect(() => {
  console.log('Modal states:', { openModal, isAddTransactionOpen });
}, [openModal, isAddTransactionOpen]);
const handleAddTransaction = () => {
  const receiptData = previewData?.azureResponse || {};
  
  const transactionData = {
    transaction_id: `temp-${Date.now()}`,
    date: new Date().toISOString().split('T')[0],
    name: receiptData.MerchantName || 'New Transaction',
    amount: receiptData.Total || '',
    tax: receiptData.Tax || '',
    notes: receiptData.MerchantName ? `Receipt from ${receiptData.MerchantName}` : '',
    subcategory: '',
    bank: '',
    tag: '',
    hideFromBudget: false,
    hidden: false,
    transactionDate: new Date().toISOString(),
    description: receiptData.MerchantName || 'New Transaction'
  };

  // Dispatch all required actions
  dispatch(setSelectedTransaction(transactionData));
  dispatch(setOpenModal(true));
  dispatch(toggleAddTransactionModal(true));
  
  console.log('Dispatched all modal open actions'); // Debug log
};
  const fileInputRef = useRef(null);
  const backFileInputRef = useRef(null);
  const { previewData, previewStep, uploading, error } = useSelector(
    (state) => state.documents
  );

  const generateInitials = (firstName, lastName) => {
    return `${firstName.charAt(0).toUpperCase()}${lastName.charAt(0).toUpperCase()}`;
  };

  const generateColors = (index) => {
    const colors = [
      { bg: 'bg-blue-200', text: 'text-blue-800' },
      { bg: 'bg-green-200', text: 'text-green-800' },
      { bg: 'bg-purple-200', text: 'text-purple-800' },
      { bg: 'bg-orange-200', text: 'text-orange-800' },
      { bg: 'bg-pink-200', text: 'text-pink-800' },
      { bg: 'bg-indigo-200', text: 'text-indigo-800' },
    ];
    return colors[index % colors.length];
  };

  const handleAddMember = () => {
    if (newMember.firstName && newMember.lastName && newMember.dob) {
      const colors = generateColors(familyMembers.length);
      const member = {
        id: Date.now(),
        ...newMember,
        initials: generateInitials(newMember.firstName, newMember.lastName),
        role: 'Family Member',
        itemCount: 0,
        bgColor: colors.bg,
        textColor: colors.text,
      };
      setFamilyMembers([...familyMembers, member]);
      setNewMember({ firstName: '', lastName: '', dob: '' });
      setShowAddModal(false);
    }
  };

  const calculateAge = (dob) => {
    const today = new Date();
    const birthDate = new Date(dob);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getDaysUntilBirthday = (dob) => {
    const today = new Date();
    const birthDate = new Date(dob);
    const thisYear = today.getFullYear();

    let nextBirthday = new Date(thisYear, birthDate.getMonth(), birthDate.getDate());
    if (nextBirthday < today) {
      nextBirthday.setFullYear(thisYear + 1);
    }

    const diffTime = nextBirthday - today;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const handleFileChange = (e, side = 'front') => {
    const file = e.target.files[0];
    if (file) {
      if (selectedCategory?.id === 'id-documents') {
        setIdDocumentFiles((prev) => ({ ...prev, [side]: file }));
        
        const formData = new FormData();
        formData.append('file', file);
        formData.append('docName', `${selectedMember?.firstName} - ${selectedCategory?.name} - ${side}`);
        formData.append('docType', 'id');
        formData.append('category', selectedCategory?.name);
        formData.append('size', file.size.toString());
        formData.append('qrData', '');
        formData.append('side', side);

        dispatch(uploadDocumentRequest(formData));
      } else {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('docName', `${selectedMember?.firstName} - ${selectedCategory?.name}`);
        const docTypeMap = {
          receipts: 'receipt',
          'id-documents': 'id',
          invoices: 'invoice',
          tax: 'tax',
          contracts: 'contract',
        };
        const docType = docTypeMap[selectedCategory?.id] || selectedCategory?.id;
        formData.append('docType', docType);
        formData.append('category', selectedCategory?.name);
        formData.append('size', file.size.toString());
        formData.append('qrData', '');

        dispatch(uploadDocumentRequest(formData));
      }
    }
  };

  const handleSaveDocument = () => {
    if (previewData) {
      dispatch(confirmUpload({ previewData }));
      setIdDocumentFiles({ front: null, back: null });
      dispatch(setPreviewStep('upload'));
    }
  };

  const renderExtractedData = (azureResponse) => {
    console.log('Azure Response in renderExtractedData:', azureResponse);
      let extractedData = azureResponse;
  if (azureResponse && azureResponse.prettyJson) {
    try {
      extractedData = JSON.parse(azureResponse.prettyJson);
    } catch (e) {
      console.error('Failed to parse prettyJson:', e);
      // Fall back to the original response if parsing fails
      extractedData = azureResponse;
    }
  }

  // If the response is still a string, try to parse it
  if (typeof extractedData === 'string') {
    try {
      extractedData = JSON.parse(extractedData);
    } catch (e) {
      console.error('Failed to parse response:', e);
      return (
        <div className="text-sm text-red-500">
          Error parsing document data. Please check the document format.
        </div>
      );
    }
  }

  if (!extractedData) {
    return (
      <div className="text-sm text-red-500">
        No document data extracted. Please check if the document was processed correctly.
      </div>
    );
  }
    switch (selectedCategory?.id) {
      case 'receipts':
      
        return (
          <div className="receipt-container bg-lime-50 p-4 rounded-lg border border-lime-200">
            <div className="flex border-b border-lime-200 mb-4">
              <button 
                className={`px-4 py-2 font-medium ${activeTab === 'receipt' ? 'text-lime-700 border-b-2 border-lime-600' : 'text-lime-600 hover:text-lime-700'}`}
                onClick={() => setActiveTab('receipt')}
              >
                Current Receipt
              </button>
                <button 
                  className={`px-4 py-2 font-medium ${activeTab === 'history' ? 'text-lime-700 border-b-2 border-lime-600' : 'text-lime-600 hover:text-lime-700'}`}
                  onClick={() => setActiveTab('history')}
                >
                  Transaction History
                </button>
              
            </div>

            <div className={`receipt-content ${activeTab !== 'receipt' ? 'hidden' : ''}`}>
              <div className="receipt-header mb-4 text-center">
                <h3 className="text-lg font-bold text-lime-800">{azureResponse.MerchantName || 'Unknown Merchant'}</h3>
                <p className="text-sm text-lime-600">{azureResponse.MerchantAddress || 'N/A'}</p>
                <p className="text-sm text-lime-600">{azureResponse.MerchantPhoneNumber || 'N/A'}</p>
              </div>

              <div className="receipt-details grid grid-cols-2 gap-2 mb-4 text-sm">
                <div>
                  <span className="text-xs text-lime-500">Date</span>
                  <p className="font-medium">{azureResponse.TransactionDate || 'N/A'}</p>
                </div>
                <div>
                  <span className="text-xs text-lime-500">Time</span>
                  <p className="font-medium">{azureResponse.TransactionTime || 'N/A'}</p>
                </div>
              </div>

              <div className="receipt-items mb-6">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="border-b border-lime-200">
                      <th className="text-left pb-2 font-medium text-lime-700">Item</th>
                      <th className="text-right pb-2 font-medium text-lime-700">Qty</th>
                      <th className="text-right pb-2 font-medium text-lime-700">Price</th>
                      <th className="text-right pb-2 font-medium text-lime-700">Total</th>
                    </tr>
                  </thead>
                  <tbody>
                    {azureResponse.Items && azureResponse.Items.length > 0 ? (
                      azureResponse.Items.map((item, index) => (
                        <tr key={index} className="border-b border-lime-100">
                          <td className="py-2">{item.Name || 'Unknown Item'}</td>
                          <td className="text-right py-2">{item.Quantity || 1}</td>
                          <td className="text-right py-2">{item.Price ? `$${item.Price}` : 'N/A'}</td>
                          <td className="text-right py-2">{item.TotalPrice ? `$${item.TotalPrice}` : 'N/A'}</td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan="4" className="py-2 text-center text-lime-500">
                          No items found
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              <div className="receipt-totals text-sm">
                <div className="flex justify-between py-1">
                  <span>Subtotal:</span>
                  <span>{azureResponse.Subtotal ? `$${azureResponse.Subtotal}` : 'N/A'}</span>
                </div>
                <div className="flex justify-between py-1">
                  <span>Tax:</span>
                  <span>{azureResponse.Tax ? `$${azureResponse.Tax}` : 'N/A'}</span>
                </div>
                <div className="flex justify-between py-2 font-bold border-t border-lime-200 mt-2 text-lime-800">
                  <span>Total:</span>
                  <span>{azureResponse.Total ? `$${azureResponse.Total}` : 'N/A'}</span>
                </div>
              </div>
            </div>

             <div className={`transaction-history ${activeTab !== 'history' ? 'hidden' : ''}`}>
        {azureResponse.matchingTransactions && azureResponse.matchingTransactions !== 'No matching transactions found' ? (
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-lime-200">
                <th className="text-left pb-2 font-medium text-lime-700">Date</th>
                <th className="text-left pb-2 font-medium text-lime-700">Description</th>
                <th className="text-left pb-2 font-medium text-lime-700">Category</th>
                <th className="text-left pb-2 font-medium text-lime-700">Account</th>
                <th className="text-right pb-2 font-medium text-lime-700">Amount</th>
              </tr>
            </thead>
            <tbody>
              {Array.isArray(azureResponse.matchingTransactions) ? (
                azureResponse.matchingTransactions.map((transaction, index) => (
                  <tr key={index} className="border-b border-lime-100 hover:bg-lime-100">
                    <td className="py-2">
                      {new Date(transaction.transactionDate).toLocaleDateString('en-US', {
                        month: 'numeric',
                        day: 'numeric',
                        year: 'numeric'
                      })}
                    </td>
                    <td className="py-2">{transaction.description || 'N/A'}</td>
                    <td className="py-2">{transaction.category || 'N/A'}</td>
                    <td className="py-2">{transaction.transactionId || 'N/A'}</td>
                    <td className="text-right py-2">
                      {transaction.transactionAmount ? `$${transaction.transactionAmount.toFixed(2)}` : 'N/A'}
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan="5" className="py-2 text-center text-lime-500">
                    No matching transactions found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">No matching transactions found</p>
            <button
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
    onClick={(e) => {
    e.preventDefault(); // Prevent default form behavior
    e.stopPropagation(); // Stop event bubbling
    handleAddTransaction();
  }}
>
              Add Transaction
            </button>
          </div>
        )}
      </div>
    </div>
  );
        case 'invoices':
  return (
    <div className="invoice-container bg-green-50 p-6 rounded-lg border border-green-200">
      <div className="flex justify-between items-start mb-6 pb-4 border-b border-green-200">
        <div>
          <h3 className="text-xl font-bold text-green-800">Invoice Details</h3>
          <p className="text-sm text-green-600">Document Type: Invoice</p>
        </div>
        <div className="text-right">
          <p className="text-lg font-bold text-green-800">
            {extractedData.InvoiceNumber ? `INV-${extractedData.InvoiceNumber}` : 'N/A'}
          </p>
          <p className="text-sm text-green-600">
            {extractedData.InvoiceDate 
              ? new Date(extractedData.InvoiceDate).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })
              : 'No date specified'}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <h4 className="font-semibold text-green-700 mb-3 border-b border-green-100 pb-2">
            Vendor Information
          </h4>
          <div className="space-y-2">
            <div>
              <span className="text-xs text-green-500">Vendor Name</span>
              <p className="font-medium">
                {extractedData.VendorName?.replace(/\\n/g, ' ') || 'N/A'}
              </p>
            </div>
            <div>
              <span className="text-xs text-green-500">Address</span>
              <p className="font-medium">
                {extractedData.VendorAddress && extractedData.VendorAddress !== 'N/A'
                  ? extractedData.VendorAddress.replace(/\\n/g, ', ')
                  : 'N/A'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm">
          <h4 className="font-semibold text-green-700 mb-3 border-b border-green-100 pb-2">
            Payment Summary
          </h4>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span>Subtotal:</span>
              <span>
                {extractedData.Subtotal 
                  ? `$${parseFloat(extractedData.Subtotal).toFixed(2)}`
                  : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Tax:</span>
              <span>
                {extractedData.Tax && extractedData.Tax !== 'N/A'
                  ? `$${parseFloat(extractedData.Tax).toFixed(2)}`
                  : 'N/A'}
              </span>
            </div>
            <div className="flex justify-between font-bold pt-2 border-t border-green-100">
              <span>Total Amount:</span>
              <span className="text-green-800">
                {extractedData.InvoiceTotal 
                  ? `$${parseFloat(extractedData.InvoiceTotal).toFixed(2)}`
                  : 'N/A'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {extractedData.LineItems && (
        <div className="bg-white p-4 rounded-lg shadow-sm mb-6">
          <h4 className="font-semibold text-green-700 mb-3 border-b border-green-100 pb-2">
            Line Items
          </h4>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b border-green-200">
                  <th className="text-left pb-2 font-medium text-green-700">Description</th>
                  <th className="text-right pb-2 font-medium text-green-700">Quantity</th>
                  <th className="text-right pb-2 font-medium text-green-700">Unit Price</th>
                  <th className="text-right pb-2 font-medium text-green-700">Amount</th>
                </tr>
              </thead>
              <tbody>
                {Array.isArray(extractedData.LineItems) ? (
                  extractedData.LineItems.map((item, index) => (
                    <tr key={index} className="border-b border-green-100 hover:bg-green-50">
                      <td className="py-2">{item.Description || 'N/A'}</td>
                      <td className="text-right py-2">{item.Quantity || '1'}</td>
                      <td className="text-right py-2">
                        {item.UnitPrice ? `$${parseFloat(item.UnitPrice).toFixed(2)}` : 'N/A'}
                      </td>
                      <td className="text-right py-2">
                        {item.Amount ? `$${parseFloat(item.Amount).toFixed(2)}` : 'N/A'}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan="4" className="py-2 text-center text-green-500">
                      No line items available
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      <div className="bg-white p-4 rounded-lg shadow-sm">
        <h4 className="font-semibold text-green-700 mb-3 border-b border-green-100 pb-2">
          Additional Information
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <span className="text-xs text-green-500">Payment Terms</span>
            <p className="font-medium">
              {extractedData.PaymentTerms || 'N/A'}
            </p>
          </div>
          <div>
            <span className="text-xs text-green-500">Due Date</span>
            <p className="font-medium">
              {extractedData.DueDate 
                ? new Date(extractedData.DueDate).toLocaleDateString()
                : 'N/A'}
            </p>
          </div>
          <div>
            <span className="text-xs text-green-500">Payment Method</span>
            <p className="font-medium">
              {extractedData.PaymentMethod || 'N/A'}
            </p>
          </div>
          <div>
            <span className="text-xs text-green-500">Reference</span>
            <p className="font-medium">
              {extractedData.ReferenceNumber || 'N/A'}
            </p>
          </div>
        </div>
      </div>

      <div className="mt-6 bg-white p-4 rounded-lg shadow-sm">
        <h4 className="font-semibold text-green-700 mb-3">Raw Invoice Data</h4>
        <div className="text-sm bg-gray-50 p-3 rounded overflow-auto max-h-60">
          <pre>{JSON.stringify(extractedData, null, 2)}</pre>
        </div>
      </div>
    </div>
  );
        case 'id-documents':
      return (
        <div className="id-document-container bg-blue-50 p-6 rounded-lg border border-blue-200">
          <h3 className="text-xl font-bold text-blue-800 mb-6 border-b border-blue-200 pb-2">
            ID Document Information
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="font-semibold text-blue-700 mb-3">Personal Information</h4>
              <div className="space-y-3">
                <div>
                  <span className="text-xs text-blue-500">Full Name</span>
                  <p className="font-medium">
                    {extractedData.FirstName && extractedData.LastName 
                      ? `${extractedData.FirstName} ${extractedData.LastName}`
                      : extractedData.FirstName || extractedData.LastName || 'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-xs text-blue-500">Date of Birth</span>
                  <p className="font-medium">
                    {extractedData.DateOfBirth 
                      ? new Date(extractedData.DateOfBirth).toLocaleDateString() 
                      : 'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-xs text-blue-500">Nationality</span>
                  <p className="font-medium">
                    {extractedData.Nationality && extractedData.Nationality !== 'N/A'
                      ? extractedData.Nationality 
                      : 'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-xs text-blue-500">Sex</span>
                  <p className="font-medium">
                    {extractedData.Sex 
                      ? extractedData.Sex === 'F' ? 'Female' 
                        : extractedData.Sex === 'M' ? 'Male' 
                        : extractedData.Sex
                      : 'N/A'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="font-semibold text-blue-700 mb-3">Document Details</h4>
              <div className="space-y-3">
                <div>
                  <span className="text-xs text-blue-500">Document Number</span>
                  <p className="font-medium">
                    {extractedData.DocumentNumber || extractedData.IdNumber || 'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-xs text-blue-500">Issuing Country</span>
                  <p className="font-medium">
                    {extractedData.CountryRegion || extractedData.IssuingCountry || 'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-xs text-blue-500">Date of Expiration</span>
                  <p className="font-medium">
                    {extractedData.DateOfExpiration 
                      ? new Date(extractedData.DateOfExpiration).toLocaleDateString() 
                      : 'N/A'}
                  </p>
                </div>
                <div>
                  <span className="text-xs text-blue-500">Date of Issue</span>
                  <p className="font-medium">
                    {extractedData.DateOfIssue 
                      ? new Date(extractedData.DateOfIssue).toLocaleDateString() 
                      : 'N/A'}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6 bg-white p-4 rounded-lg shadow-sm">
            <h4 className="font-semibold text-blue-700 mb-3">Raw Document Data</h4>
            <div className="text-sm bg-gray-50 p-3 rounded overflow-auto max-h-60">
              <pre>{JSON.stringify(extractedData, null, 2)}</pre>
            </div>
          </div>
        </div>
      );
    case 'tax':
  return (
    <div className="tax-document-container bg-purple-50 p-6 rounded-lg border border-purple-200">
      <h3 className="text-xl font-bold text-purple-800 mb-6 border-b border-purple-200 pb-2">
        Tax Document Information
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white p-4 rounded-lg shadow-sm">
          <h4 className="font-semibold text-purple-700 mb-3">Taxpayer Information</h4>
          <div className="space-y-3">
            <div>
              <span className="text-xs text-purple-500">Employee Name</span>
              <p className="font-medium">
                {extractedData.EmployeeName || 'N/A'}
              </p>
            </div>
            <div>
              <span className="text-xs text-purple-500">Employer Name</span>
              <p className="font-medium">
                {extractedData.EmployerName || 'N/A'}
              </p>
            </div>
            <div>
              <span className="text-xs text-purple-500">Tax Year</span>
              <p className="font-medium">
                {extractedData.TaxYear || 'N/A'}
              </p>
            </div>
            <div>
              <span className="text-xs text-purple-500">SSN/ITIN</span>
              <p className="font-medium">
                {extractedData.SocialSecurityNumber || 'N/A'}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-4 rounded-lg shadow-sm">
          <h4 className="font-semibold text-purple-700 mb-3">Financial Information</h4>
          <div className="space-y-3">
            <div>
              <span className="text-xs text-purple-500">Wages</span>
              <p className="font-medium">
                {extractedData.Wages && extractedData.Wages !== 'N/A' 
                  ? `$${parseFloat(extractedData.Wages).toFixed(2)}` 
                  : 'N/A'}
              </p>
            </div>
            <div>
              <span className="text-xs text-purple-500">Federal Tax Withheld</span>
              <p className="font-medium">
                {extractedData.FederalTaxWithheld && extractedData.FederalTaxWithheld !== 'N/A'
                  ? `$${parseFloat(extractedData.FederalTaxWithheld).toFixed(2)}`
                  : 'N/A'}
              </p>
            </div>
            <div>
              <span className="text-xs text-purple-500">State Tax Withheld</span>
              <p className="font-medium">
                {extractedData.StateTaxWithheld && extractedData.StateTaxWithheld !== 'N/A'
                  ? `$${parseFloat(extractedData.StateTaxWithheld).toFixed(2)}`
                  : 'N/A'}
              </p>
            </div>
            <div>
              <span className="text-xs text-purple-500">Social Security Wages</span>
              <p className="font-medium">
                {extractedData.SocialSecurityWages && extractedData.SocialSecurityWages !== 'N/A'
                  ? `$${parseFloat(extractedData.SocialSecurityWages).toFixed(2)}`
                  : 'N/A'}
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-6 bg-white p-4 rounded-lg shadow-sm">
        <h4 className="font-semibold text-purple-700 mb-3">Raw Document Data</h4>
        <div className="text-sm bg-gray-50 p-3 rounded overflow-auto max-h-60">
          <pre>{JSON.stringify(extractedData, null, 2)}</pre>
        </div>
      </div>
    </div>
  );
      case 'contracts':
        return (
          <>
            <p><strong>Contract Title:</strong> {azureResponse.ContractTitle || 'N/A'}</p>
            <p><strong>Parties Involved:</strong> {azureResponse.PartiesInvolved || 'N/A'}</p>
            <p><strong>Effective Date:</strong> {azureResponse.EffectiveDate || 'N/A'}</p>
            <p><strong>Contract Type:</strong> {azureResponse.ContractType || 'N/A'}</p>
            {azureResponse.contractContent && (
              <p><strong>Content:</strong> {azureResponse.contractContent.slice(0, 200) + (azureResponse.contractContent.length > 200 ? '...' : '')}</p>
            )}
            {azureResponse.prettyJson && (
              <div className="mt-4">
                <strong>Full Analysis:</strong>
                <pre className="text-sm text-gray-700 overflow-auto max-h-60">
                  {JSON.stringify(azureResponse.prettyJson, null, 2)}
                </pre>
              </div>
            )}
                  {(openModal || isAddTransactionOpen) && <TransactionPopup />}

          </>
        );
      default:
        return (
          <>
            <p className="text-sm text-gray-500">No specific data extracted for this category</p>
            {azureResponse.prettyJson && (
              <div className="mt-4">
                <strong>Full Analysis:</strong>
                <pre className="text-sm text-gray-700 overflow-auto max-h-60">
                  {JSON.stringify(azureResponse.prettyJson, null, 2)}
                </pre>
              </div>
            )}
          </>
        );
    }
  };

  if (currentView === 'dashboard') {
    return (
      <div className="min-h-screen bg-gray-50 p-6">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center gap-4 mb-8">
            <h1 className="text-3xl font-bold text-gray-900">Family IDs</h1>
            <button
              onClick={() => setShowAddModal(true)}
              className="w-10 h-10 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center text-white transition-colors"
            >
              <Plus size={20} />
            </button>
            <span className="text-blue-500 text-sm font-medium">17 recommended items</span>
          </div>

          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">People</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {familyMembers.map((member) => (
                <div
                  key={member.id}
                  className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => {
                    setSelectedMember(member);
                    setCurrentView('member-detail');
                  }}
                >
                  <div className="flex items-center gap-4 mb-4">
                    <div className={`w-12 h-12 ${member.bgColor} rounded-full flex items-center justify-center`}>
                      <span className={`text-lg font-semibold ${member.textColor}`}>
                        {member.initials}
                      </span>
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {member.firstName} {member.lastName}
                      </h3>
                      <p className="text-sm text-gray-500">{member.role}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-1 text-blue-600 text-sm">
                    <span className="text-lg">⚡</span>
                    <span>{member.itemCount} items</span>
                  </div>
                </div>
              ))}
              <div
                className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-md transition-shadow cursor-pointer border-dashed"
                onClick={() => setShowAddModal(true)}
              >
                <div className="flex items-center gap-4 mb-4">
                  <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                    <User className="text-gray-400" size={24} />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-700">New Family ID</h3>
                    <p className="text-sm text-gray-500">Recommended item</p>
                  </div>
                </div>
                <div className="flex items-center gap-1 text-blue-600 text-sm">
                  <Plus size={16} />
                  <span>Add this item</span>
                </div>
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Pets</h2>
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <div className="flex items-center gap-4 mb-4">
                <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center">
                  <span className="text-2xl">🐾</span>
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Pattnaik Family Pet</h3>
                </div>
              </div>
              <div className="flex items-center gap-1 text-blue-600 text-sm">
                <span className="text-lg">⚡</span>
                <span>2 items</span>
                <span className="text-gray-400 ml-2">Pre-populated</span>
              </div>
            </div>
          </div>
        </div>

        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-semibold">Add New Family Member</h2>
                <button
                  onClick={() => setShowAddModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X size={24} />
                </button>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    First Name
                  </label>
                  <input
                    type="text"
                    value={newMember.firstName}
                    onChange={(e) => setNewMember({ ...newMember, firstName: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter first name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={newMember.lastName}
                    onChange={(e) => setNewMember({ ...newMember, lastName: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Enter last name"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Date of Birth
                  </label>
                  <input
                    type="date"
                    value={newMember.dob}
                    onChange={(e) => setNewMember({ ...newMember, dob: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              <div className="flex gap-3 mt-6">
                <button
                  onClick={() => setShowAddModal(false)}
                  className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleAddMember}
                  disabled={!newMember.firstName || !newMember.lastName || !newMember.dob}
                  className="flex-1 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white rounded-lg transition-colors"
                >
                  Add Member
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  }

  if (currentView === 'member-detail') {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center gap-4 mb-4">
              <button
                onClick={() => setCurrentView('dashboard')}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <ChevronLeft size={20} />
              </button>
              <span className="text-sm text-gray-500">Family IDs</span>
              <span className="text-sm text-gray-400">/</span>
              <span className="text-sm text-red-500">
                {selectedMember?.firstName} {selectedMember?.lastName}
              </span>
            </div>
            <div className="flex items-center gap-4">
              <div className={`w-16 h-16 ${selectedMember?.bgColor} rounded-full flex items-center justify-center`}>
                <span className={`text-2xl font-semibold ${selectedMember?.textColor}`}>
                  {selectedMember?.initials}
                </span>
              </div>
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">
                  {selectedMember?.firstName} {selectedMember?.lastName}
                </h1>
                <p className="text-gray-600">{selectedMember?.role}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mb-6">
                  <div className="flex items-center gap-4 mb-4">
                    <Calendar className="text-gray-400" size={20} />
                    <h3 className="font-semibold text-gray-900">Birthday</h3>
                  </div>
                  <p className="text-lg font-medium">{formatDate(selectedMember?.dob)}</p>
                  <p className="text-sm text-gray-500">
                    Birthday in {getDaysUntilBirthday(selectedMember?.dob)} days
                  </p>
                </div>

                <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                  <h3 className="font-semibold text-gray-900 mb-6">Document Categories</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {documentCategories.map((category) => {
                      const IconComponent = category.icon;
                      return (
                        <div
                          key={category.id}
                          className="p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow cursor-pointer"
                          onClick={() => {
                            setSelectedCategory(category);
                            setIdDocumentFiles({ front: null, back: null });
                            setCurrentView('upload');
                          }}
                        >
                          <div className="flex items-center gap-3 mb-3">
                            <div className={`p-2 ${category.bgColor} rounded-lg`}>
                              <IconComponent className={category.iconColor} size={20} />
                            </div>
                            <h4 className="font-medium text-gray-900">{category.name}</h4>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-sm text-gray-500">
                              {category.count} items
                            </span>
                            <Plus className="text-blue-500" size={16} />
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-2">Contact Information</h4>
                  <p className="text-sm text-gray-500">No contact info added yet</p>
                </div>

                <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-2">Reminders</h4>
                  <div className="space-y-2">
                    <div className="flex items-start gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5"></div>
                      <div>
                        <p className="text-sm font-medium">Birthday reminder</p>
                        <p className="text-xs text-gray-500">
                          Trustworthy will remind you before {selectedMember?.firstName}'s birthday
                        </p>
                        <p className="text-xs text-gray-500">Feb 5, 2026, Yearly</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 shadow-sm border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-2">Linked Contacts</h4>
                  <p className="text-sm text-gray-500">No linked contacts</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (currentView === 'upload') {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="bg-white border-b border-gray-200 p-4">
          <div className="max-w-7xl mx-auto">
            <div className="flex items-center gap-4 mb-4">
              <button
                onClick={() => {
                  setCurrentView('member-detail');
                  setIdDocumentFiles({ front: null, back: null });
                  dispatch(setPreviewStep('upload'));
                }}
                className="p-2 hover:bg-gray-100 rounded-full"
              >
                <ChevronLeft size={20} />
              </button>
              <span className="text-sm text-gray-500">Family IDs</span>
              <span className="text-sm text-gray-400">/</span>
              <span className="text-sm text-gray-500">
                {selectedMember?.firstName} {selectedMember?.lastName}
              </span>
              <span className="text-sm text-gray-400">/</span>
              <span className="text-sm text-red-500">{selectedCategory?.name}</span>
            </div>
            <div className="flex items-center gap-4">
              <div className={`w-16 h-16 ${selectedMember?.bgColor} rounded-full flex items-center justify-center`}>
                <span className={`text-2xl font-semibold ${selectedMember?.textColor}`}>
                  {selectedMember?.initials}
                </span>
              </div>
              <div>
                <h1 className="text-2xl font-semibold text-gray-900">
                  {selectedMember?.firstName} {selectedMember?.lastName}
                </h1>
                <p className="text-gray-600">{selectedCategory?.name}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
              <h3 className="font-semibold text-gray-900 mb-6">Upload {selectedCategory?.name}</h3>

              {uploading && (
                <p className="text-blue-500 mb-4">Uploading and processing document, please wait...</p>
              )}
              {error && (
                <p className="text-red-500 mb-4">
                  Error: {error || 'Failed to process document. Please try again or check server logs.'}
                </p>
              )}

              {previewStep === 'upload' && selectedCategory?.id === 'id-documents' && (
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-4">ID Document</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer">
                      <input
                        type="file"
                        ref={fileInputRef}
                        onChange={(e) => handleFileChange(e, 'front')}
                        accept="image/*,application/pdf"
                        className="hidden"
                      />
                      <Upload className="mx-auto text-gray-400 mb-2" size={32} />
                      <p className="text-sm text-gray-600 mb-1">
                        {idDocumentFiles.front ? idDocumentFiles.front.name : 'Drop front side here or'}
                      </p>
                      <button
                        onClick={() => fileInputRef.current.click()}
                        className="text-blue-500 text-sm hover:underline"
                      >
                        Browse files
                      </button>
                    </div>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors cursor-pointer">
                      <input
                        type="file"
                        ref={backFileInputRef}
                        onChange={(e) => handleFileChange(e, 'back')}
                        accept="image/*,application/pdf"
                        className="hidden"
                      />
                      <Upload className="mx-auto text-gray-400 mb-2" size={32} />
                      <p className="text-sm text-gray-600 mb-1">
                        {idDocumentFiles.back ? idDocumentFiles.back.name : 'Drop back side here (optional) or'}
                      </p>
                      <button
                        onClick={() => backFileInputRef.current.click()}
                        className="text-blue-500 text-sm hover:underline"
                      >
                        Browse files
                      </button>
                    </div>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">
                    Note: Uploading the front side is required. The back side is optional.
                  </p>
                  <div className="space-y-3 mt-4">
                    <button className="flex items-center gap-2 text-blue-500 text-sm hover:underline">
                      <Plus size={16} />
                      Number
                    </button>
                    <button className="flex items-center gap-2 text-blue-500 text-sm hover:underline">
                      <Plus size={16} />
                      State issued
                    </button>
                    <button className="flex items-center gap-2 text-blue-500 text-sm hover:underline">
                      <Plus size={16} />
                      Expiration date
                    </button>
                  </div>
                </div>
              )}
              {previewStep === 'upload' && selectedCategory?.id !== 'id-documents' && (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-12 text-center hover:border-blue-400 transition-colors cursor-pointer">
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileChange}
                    accept="image/*,application/pdf"
                    className="hidden"
                  />
                  <Upload className="mx-auto text-gray-400 mb-4" size={48} />
                  <p className="text-lg font-medium text-gray-600 mb-2">
                    Drop your {selectedCategory?.name.toLowerCase()} here
                  </p>
                  <p className="text-sm text-gray-500 mb-4">or</p>
                  <button
                    onClick={() => fileInputRef.current.click()}
                    className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
                  >
                    Browse files
                  </button>
                </div>
              )}
              {previewStep === 'preview' && previewData && (
                <div className="mt-6">
                  <h4 className="font-medium text-gray-900 mb-4">Document Preview</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h5 className="font-medium text-gray-700 mb-2">Original Document</h5>
                      {previewData.originalFile ? (
                        previewData.originalFile.endsWith('.pdf') ? (
                          <embed
                            src={previewData.originalFile}
                            type="application/pdf"
                            className="max-w-full h-96 rounded-lg border border-gray-200"
                          />
                        ) : (
                          <img
                            src={previewData.originalFile}
                            alt="Original Document"
                            className="max-w-full h-auto rounded-lg border border-gray-200"
                          />
                        )
                      ) : (
                        <p className="text-sm text-gray-500">No preview available</p>
                      )}
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-700 mb-2">Extracted Data</h5>
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        {renderExtractedData(previewData.azureResponse)}
                      </div>
                    </div>
                  </div>
                  {selectedCategory?.id === 'id-documents' && idDocumentFiles.back && (
                    <div className="mt-6">
                      <h5 className="font-medium text-gray-700 mb-2">Back Side Preview (Optional)</h5>
                      {previewData.backFile ? (
                        previewData.backFile.endsWith('.pdf') ? (
                          <embed
                            src={previewData.backFile}
                            type="application/pdf"
                            className="max-w-full h-96 rounded-lg border border-gray-200"
                          />
                        ) : (
                          <img
                            src={previewData.backFile}
                            alt="Back Side Document"
                            className="max-w-full h-auto rounded-lg border border-gray-200"
                          />
                        )
                      ) : (
                        <p className="text-sm text-gray-500">No back side uploaded</p>
                      )}
                    </div>
                  )}
                  <div className="flex gap-3 mt-6">
                    <button
                      onClick={() => {
                        dispatch(setPreviewStep('upload'));
                        setIdDocumentFiles({ front: null, back: null });
                      }}
                      className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleSaveDocument}
                      disabled={isProcessing || !previewData}
                      className="flex-1 px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-300 text-white rounded-lg transition-colors"
                    >
                      Save Document
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default FamilyDocumentManager;