import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  createGoalRequest,
  updateGoalRequest,
  fetchGoalsRequest
} from '../../../../logic/redux/goalSlice';

const CreateGoal = ({ goalToEdit, accounts, isEdit, onClose }) => {
  const dispatch = useDispatch();
  const { isAddingGoal, isUpdatingGoal, error } = useSelector(state => state.goals || {});

  // Form state
  const [formData, setFormData] = useState({
    goalName: '',
    goalAmount: '',
    startDate: '',
    targetDate: '',
    goalType: 'PERSONAL',
    description: ''
  });

  // Account contributions state - simplified to account selection and amounts
  const [accountContributions, setAccountContributions] = useState([]);

  // Initialize form data when editing
  useEffect(() => {
    if (isEdit && goalToEdit) {
      setFormData({
        goalName: goalToEdit.goalName || '',
        goalAmount: goalToEdit.goalAmount?.toString() || '',
        startDate: goalToEdit.startDate || '',
        targetDate: goalToEdit.targetDate || '',
        goalType: goalToEdit.goalType || 'PERSONAL',
        description: goalToEdit.description || ''
      });

      // Initialize account contributions from existing goal
      if (goalToEdit.accounts && goalToEdit.accounts.length > 0) {
        const contributions = goalToEdit.accounts.map(acc => ({
          accountId: acc.accountId,
          amount: acc.currentContribution || acc.allocatedAmount || 0
        }));
        setAccountContributions(contributions);
      }
    }
  }, [isEdit, goalToEdit]);

  // Goal types
  const goalTypes = [
    { value: 'PERSONAL', label: 'Personal' },
    { value: 'INVESTMENT', label: 'Investment' },
    { value: 'EDUCATION', label: 'Education' },
    { value: 'RETIREMENT', label: 'Retirement' },
    { value: 'VEHICLE', label: 'Vehicle' },
    { value: 'VACATION', label: 'Vacation' },
    { value: 'EMERGENCY_FUND', label: 'Emergency Fund' },
    { value: 'HOUSING', label: 'Housing' },
    { value: 'OTHER', label: 'Other' }
  ];

  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Add account contribution
  const handleAddAccountContribution = () => {
    if (accounts && accounts.length > 0) {
      const availableAccounts = accounts.filter(account => 
        !accountContributions.some(contrib => contrib.accountId === (account.id || account.account_id))
      );
      
      if (availableAccounts.length > 0) {
        setAccountContributions(prev => [...prev, {
          accountId: availableAccounts[0].id || availableAccounts[0].account_id,
          amount: 0
        }]);
      }
    }
  };

  // Remove account contribution
  const handleRemoveAccountContribution = (index) => {
    setAccountContributions(prev => prev.filter((_, i) => i !== index));
  };

  // Update account contribution
  const handleAccountContributionChange = (index, field, value) => {
    setAccountContributions(prev => prev.map((contrib, i) => 
      i === index ? { ...contrib, [field]: field === 'amount' ? parseFloat(value) || 0 : value } : contrib
    ));
  };

  // Get account name by ID
  const getAccountName = (accountId) => {
    const account = accounts?.find(acc => (acc.id || acc.account_id) === accountId);
    return account ? (account.name || account.account_name || `Account ${accountId}`) : `Account ${accountId}`;
  };

  // Get account balance by ID
  const getAccountBalance = (accountId) => {
    const account = accounts?.find(acc => (acc.id || acc.account_id) === accountId);
    return account ? account.balance || 0 : 0;
  };

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount || 0);
  };

  // Validate form
  const validateForm = () => {
    if (!formData.goalName.trim()) {
      return 'Goal name is required';
    }
    if (!formData.goalAmount || parseFloat(formData.goalAmount) <= 0) {
      return 'Goal amount must be positive';
    }
    if (!formData.startDate) {
      return 'Start date is required';
    }
    if (!formData.targetDate) {
      return 'Target date is required';
    }
    if (new Date(formData.targetDate) <= new Date(formData.startDate)) {
      return 'Target date must be after start date';
    }
    
    // Validate account contributions
    for (let i = 0; i < accountContributions.length; i++) {
      const contrib = accountContributions[i];
      if (contrib.amount < 0) {
        return `Account contribution amount cannot be negative`;
      }
      if (contrib.amount > getAccountBalance(contrib.accountId)) {
        return `Insufficient balance in ${getAccountName(contrib.accountId)}`;
      }
    }
    
    return null;
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    const validationError = validateForm();
    if (validationError) {
      alert(validationError);
      return;
    }

    // Prepare goal data
    const goalData = {
      goalName: formData.goalName.trim(),
      goalAmount: parseFloat(formData.goalAmount),
      startDate: formData.startDate,
      targetDate: formData.targetDate,
      goalType: formData.goalType,
      description: formData.description.trim(),
      initialContributions: accountContributions.filter(contrib => contrib.amount > 0)
    };

    console.log('Submitting goal data:', goalData);

    try {
      if (isEdit && goalToEdit) {
        // Update existing goal
        dispatch(updateGoalRequest({
          goalId: goalToEdit.id,
          goalData: goalData
        }));
      } else {
        // Create new goal
        dispatch(createGoalRequest(goalData));
      }
      
      // Close modal on successful submission
      // The epic will handle the success/failure
      setTimeout(() => {
        if (!error) {
          dispatch(fetchGoalsRequest()); // Refresh goals list
          onClose();
        }
      }, 1000);
      
    } catch (error) {
      console.error('Error submitting goal:', error);
    }
  };

  // Calculate total initial contributions
  const totalContributions = accountContributions.reduce((sum, contrib) => sum + (contrib.amount || 0), 0);

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-3xl p-8 max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-3xl font-bold text-slate-800">
            {isEdit ? 'Edit Goal' : 'Create New Goal'}
          </h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-slate-100 rounded-full transition-colors"
          >
            <svg className="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Goal Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Goal Name *
              </label>
              <input
                type="text"
                name="goalName"
                value={formData.goalName}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="e.g., Emergency Fund"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Goal Amount *
              </label>
              <input
                type="number"
                name="goalAmount"
                value={formData.goalAmount}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                placeholder="10000"
                min="0"
                step="0.01"
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Start Date *
              </label>
              <input
                type="date"
                name="startDate"
                value={formData.startDate}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Target Date *
              </label>
              <input
                type="date"
                name="targetDate"
                value={formData.targetDate}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                required
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">
              Goal Type *
            </label>
            <select
              name="goalType"
              value={formData.goalType}
              onChange={handleInputChange}
              className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              required
            >
              {goalTypes.map(type => (
                <option key={type.value} value={type.value}>
                  {type.label}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">
              Description
            </label>
            <textarea
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              rows="3"
              className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="Describe your goal..."
            />
          </div>

          {/* Account Contributions Section */}
          <div className="border-t pt-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold text-slate-800">Initial Contributions</h3>
              <button
                type="button"
                onClick={handleAddAccountContribution}
                disabled={!accounts || accountContributions.length >= accounts.length}
                className="text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                style={{ backgroundColor: 'rgb(139, 195, 74)' }}
              >
                Add Account
              </button>
            </div>

            {accountContributions.length === 0 ? (
              <div className="text-center py-8 bg-slate-50 rounded-xl border border-slate-200">
                <p className="text-slate-600">No initial contributions added. You can add them later.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {accountContributions.map((contrib, index) => (
                  <div key={index} className="bg-slate-50 p-4 rounded-xl border border-slate-200">
                    <div className="flex justify-between items-start mb-4">
                      <h4 className="font-medium text-slate-800">Contribution {index + 1}</h4>
                      <button
                        type="button"
                        onClick={() => handleRemoveAccountContribution(index)}
                        className="text-red-600 hover:text-red-800 transition-colors"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Account
                        </label>
                        <select
                          value={contrib.accountId}
                          onChange={(e) => handleAccountContributionChange(index, 'accountId', parseInt(e.target.value))}
                          className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                        >
                          {accounts?.filter(account => 
                            (account.id || account.account_id) === contrib.accountId ||
                            !accountContributions.some(c => c.accountId === (account.id || account.account_id))
                          ).map(account => (
                            <option key={account.id || account.account_id} value={account.id || account.account_id}>
                              {account.name || account.account_name} - {formatCurrency(account.balance)}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-slate-700 mb-2">
                          Amount
                        </label>
                        <input
                          type="number"
                          value={contrib.amount}
                          onChange={(e) => handleAccountContributionChange(index, 'amount', e.target.value)}
                          className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500"
                          placeholder="0.00"
                          min="0"
                          max={getAccountBalance(contrib.accountId)}
                          step="0.01"
                        />
                        <p className="text-xs text-slate-500 mt-1">
                          Available: {formatCurrency(getAccountBalance(contrib.accountId))}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Total Contributions Summary */}
                <div className="bg-green-50 border border-green-200 rounded-xl p-4">
                  <div className="flex justify-between items-center">
                    <span className="font-medium text-green-800">Total Initial Contributions:</span>
                    <span className="font-bold text-lg text-green-600">{formatCurrency(totalContributions)}</span>
                  </div>
                  {parseFloat(formData.goalAmount) > 0 && (
                    <div className="mt-2">
                      <div className="flex justify-between text-sm text-green-700">
                        <span>Progress towards goal:</span>
                        <span>{((totalContributions / parseFloat(formData.goalAmount)) * 100).toFixed(1)}%</span>
                      </div>
                      <div className="mt-1 h-2 bg-green-200 rounded-full">
                        <div 
                          className="h-2 bg-green-500 rounded-full transition-all duration-300"
                          style={{ width: `${Math.min((totalContributions / parseFloat(formData.goalAmount)) * 100, 100)}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Error Display */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-4">
              <p className="text-red-800 font-medium">{error}</p>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex gap-4 pt-6 border-t">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-6 py-3 border border-slate-300 text-slate-700 rounded-xl font-medium hover:bg-slate-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isAddingGoal || isUpdatingGoal}
              className="flex-1 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
              style={{ backgroundColor: 'rgb(139, 195, 74)' }}
            >
              {isAddingGoal || isUpdatingGoal 
                ? (isEdit ? 'Updating...' : 'Creating...') 
                : (isEdit ? 'Update Goal' : 'Create Goal')
              }
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default CreateGoal; 