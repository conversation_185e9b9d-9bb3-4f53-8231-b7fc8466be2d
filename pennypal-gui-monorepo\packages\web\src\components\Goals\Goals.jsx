import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  fetchGoalsRequest,
  fetchGoalsSuccess,
  fetchGoalsFailure,
  fetchGoalRequest,
  fetchGoalSuccess,
  fetchGoalFailure,
  createGoalRequest,
  createGoalSuccess,
  createGoalFailure,
  updateGoalRequest,
  updateGoalSuccess,
  updateGoalFailure,
  contributeToGoalRequest,
  contributeToGoalSuccess,
  contributeToGoalFailure,
  fetchGoalAccountBalanceRequest,
  fetchGoalAccountBalanceSuccess,
  fetchGoalAccountBalanceFailure,
  fetchAccountGoalSummaryRequest,
  fetchAccountGoalSummarySuccess,
  fetchAccountGoalSummaryFailure,
  clearMessages,
  setSelectedGoal,
  clearSelectedGoal
} from '../../../../logic/redux/goalSlice';
import { fetchAccountDetails } from '../../../../logic/redux/accountsDashboardSlice';
import RecurringGoal from './RecurringGoal';
import CreateGoal from './CreateGoal';
import PaymentLoader from '../load/PaymentLoader';

const Goals = () => {
  const dispatch = useDispatch();
  
  // Tab state
  const [activeTab, setActiveTab] = useState('active');
  const [showAIChat, setShowAIChat] = useState(false);

  // Fetch accounts from accountsDashboardSlice
  const { accounts = [], isLoading: accountsLoading = false } = useSelector(state => state.accounts || {});
  const [showRecurringModal, setShowRecurringModal] = useState(false);
  const [recurringGoal, setRecurringGoal] = useState(null);
  const [showCreateGoalModal, setShowCreateGoalModal] = useState(false);
  const [goalToEdit, setGoalToEdit] = useState(null);
  const [isLoadingGoalDetails, setIsLoadingGoalDetails] = useState(false);

  const handleOpenRecurringModal = (goal) => {
    setRecurringGoal(goal);
    setShowRecurringModal(true);
  };

  const handleCloseRecurringModal = () => {
    setShowRecurringModal(false);
    setRecurringGoal(null);
  };

  // Safely extract goal data from Redux state
  const goalState = useSelector(state => {
    if (state.goal && state.goal.goals) {
      return state.goal;
    }
    if (state.goals && state.goals.goals) {
      return state.goals;
    }
    for (const key of Object.keys(state)) {
      if (state[key] && Array.isArray(state[key].goals)) {
        return state[key];
      }
    }
    return { 
      goals: [], 
      selectedGoal: null,
      loading: false,
      error: null,
      successMessage: '',
      isAddingGoal: false,
      isAddingContribution: false,
      goalAccountBalance: null,
      accountGoalSummary: null
    };
  });
  
  const goals = goalState?.goals || [];
  const selectedGoal = goalState?.selectedGoal || null;
  const loading = goalState?.loading || false;
  const error = goalState?.error || null;
  const successMessage = goalState?.successMessage || '';
  const isAddingGoal = goalState?.isAddingGoal || false;
  const isAddingContribution = goalState?.isAddingContribution || false;
  const goalAccountBalance = goalState?.goalAccountBalance || null;
  const accountGoalSummary = goalState?.accountGoalSummary || null;
  
  // Split goals based on completion status
  const activeGoals = goals.filter(goal => {
    const progressPercent = Math.min((goal.currentAmount / goal.goalAmount) * 100, 100);
    return progressPercent < 100;
  });
  
  const completedGoals = goals.filter(goal => {
    const progressPercent = Math.min((goal.currentAmount / goal.goalAmount) * 100, 100);
    return progressPercent >= 100;
  });
  
  // Contribution modal state
  const [showContributeModal, setShowContributeModal] = useState(false);
  const [contributionGoal, setContributionGoal] = useState(null);
  const [contributionAccountId, setContributionAccountId] = useState('');
  const [contributionAmount, setContributionAmount] = useState('');
  
  // Fetch goals and accounts data on component mount
  useEffect(() => {
    dispatch(fetchGoalsRequest());
    dispatch(fetchAccountDetails());
  }, [dispatch]);

  useEffect(() => {
    if (selectedGoal && selectedGoal.id) {
      // Fetch goal account balance
      dispatch(fetchGoalAccountBalanceRequest(selectedGoal.id));
      
      // Fetch account goal summary for first account if exists
      if (selectedGoal.accounts && selectedGoal.accounts.length > 0) {
        dispatch(fetchAccountGoalSummaryRequest(selectedGoal.accounts[0].accountId));
      }
    }
  }, [selectedGoal, dispatch]);

  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        dispatch(clearMessages());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [successMessage, dispatch]);

  const handleSelectGoal = async (goalId) => {
    try {
      // First set the selected goal ID in Redux store
      const selectedGoal = goals.find(g => g.id === goalId);
      if (selectedGoal) {
        dispatch(setSelectedGoal(selectedGoal));
      } else {
        // If not found in local cache, fetch from server
        dispatch(fetchGoalRequest(goalId));
      }
      
      // Switch to the appropriate tab if the goal is in another tab
      const progressPercent = selectedGoal 
        ? Math.min((selectedGoal.currentAmount / selectedGoal.goalAmount) * 100, 100)
        : 0;
      
      if (progressPercent >= 100) {
        setActiveTab('completed');
      } else {
        setActiveTab('active');
      }
    } catch (error) {
      console.error('Error selecting goal:', error);
      dispatch(fetchGoalsFailure(error.message));
    }
  };

  const handleOpenContributeModal = (e, goal) => {
    e.stopPropagation(); // Prevent goal selection when clicking contribute button
    setContributionGoal(goal);
    if (accounts && accounts.length > 0) {
      setContributionAccountId((accounts[0].id || accounts[0].account_id).toString());
    } else {
      setContributionAccountId('');
    }
    
    setContributionAmount('');
    setShowContributeModal(true);
  };

  // Close contribution modal
  const handleCloseContributeModal = () => {
    setShowContributeModal(false);
    setContributionGoal(null);
    setContributionAccountId('');
    setContributionAmount('');
  };

  const handleContributeToGoal = async () => {
    try {
      // Validate input
      if (!contributionGoal || !contributionAccountId || !contributionAmount) {
        dispatch(contributeToGoalFailure("Please select an account and enter an amount"));
        return;
      }
      
      const amount = parseFloat(contributionAmount);
      if (isNaN(amount) || amount <= 0) {
        dispatch(contributeToGoalFailure("Please enter a valid amount"));
        return;
      }
      
      // Create contribution payload
      const contributionData = {
        goalId: contributionGoal.id,
        accountId: parseInt(contributionAccountId),
        amount: amount
      };
      
      console.log("Contributing:", contributionData);
      
      // Dispatch contribution action
      dispatch(contributeToGoalRequest(contributionData));
      handleCloseContributeModal();
    } catch (error) {
      console.error('Error contributing to goal:', error);
      dispatch(contributeToGoalFailure(error.message));
    }
  };

  const handleClearSelection = () => {
    dispatch(setSelectedGoal(null));
    setGoalToEdit(null);
  };

  useEffect(() => {
    return () => {
      // Clean up any pending states when component unmounts
      dispatch(clearMessages());
    };
  }, [dispatch]);

  // Enhanced edit goal handler
  const handleEditGoal = async (e, goal) => {
    e.stopPropagation();
    
    try {
      setIsLoadingGoalDetails(true);
      
      // First, ensure we have the complete goal details
      if (!goal.accounts || goal.accounts.length === 0) {
        console.log("Fetching detailed goal information...");
        dispatch(fetchGoalRequest(goal.id));
        
        // Wait for the Redux state to update
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // Get the updated goal from Redux state
        const updatedGoal = goals.find(g => g.id === goal.id);
        setGoalToEdit(updatedGoal || goal);
      } else {
        setGoalToEdit(goal);
      }
      
      setShowCreateGoalModal(true);
    } catch (error) {
      console.error("Error preparing goal for edit:", error);
      dispatch(fetchGoalFailure(error.message));
      // Fallback: open modal with current goal data
      setGoalToEdit(goal);
      setShowCreateGoalModal(true);
    } finally {
      setIsLoadingGoalDetails(false);
    }
  };

  // Close create/edit goal modal
  const handleCloseCreateGoalModal = () => {
    setShowCreateGoalModal(false);
    setGoalToEdit(null);
    setIsLoadingGoalDetails(false);
  };

  const isOverdue = (goal) => {
    const today = new Date();
    const targetDate = new Date(goal.targetDate);
    const progressPercent = Math.min((goal.currentAmount / goal.goalAmount) * 100, 100);
    return targetDate < today && progressPercent < 100;
  };
 
  // Helper to format account balances
  const formatBalance = (balance) => {
    if (balance === undefined || balance === null) return '$0';
    return balance.toLocaleString('en-US', { style: 'currency', currency: 'USD' });
  };
  
  // Render a goal card
  const renderGoalCard = (goal) => {
    const progressPercent = Math.min((goal.currentAmount / goal.goalAmount) * 100, 100);
    const isCompleted = progressPercent >= 100;
    
    return (
      <div
        key={goal.id}
        className={`group relative bg-white rounded-3xl p-6 transition-all duration-300 hover:shadow-2xl hover:scale-[1.02] cursor-pointer border border-slate-200 overflow-hidden ${
          selectedGoal && selectedGoal.id === goal.id 
            ? 'ring-2 ring-blue-500 ring-offset-2 shadow-xl' 
            : 'hover:border-slate-300'
        }`}
        onClick={() => handleSelectGoal(goal.id)}
      >
        {/* Loading overlay for goal details */}
        {isLoadingGoalDetails && goalToEdit && goalToEdit.id === goal.id && (
          <div className="absolute inset-0 bg-white/80 backdrop-blur-sm flex items-center justify-center z-20">
            <div className="flex flex-col items-center gap-2">
              <div className="w-8 h-8 rounded-full animate-spin" style={{ backgroundColor: 'rgb(139, 195, 74)' }}></div>
              <span className="text-sm text-slate-600">Loading goal details...</span>
            </div>
          </div>
        )}

        {/* Gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-purple-50/20 to-pink-50/30 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
        
        {/* Content */}
        <div className="relative z-10">
          {/* Add the overdue warning here */}
          {isOverdue(goal) && (             
            <div className="mb-4 p-4 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-2xl">            
              <div className="flex items-center justify-between">   
                <div className="flex items-center">
                  <div className="flex-shrink-0 w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center mr-3">     
                    <svg className="w-4 h-4 text-amber-600" fill="currentColor" viewBox="0 0 20 20">       
                      <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />     
                    </svg>   
                  </div>   
                  <div className="">     
                    <p className="text-sm font-semibold text-amber-800 mb-1">       
                      Target date has passed     
                    </p>   
                  </div>
                </div>
                
                {selectedGoal && selectedGoal.id === goal.id && (
                  <button
                    onClick={(e) => handleEditGoal(e, goal)}
                    disabled={isLoadingGoalDetails}
                    className="text-white px-4 py-2 rounded-xl text-xs font-medium transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                    style={{ backgroundColor: 'rgb(139, 195, 74)' }}
                    onMouseEnter={(e) => e.target.style.backgroundColor = 'rgb(124, 179, 66)'}
                    onMouseLeave={(e) => e.target.style.backgroundColor = 'rgb(139, 195, 74)'}
                  >
                    {isLoadingGoalDetails ? 'Loading...' : 'Update Goal'}
                  </button>
                )}
              </div>
            </div>
          )}

          <div className="flex justify-between items-start mb-4">
            <div className="flex-1">
              <h4 className="font-bold text-xl text-slate-800 mb-1 group-hover:text-blue-600 transition-colors">
                {goal.goalName}
              </h4>
              <div className="flex items-center gap-2 text-sm text-slate-600">
                {goal.goalType && (
                  <span className="bg-slate-100 text-slate-700 px-2 py-1 rounded-full text-xs font-medium">
                    {goal.goalType}
                  </span>
                )}
                <span>Start: {new Date(goal.startDate).toLocaleDateString()}</span>
                <span>•</span>
                <span>Target: {new Date(goal.targetDate).toLocaleDateString()}</span>
              </div>
            </div>
            <div className={`ml-4 px-3 py-1.5 rounded-full text-xs font-bold ${
              isCompleted 
                ? 'text-white shadow-lg' 
                : 'text-white shadow-lg'
            }`}
            style={{ backgroundColor: isCompleted ? 'rgb(139, 195, 74)' : 'rgb(139, 195, 74)' }}>
              {isCompleted ? '✓ Completed' : `${Math.round(progressPercent)}%`}
            </div>
          </div>
          
          {goal.description && (
            <div className="mb-4 p-3 bg-slate-50 rounded-xl border border-slate-100">
              <p className="text-sm text-slate-700">{goal.description}</p>
            </div>
          )}
          
          {/* Enhanced Progress Section */}
          <div className="mb-6">
            <div className="flex justify-between items-center mb-3">
              <span className="text-sm font-medium text-slate-600">Progress</span>
              <div className="text-right">
                <div className="font-bold text-lg text-slate-800">
                  {goal.currentAmount ? goal.currentAmount.toLocaleString('en-US', { style: 'currency', currency: 'USD' }) : '$0'}
                </div>
                <div className="text-xs text-slate-500">
                  of {goal.goalAmount.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="h-3 w-full bg-slate-200 rounded-full overflow-hidden">
                <div 
                  className="h-full transition-all duration-700 ease-out"
                  style={{ 
                    width: `${progressPercent}%`,
                    backgroundColor: 'rgb(139, 195, 74)'
                  }}
                ></div>
              </div>
              {progressPercent > 0 && (
                <div 
                  className="absolute top-0 h-3 w-1 bg-white rounded-full shadow-md transition-all duration-700"
                  style={{ left: `${Math.min(progressPercent, 97)}%` }}
                ></div>
              )}
            </div>
          </div>
          
          {/* Modern Action Buttons */}
          <div className="flex flex-wrap gap-2 mb-4">
            {!isCompleted && (
              <>
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    handleOpenRecurringModal(goal);
                  }}
                  className="flex items-center gap-2 text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl"
                  style={{ backgroundColor: 'rgb(139, 195, 74)' }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = 'rgb(124, 179, 66)'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = 'rgb(139, 195, 74)'}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Recurring
                </button>
                <button
                  onClick={(e) => handleOpenContributeModal(e, goal)}
                  className="flex items-center gap-2 text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl"
                  style={{ backgroundColor: 'rgb(139, 195, 74)' }}
                  onMouseEnter={(e) => e.target.style.backgroundColor = 'rgb(124, 179, 66)'}
                  onMouseLeave={(e) => e.target.style.backgroundColor = 'rgb(139, 195, 74)'}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Contribute
                </button>
              </>
            )}
            {/* Edit button - always visible for selected goal */}
            {selectedGoal && selectedGoal.id === goal.id && (
              <button
                onClick={(e) => handleEditGoal(e, goal)}
                disabled={isLoadingGoalDetails}
                className="flex items-center gap-2 text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                style={{ backgroundColor: 'rgb(139, 195, 74)' }}
                onMouseEnter={(e) => !e.target.disabled && (e.target.style.backgroundColor = 'rgb(124, 179, 66)')}
                onMouseLeave={(e) => !e.target.disabled && (e.target.style.backgroundColor = 'rgb(139, 195, 74)')}
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                </svg>
                {isLoadingGoalDetails ? 'Loading...' : 'Edit'}
              </button>
            )}
          </div>
          
          {/* Detailed Accounts expanded view - Updated without allocation percentage */}
          {selectedGoal && selectedGoal.id === goal.id && goal.accounts && goal.accounts.length > 0 && (
            <div className="mt-6 p-4 bg-gradient-to-br from-slate-50 to-blue-50 rounded-2xl border border-slate-200">
              <h5 className="font-semibold text-slate-800 mb-4 flex items-center gap-2">
                <div className="w-6 h-6 rounded-full flex items-center justify-center" style={{ backgroundColor: 'rgba(139, 195, 74, 0.2)' }}>
                  <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" style={{ color: 'rgb(139, 195, 74)' }}>
                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"/>
                    <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd"/>
                  </svg>
                </div>
                Contributing Accounts
              </h5>
              <div className="space-y-3">
                {goal.accounts.map((acc) => (
                  <div key={acc.accountId} className="bg-white p-4 rounded-xl border border-slate-200 hover:shadow-md transition-all duration-200">
                    <div className="flex justify-between items-center mb-3">
                      <div className="font-medium text-slate-800">{acc.accountName}</div>
                      <div className="text-white px-3 py-1 rounded-full text-xs font-medium" style={{ backgroundColor: 'rgb(139, 195, 74)' }}>
                        Contributing
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 mb-3">
                      <div>
                        <div className="text-xs text-slate-500 mb-1">Total Contributed</div>
                        <div className="font-semibold text-slate-800">{formatBalance(acc.currentContribution || acc.allocatedAmount)}</div>
                      </div>
                      <div>
                        <div className="text-xs text-slate-500 mb-1">Goal Balance</div>
                        <div className="font-semibold text-slate-800">{formatBalance(acc.goalAccountBalance || acc.currentContribution)}</div>
                      </div>
                    </div>
                    
                    <div className="relative">
                      <div className="flex justify-between text-xs mb-1">
                        <span className="text-slate-600">Account Progress</span>
                        <span className="font-medium text-blue-600">
                          {formatBalance(acc.currentContribution || 0)} contributed
                        </span>
                      </div>
                      <div className="h-2 w-full bg-slate-200 rounded-full overflow-hidden">
                        <div 
                          className="h-full transition-all duration-500"
                          style={{ 
                            width: `${Math.min((acc.currentContribution || 0) / goal.goalAmount * 100, 100)}%`,
                            backgroundColor: 'rgb(139, 195, 74)'
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Time remaining and status info */}
          {selectedGoal && selectedGoal.id === goal.id && (
            <div className="mt-4 p-4 bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl border border-green-200">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-sm font-medium text-slate-800">Time Remaining</div>
                  <div className="text-lg font-semibold text-green-600">
                    {goal.timeRemaining || 'Not specified'}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-slate-800">Status</div>
                  <div className={`text-lg font-semibold ${goal.status === 'COMPLETED' ? 'text-green-600' : goal.status === 'FAILED' ? 'text-red-600' : 'text-blue-600'}`}>
                    {goal.status === 'IN_PROGRESS' ? 'In Progress' : goal.status === 'COMPLETED' ? 'Completed' : 'Failed'}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };
  
  // Display loading state when fetching initial data
  if ((loading && goals.length === 0) || (accountsLoading && accounts.length === 0)) {
    return <PaymentLoader />;
  }
  
  return (
    <div>
      <div className="">
        <div className="bg-white/80 p-8">
          
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1
                className="text-4xl font-bold mb-2"
                style={{ color: 'rgb(139, 195, 74)' }}
              >
                Financial Goals
              </h1>
            </div>
            <div className="flex items-center gap-4">
              {/* AI Assistant Button */}
              <button
                className="p-3 rounded-xl transition-all duration-200 hover:scale-110 bg-white border border-slate-200 shadow-lg hover:shadow-xl"
                onClick={() => setShowAIChat(true)}
                title="AI Assistant"
                style={{ color: 'rgb(139, 195, 74)' }}
              >
                <svg
                  className="w-6 h-6"
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
                  <path d="M19.5 8.5L20.5 11L23 12L20.5 13L19.5 15.5L18.5 13L16 12L18.5 11L19.5 8.5Z" />
                  <path d="M4.5 16.5L5.5 19L8 20L5.5 21L4.5 23.5L3.5 21L1 20L3.5 19L4.5 16.5Z" />
                </svg>
              </button>
              
              {/* Add Goal Button */}
              <button
                onClick={() => {
                  setGoalToEdit(null);
                  setShowCreateGoalModal(true);
                }}
                style={{ backgroundColor: 'rgb(139, 195, 74)' }}
                className="text-white px-6 py-3 rounded-xl font-medium flex items-center gap-2 transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Goal
              </button>
            </div>
          </div>

          {/* Enhanced Messages */}
          {successMessage && (
            <div className="mb-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-2xl p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-green-800 font-medium">{successMessage}</p>
              </div>
            </div>
          )}
          
          {error && (
            <div className="mb-6 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-2xl p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-red-800 font-medium">{error}</p>
              </div>
            </div>
          )}
          
          {/* No accounts message */}
          {accounts && accounts.length === 0 && (
            <div className="mb-6 bg-gradient-to-r from-amber-50 to-orange-50 border border-amber-200 rounded-2xl p-4">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center">
                  <svg className="w-4 h-4 text-amber-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-amber-800 font-medium">You need to add accounts first before creating goals. Please visit the Accounts section to add your bank accounts.</p>
              </div>
            </div>
          )}
          
          {/* Modern Tab Navigation */}
          <div className="mb-8">
            <div className="flex bg-slate-100 p-1 rounded-2xl w-fit">
              <button
                onClick={() => setActiveTab('active')}
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                  activeTab === 'active'
                    ? 'bg-gradient-to-r from-lime-500 to-green-500 text-white shadow-lg'
                    : 'text-slate-600 hover:text-slate-800'
                }`}
              >
                Active Goals ({activeGoals.length})
              </button>
              <button
                onClick={() => setActiveTab('completed')}
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                  activeTab === 'completed'
                    ? 'bg-gradient-to-r from-lime-500 to-green-500 text-white shadow-lg'
                    : 'text-slate-600 hover:text-slate-800'
                }`}
              >
                Completed ({completedGoals.length})
              </button>
            </div>
          </div>
          
          {/* Clear Selection Button */}
          {selectedGoal && (
            <div className="mb-6 flex justify-end">
              <button
                onClick={handleClearSelection}
                className="bg-slate-100 hover:bg-slate-200 text-slate-700 px-4 py-2 rounded-xl font-medium transition-all duration-200 flex items-center gap-2"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
                Clear Selection
              </button>
            </div>
          )}
          
          {/* Goals Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {activeTab === 'active' && activeGoals.map(renderGoalCard)}
            {activeTab === 'completed' && completedGoals.map(renderGoalCard)}
          </div>
          
          {/* Empty State */}
          {((activeTab === 'active' && activeGoals.length === 0) || 
            (activeTab === 'completed' && completedGoals.length === 0)) && (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="w-24 h-24 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-6">
                <svg className="w-12 h-12 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-slate-800 mb-2">
                {activeTab === 'active' ? 'No Active Goals' : 'No Completed Goals'}
              </h3>
              <p className="text-slate-600 text-center mb-6 max-w-md">
                {activeTab === 'active' 
                  ? 'Start your financial journey by creating your first goal. Set targets for savings, investments, or major purchases.'
                  : 'Complete your first goal to see it here. Keep working towards your active goals!'
                }
              </p>
              {activeTab === 'active' && (
                <button
                  onClick={() => {
                    setGoalToEdit(null);
                    setShowCreateGoalModal(true);
                  }}
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-6 py-3 rounded-xl font-medium flex items-center gap-2 transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Create Your First Goal
                </button>
              )}
            </div>
          )}
        </div>
      </div>
      
      {/* Modals */}
      {showRecurringModal && (
        <RecurringGoal 
          goal={recurringGoal}
          accounts={accounts}
          onClose={handleCloseRecurringModal}
        />
      )}
      
      {showCreateGoalModal && (
        <CreateGoal
          goalToEdit={goalToEdit}
          accounts={accounts}
          isEdit={!!goalToEdit}
          onClose={() => {
            setShowCreateGoalModal(false);
            setGoalToEdit(null);
          }}
        />
      )}
      
      {/* Contribute Modal */}
      {showContributeModal && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-3xl p-8 max-w-md w-full shadow-2xl">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-2xl font-bold text-slate-800">Contribute to Goal</h3>
              <button
                onClick={handleCloseContributeModal}
                className="p-2 hover:bg-slate-100 rounded-full transition-colors"
              >
                <svg className="w-6 h-6 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            
            {contributionGoal && (
              <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl border border-blue-200">
                <h4 className="font-semibold text-slate-800 mb-1">{contributionGoal.goalName}</h4>
                <div className="text-sm text-slate-600">
                  Target: {contributionGoal.goalAmount.toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
                </div>
                <div className="text-sm text-slate-600">
                  Current: {(contributionGoal.currentAmount || 0).toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
                </div>
                <div className="text-sm text-slate-600">
                  Remaining: {(contributionGoal.goalAmount - (contributionGoal.currentAmount || 0)).toLocaleString('en-US', { style: 'currency', currency: 'USD' })}
                </div>
              </div>
            )}
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Select Account
                </label>
                <select
                  value={contributionAccountId}
                  onChange={(e) => setContributionAccountId(e.target.value)}
                  className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select an account</option>
                  {accounts && accounts.map(account => (
                    <option key={account.id || account.account_id} value={account.id || account.account_id}>
                      {account.name || account.account_name} - {formatBalance(account.balance)}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Amount
                </label>
                <input
                  type="number"
                  value={contributionAmount}
                  onChange={(e) => setContributionAmount(e.target.value)}
                  placeholder="Enter amount"
                  min="0"
                  step="0.01"
                  className="w-full px-4 py-3 border border-slate-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            
            <div className="flex gap-3 mt-8">
              <button
                onClick={handleCloseContributeModal}
                className="flex-1 px-6 py-3 border border-slate-300 text-slate-700 rounded-xl font-medium hover:bg-slate-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleContributeToGoal}
                disabled={isAddingContribution}
                className="flex-1 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 hover:scale-105 shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed"
                style={{ backgroundColor: 'rgb(139, 195, 74)' }}
              >
                {isAddingContribution ? 'Contributing...' : 'Contribute'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* AI Chat Modal */}
      {showAIChat && (
        <>
          {/* Transparent overlay for click-outside-to-close */}
          <div
            className="fixed inset-0 bg-black/10 z-40"
            onClick={() => setShowAIChat(false)}
          />

          {/* Chatbox modal */}
          <div
            className="fixed top-0 right-0 h-full w-96 border-l shadow-2xl z-50 transform transition-transform duration-300 ease-in-out"
            style={{
              backgroundColor: "#fff",
              borderColor: "#e2e8f0",
            }}
          >
            {/* Chat Header */}
            <div className="flex justify-between items-center p-4 border-b border-gray-200">
              <div className="flex items-center gap-3">
                <div className="p-2 rounded-lg bg-purple-500">
                  <svg
                    className="w-5 h-5 text-white"
                    fill="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
                    <path d="M19.5 8.5L20.5 11L23 12L20.5 13L19.5 15.5L18.5 13L16 12L18.5 11L19.5 8.5Z" />
                    <path d="M4.5 16.5L5.5 19L8 20L5.5 21L4.5 23.5L3.5 21L1 20L3.5 19L4.5 16.5Z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold">AI Assistant</h3>
                  <p className="text-xs text-gray-600">Goal insights & advice</p>
                </div>
              </div>
              <button
                onClick={() => setShowAIChat(false)}
                className="p-2 rounded-lg transition-all duration-200 hover:scale-110 text-gray-600 hover:text-gray-800 hover:bg-gray-100"
              >
                <svg
                  className="w-5 h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </button>
            </div>

            {/* Chat Messages Area */}
            <div className="flex-1 p-4 overflow-y-auto bg-gray-50/30">
              <div className="space-y-4">
                <div className="flex items-start gap-2">
                  <div className="p-1.5 rounded-full bg-purple-500 flex-shrink-0 mt-1">
                    <svg
                      className="w-3 h-3 text-white"
                      fill="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
                    </svg>
                  </div>
                  <div className="rounded-xl p-3 bg-white text-gray-800 shadow-sm">
                    <p className="text-sm">👋 Hi! I can help you with:</p>
                    <ul className="mt-2 space-y-1 text-xs">
                      <li>• Goal analysis</li>
                      <li>• Progress tips</li>
                      <li>• Timeline advice</li>
                      <li>• Savings strategies</li>
                    </ul>
                    <p className="mt-2 text-xs">What can I help you with?</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-2">
                  {[
                    "Analyze goals",
                    "Goal tips",
                    "Progress insights",
                    "Savings advice",
                  ].map((suggestion) => (
                    <button
                      key={suggestion}
                      className="px-3 py-2 rounded-lg text-sm transition-all duration-200 hover:scale-[1.02] text-left bg-white hover:bg-gray-50 text-gray-700 border border-gray-200 shadow-sm"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Chat Input */}
            <div className="p-4 border-t border-gray-200">
              <div className="space-y-3">
                <textarea
                  placeholder="Ask about your goals..."
                  className="w-full p-3 rounded-xl resize-none transition-all duration-200 text-sm bg-white border border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 focus:outline-none"
                  rows={2}
                />
                <button className="w-full py-2.5 bg-purple-500 hover:bg-purple-600 text-white rounded-xl transition-all duration-200 hover:scale-[1.02] shadow-lg text-sm font-medium">
                  Send Message
                </button>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default Goals;