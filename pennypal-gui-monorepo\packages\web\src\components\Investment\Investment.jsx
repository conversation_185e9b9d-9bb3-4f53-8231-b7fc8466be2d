import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  fetchInvestmentsRequest, 
  fetchPerformanceRequest,
  resetInvestmentStatus
} from '../../../../logic/redux/investmentSlice';
import {
  Box,
  Container,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  Alert,
  Grid,
  Card,
  CardContent
} from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import TrendingDownIcon from '@mui/icons-material/TrendingDown';
import InvestmentChart from './InvestmentChart';
import PaymentLoader from '../load/PaymentLoader'; // Adjust the path as needed
const Investment = ({ darkMode }) => {
  const dispatch = useDispatch();
  const userId = useSelector(state => state.auth.user?.id || '1');
  const investmentState = useSelector(state => state.mock_investments);
  const { loading, error } = investmentState;

  const [showAIChat, setShowAIChat] = useState(false);

  useEffect(() => {
    dispatch(fetchInvestmentsRequest(userId));
    dispatch(fetchPerformanceRequest(userId));
    return () => dispatch(resetInvestmentStatus());
  }, [dispatch, userId]);

  const { processedInvestments, investmentAccounts, performanceMetrics, portfolioTotal } = React.useMemo(() => {
    const investmentData = 
      investmentState.investmentData || 
      investmentState.data || 
      investmentState.investments;

    let processed = {};
    let accounts = [];
    let total = 0;

    if (investmentData?.success && investmentData?.rawData) {
      const { rawData } = investmentData;
      const holdings = rawData.holdings || [];
      const securities = rawData.securities || [];
      accounts = rawData.accounts || [];

      const securitiesMap = securities.reduce((acc, sec) => {
        acc[sec.security_id] = sec;
        return acc;
      }, {});

      processed = holdings.reduce((acc, holding) => {
        const accountId = holding.account_id;
        if (!acc[accountId]) acc[accountId] = [];

        const security = securitiesMap[holding.security_id];
        if (security) {
          const price = holding.institution_price || 0;
          const totalValue = holding.quantity * price;
          
          // Add to portfolio total
          total += totalValue;

          acc[accountId].push({
            ...holding,
            ticker_symbol: security.ticker_symbol || 'N/A',
            name: security.name || 'Unknown Security',
            close_price: price,
            total_value: totalValue,
            type: security.type || 'Unknown',
            fixed_income: security.fixed_income
          });
        }
        return acc;
      }, {});
    }

    return {
      processedInvestments: processed,
      investmentAccounts: accounts,
      performanceMetrics: investmentState.performanceMetrics || null,
      portfolioTotal: total
    };
  }, [investmentState]);

  const getAccountName = (accountId) => {
    const account = investmentAccounts.find(acc => acc.account_id === accountId);
    return account ? account.name : `Account ${accountId}`;
  };

  // Calculate total across all accounts
  const calculateTotalPortfolioValue = () => {
    let total = 0;
    Object.keys(processedInvestments).forEach(accountId => {
      total += processedInvestments[accountId].reduce((sum, inv) => sum + (Number(inv.total_value) || 0), 0);
    });
    return total;
  };

  if (loading) {
    return (
      <div className={`min-h-screen w-full p-5 flex flex-col items-center justify-center ${darkMode ? 'bg-gray-900 text-gray-100' : 'bg-white text-gray-900'}`}>
        <PaymentLoader darkMode={darkMode} />
        <p className={`mt-4 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Loading investment data...
        </p>
      </div>
    );
  }

  if (error) {
    return (
      <Box sx={{ mt: 2 }}>
        <Alert 
          severity="error" 
          sx={{ 
            backgroundColor: darkMode ? '#451a03' : undefined, 
            color: darkMode ? '#fed7aa' : undefined 
          }}
        >
          {error}
        </Alert>
      </Box>
    );
  }

  const hasInvestments = Object.keys(processedInvestments).length > 0;
  const totalPortfolioValue = calculateTotalPortfolioValue();

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };

  const formatPercentage = (value) => {
    return new Intl.NumberFormat('en-US', {
      style: 'percent',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(value / 100);
  };

  // Ensure the performance metrics match the actual total if we have investments
  const displayedPerformanceMetrics = performanceMetrics && hasInvestments ? {
    ...performanceMetrics,
    totalValue: totalPortfolioValue,
    // Recalculate other metrics based on the correct total
    totalGain: totalPortfolioValue - performanceMetrics.totalCost,
    totalGainPercent: ((totalPortfolioValue - performanceMetrics.totalCost) / performanceMetrics.totalCost) * 100,
    dailyChange: totalPortfolioValue * 0.01, // 1% daily change (matching backend calculation)
    dailyChangePercent: 1.0 // 1% as defined in backend
  } : performanceMetrics;

  return (
    <div className={`p-5 w-full min-h-screen ${darkMode ? 'bg-gray-900 text-gray-100' : 'bg-white text-gray-900'} font-roboto text-lg`}>
    <div className="flex items-center justify-between mb-10">
  <h1 className={`text-2xl ${darkMode ? 'text-white' : 'text-black'}`}>
    Investment Portfolio
  </h1>

  <button
    className="p-3 rounded-xl transition-all duration-200 hover:scale-110 bg-white border border-slate-200 shadow-lg"
    onClick={() => setShowAIChat(true)}
    title="AI Assistant"
  >
    <svg
      className="w-6 h-6 text-purple-500"
      fill="currentColor"
      viewBox="0 0 24 24"
    >
      <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
      <path d="M19.5 8.5L20.5 11L23 12L20.5 13L19.5 15.5L18.5 13L16 12L18.5 11L19.5 8.5Z" />
      <path d="M4.5 16.5L5.5 19L8 20L5.5 21L4.5 23.5L3.5 21L1 20L3.5 19L4.5 16.5Z" />
    </svg>
  </button>
</div>

    
      <InvestmentChart userId={userId} darkMode={darkMode} />

      {/* Performance Metrics Grid */}
      {displayedPerformanceMetrics && (
        <Box sx={{ mb: 4 }}>
          <Typography 
            variant="h6" 
            sx={{ 
              mb: 2, 
              color: darkMode ? 'white' : 'text.primary' 
            }}
          >
            Portfolio Performance
          </Typography>
          <Grid container spacing={2}>
            {/* Total Value */}
            <Grid item xs={12} sm={6} md={4}>
              <Card 
                sx={{ 
                  backgroundColor: darkMode ? '#1f2937' : 'white',
                  color: darkMode ? 'white' : 'text.primary'
                }}
              >
                <CardContent>
                  <Typography 
                    color={darkMode ? 'gray.400' : 'textSecondary'} 
                    gutterBottom
                  >
                    Portfolio Value
                  </Typography>
                  <Typography 
                    variant="h5" 
                    component="div"
                    sx={{ color: darkMode ? 'white' : 'text.primary' }}
                  >
                    {formatCurrency(displayedPerformanceMetrics.totalValue)}
                  </Typography>
                  <Typography 
                    variant="body2" 
                    color={darkMode ? 'gray.400' : 'textSecondary'}
                  >
                    Total Cost: {formatCurrency(displayedPerformanceMetrics.totalCost)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Daily Change */}
            <Grid item xs={12} sm={6} md={4}>
              <Card 
                sx={{ 
                  backgroundColor: darkMode ? '#1f2937' : 'white',
                  color: darkMode ? 'white' : 'text.primary'
                }}
              >
                <CardContent>
                  <Typography 
                    color={darkMode ? 'gray.400' : 'textSecondary'} 
                    gutterBottom
                  >
                    Daily Change
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {displayedPerformanceMetrics.dailyChange >= 0 ? (
                      <TrendingUpIcon color="success" />
                    ) : (
                      <TrendingDownIcon color="error" />
                    )}
                    <Typography 
                      variant="h5" 
                      component="div" 
                      color={displayedPerformanceMetrics.dailyChange >= 0 ? 'success.main' : 'error.main'}
                      sx={{ ml: 1 }}
                    >
                      {formatCurrency(displayedPerformanceMetrics.dailyChange)}
                    </Typography>
                  </Box>
                  <Typography 
                    variant="body2" 
                    color={displayedPerformanceMetrics.dailyChangePercent >= 0 ? 'success.main' : 'error.main'}
                  >
                    {formatPercentage(displayedPerformanceMetrics.dailyChangePercent)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>

            {/* Total Gain/Loss */}
            <Grid item xs={12} sm={6} md={4}>
              <Card 
                sx={{ 
                  backgroundColor: darkMode ? '#1f2937' : 'white',
                  color: darkMode ? 'white' : 'text.primary'
                }}
              >
                <CardContent>
                  <Typography 
                    color={darkMode ? 'gray.400' : 'textSecondary'} 
                    gutterBottom
                  >
                    Total Gain/Loss
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {displayedPerformanceMetrics.totalGain >= 0 ? (
                      <TrendingUpIcon color="success" />
                    ) : (
                      <TrendingDownIcon color="error" />
                    )}
                    <Typography 
                      variant="h5" 
                      component="div"
                      color={displayedPerformanceMetrics.totalGain >= 0 ? 'success.main' : 'error.main'}
                      sx={{ ml: 1 }}
                    >
                      {formatCurrency(displayedPerformanceMetrics.totalGain)}
                    </Typography>
                  </Box>
                  <Typography 
                    variant="body2"
                    color={displayedPerformanceMetrics.totalGainPercent >= 0 ? 'success.main' : 'error.main'}
                  >
                    {formatPercentage(displayedPerformanceMetrics.totalGainPercent)}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}

      {!hasInvestments ? (
        <Alert 
          severity="info" 
          sx={{ 
            backgroundColor: darkMode ? '#1e3a8a' : undefined, 
            color: darkMode ? '#bfdbfe' : undefined 
          }}
        >
          No investment data available.
        </Alert>
      ) : (
        <>
          {Object.keys(processedInvestments).map(accountId => (
            <Box key={accountId} sx={{ mb: 4 }}>
              <Typography 
                variant="h6" 
                sx={{ 
                  mb: 2, 
                  color: darkMode ? 'white' : 'text.primary' 
                }}
              >
                {getAccountName(accountId)}
              </Typography>

              <TableContainer 
                component={Paper} 
                sx={{ 
                  backgroundColor: darkMode ? '#1f2937' : 'white' 
                }}
              >
                <Table>
                  <TableHead
                    sx={{
                      backgroundColor: darkMode ? '#1f2937' : '#c5e1a5', // Fixed background color for table header
                    }}
                  >
                    <TableRow>
                      <TableCell sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 16, fontWeight: 'bold' }}>
                        Security Name
                      </TableCell>
                      <TableCell sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 16, fontWeight: 'bold' }}>
                        Ticker
                      </TableCell>
                      <TableCell sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 16,  fontWeight: 'bold' }}>
                        Type
                      </TableCell>
                      <TableCell 
                        align="right" 
                        sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 16 , fontWeight: 'bold' }}
                      >
                        Quantity
                      </TableCell>
                      <TableCell 
                        align="right" 
                        sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 16, fontWeight: 'bold' }}
                      >
                        Price ($)
                      </TableCell>
                      <TableCell 
                        align="right" 
                        sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 16, fontWeight: 'bold' }}
                      >
                        Total Value ($)
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {processedInvestments[accountId].map((investment, idx) => (
                      <TableRow key={investment.security_id || idx}>
                        <TableCell sx={{ color: darkMode ? 'white' : 'text.primary' , fontSize: 14 }}>
                          {investment.name}
                        </TableCell>
                        <TableCell sx={{ color: darkMode ? 'white' : 'text.primary' , fontSize: 14}}>
                          {investment.ticker_symbol}
                        </TableCell>
                        <TableCell sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 14 }}>
                          {investment.type}
                        </TableCell>
                        <TableCell 
                          align="right" 
                          sx={{ color: darkMode ? 'white' : 'text.primary' , fontSize: 14}}
                        >
                          {Number(investment.quantity).toFixed(2)}
                        </TableCell>
                        <TableCell 
                          align="right" 
                          sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 14 }}
                        >
                          {Number(investment.close_price).toFixed(2)}
                        </TableCell>
                        <TableCell 
                          align="right" 
                          sx={{ color: darkMode ? 'white' : 'text.primary', fontSize: 14 }}
                        >
                          {Number(investment.total_value).toFixed(2)}
                        </TableCell>
                      </TableRow>
                    ))}
                    <TableRow>
                      <TableCell 
                        colSpan={5} 
                        align="right" 
                        sx={{ color: darkMode ? 'white' : 'text.primary' }}
                      >
                        <strong>Account Total:</strong>
                      </TableCell>
                      <TableCell 
                        align="right" 
                        sx={{ color: darkMode ? 'white' : 'text.primary' }}
                      >
                        <strong>
                          {processedInvestments[accountId]
                            .reduce((sum, inv) => sum + (Number(inv.total_value) || 0), 0)
                            .toFixed(2)}
                        </strong>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          ))}
          
          {/* Portfolio total across all accounts */}
          {Object.keys(processedInvestments).length > 1 && (
            <Box sx={{ mb: 4 }}>
              <TableContainer 
                component={Paper} 
                sx={{ 
                  backgroundColor: darkMode ? '#1f2937' : 'white' 
                }}
              >
                <Table>
                  <TableBody>
                    <TableRow>
                      <TableCell 
                        colSpan={5} 
                        align="right"
                      >
                        <Typography 
                          variant="h6"
                          sx={{ color: darkMode ? 'white' : 'text.primary' }}
                        >
                          Portfolio Total:
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography 
                          variant="h6"
                          sx={{ color: darkMode ? 'white' : 'text.primary' }}
                        >
                          {formatCurrency(totalPortfolioValue)}
                        </Typography>
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </Box>
          )}
        </>
      )}
    {/* </Container> */}
   

  {showAIChat && (
      <>
        {/* Transparent overlay for click-outside-to-close */}
        <div
          className="fixed inset-0 bg-black/10 z-40"
          onClick={() => setShowAIChat(false)}
        />
        {/* Chatbox modal */}
        <div
          className="fixed top-0 right-0 h-full w-96 border-l shadow-2xl z-50 transform transition-transform duration-300 ease-in-out"
          style={{
            backgroundColor: "#fff",
            borderColor: "#e2e8f0"
          }}
        >
          {/* Chat Header */}
          <div className="flex justify-between items-center p-4 border-b border-gray-200">
            <div className="flex items-center gap-3">
              <div className="p-2 rounded-lg bg-purple-500">
                <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
                  <path d="M19.5 8.5L20.5 11L23 12L20.5 13L19.5 15.5L18.5 13L16 12L18.5 11L19.5 8.5Z" />
                  <path d="M4.5 16.5L5.5 19L8 20L5.5 21L4.5 23.5L3.5 21L1 20L3.5 19L4.5 16.5Z" />
                </svg>
              </div>
              <div>
                <h3 className="text-lg font-semibold">AI Assistant</h3>
                <p className="text-xs text-gray-600">
                  Investment insights & advice
                </p>
              </div>
            </div>
            <button
              onClick={() => setShowAIChat(false)}
              className="p-2 rounded-lg transition-all duration-200 hover:scale-110 text-gray-600 hover:text-gray-800 hover:bg-gray-100"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          {/* Chat Messages Area */}
          <div className="flex-1 p-4 overflow-y-auto bg-gray-50/30">
            <div className="space-y-4">
              <div className="flex items-start gap-2">
                <div className="p-1.5 rounded-full bg-purple-500 flex-shrink-0 mt-1">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
                  </svg>
                </div>
                <div className="rounded-xl p-3 bg-white text-gray-800 shadow-sm">
                  <p className="text-sm">👋 Hi! I can help you with:</p>
                  <ul className="mt-2 space-y-1 text-xs">
                    <li>• Investment analysis</li>
                    <li>• Portfolio optimization</li>
                    <li>• Stock insights</li>
                    <li>• Risk management</li>
                  </ul>
                  <p className="mt-2 text-xs">What can I help you with?</p>
                </div>
              </div>
              <div className="grid grid-cols-1 gap-2">
                {[
                  "Analyze portfolio",
                  "Investment tips",
                  "Stock insights",
                  "Risk advice"
                ].map((suggestion) => (
                  <button
                    key={suggestion}
                    className="px-3 py-2 rounded-lg text-sm transition-all duration-200 hover:scale-[1.02] text-left bg-white hover:bg-gray-50 text-gray-700 border border-gray-200 shadow-sm"
                  >
                    {suggestion}
                  </button>
                ))}
              </div>
            </div>
          </div>
          {/* Chat Input */}
          <div className="p-4 border-t border-gray-200">
            <div className="space-y-3">
              <textarea
                placeholder="Ask about your investments..."
                className="w-full p-3 rounded-xl resize-none transition-all duration-200 text-sm bg-white border border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 focus:outline-none"
                rows={2}
              />
              <button className="w-full py-2.5 bg-purple-500 hover:bg-purple-600 text-white rounded-xl transition-all duration-200 hover:scale-[1.02] shadow-lg text-sm font-medium">
                Send Message
              </button>
            </div>
          </div>
        </div>
      </>
    )}
    </div>
  );
};

export default Investment;