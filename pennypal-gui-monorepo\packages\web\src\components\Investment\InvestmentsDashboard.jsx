import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { TrendingUp,TrendingDown,DollarSign,PieChart,Activity,RefreshCw,Calendar,Search,
  Filter,ArrowUpRight, ArrowDownRight, Eye,Wallet,Target,BarChart3,Clock,AlertCircle} from 'lucide-react';
import {syncInvestmentDataRequest,fetchUserInvestmentsRequest,fetchDailyInvestmentStocksRequest,
  fetchPortfolioSummaryRequest,fetchInvestmentByTickerRequest,resetInvestmentStatus,setFilters
} from '../../../../logic/redux/investmentsSlice';
import InvestmentsChart from './InvestmentsChart'; 
import BankIcon from '../../components/v1/components/account/BankIcon';
import PaymentLoader from '../load/PaymentLoader';

const InvestmentDashboard = () => {
  console.log("screen is investmentdashboard")
  const dispatch = useDispatch();
  const {userInvestments,dailyInvestmentStocks,portfolioSummary,
    investmentByTicker,loading,syncLoading,error,success,message,filters
  } = useSelector((state) => state.investment);

  // Local state for UI
  const [selectedTab, setSelectedTab] = useState('holdings');
  const [searchTicker, setSearchTicker] = useState('');
  const [dateFilter, setDateFilter] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [showAIChat, setShowAIChat] = useState(false);
  
  // FIXED: Enhanced state to track search results and search type
  const [searchState, setSearchState] = useState({
    investment: null,
    dailyStocks: [],
    hasSearched: false,
    searchType: null, // 'ticker', 'filter', or null
    searchParams: null // store what was searched for display
  });

  // Initialize dashboard data
  useEffect(() => {
    dispatch(fetchPortfolioSummaryRequest());
    dispatch(fetchUserInvestmentsRequest());
    dispatch(fetchDailyInvestmentStocksRequest());
  },[]);

  if (loading && (!userInvestments || !portfolioSummary || !dailyInvestmentStocks)) {
    return <PaymentLoader />;
  }

  // Handle sync
  const handleSync = () => {
    dispatch(syncInvestmentDataRequest());
  };

  // FIXED: Handle ticker search - only triggers on button click or Enter key
  const handleTickerSearch = () => {
    if (!searchTicker.trim()) {
      alert('Please enter a ticker symbol');
      return;
    }

    const ticker = searchTicker.toUpperCase();
    console.log('Explicitly searching for ticker:', ticker, 'with date:', dateFilter);
    
    // Reset any previous search state first
    dispatch(resetInvestmentStatus());
    
    // Set search state
    setSearchState({
      investment: null,
      dailyStocks: [],
      hasSearched: true,
      searchType: 'ticker',
      searchParams: { ticker, date: dateFilter || null }
    });

    // Search for investment data by ticker
    dispatch(fetchInvestmentByTickerRequest({ ticker }));
    
    // Search for daily stock data with ticker and optional date
    const filterParams = { ticker };
    if (dateFilter && /^\d{4}-\d{2}-\d{2}$/.test(dateFilter)) {
      filterParams.date = dateFilter;
    }
    
    dispatch(fetchDailyInvestmentStocksRequest(filterParams));
    
    // Switch to search tab to show results
    setSelectedTab('search');
  };

  // FIXED: Handle filter apply - only triggers on explicit button click
  const handleApplyFilters = () => {
    const filterParams = {};
    
    // Validate inputs
    if (dateFilter && /^\d{4}-\d{2}-\d{2}$/.test(dateFilter)) {
      filterParams.date = dateFilter;
    }
    if (searchTicker && searchTicker.trim().length > 0) {
      filterParams.ticker = searchTicker.trim().toUpperCase();
    }
    
    // Check if we have at least one filter
    if (Object.keys(filterParams).length === 0) {
      alert('Please enter at least one filter (date or ticker).');
      return;
    }
    
    console.log('Explicitly applying filters:', filterParams);
    
    // Reset any previous search state first
    dispatch(resetInvestmentStatus());
    
    // Set search state
    setSearchState({
      investment: null,
      dailyStocks: [],
      hasSearched: true,
      searchType: 'filter',
      searchParams: filterParams
    });
    
    // If ticker is provided, also search for investment data
    if (filterParams.ticker) {
      dispatch(fetchInvestmentByTickerRequest({ ticker: filterParams.ticker }));
    }
    
    // Update filters in Redux and fetch daily stocks
    dispatch(setFilters(filterParams));
    dispatch(fetchDailyInvestmentStocksRequest(filterParams));
    
    // Switch to search tab to show results
    setSelectedTab('search');
    setShowFilters(false);
  };

  // FIXED: Clear search results and reset to initial state
  const clearSearchResults = () => {
    dispatch(resetInvestmentStatus());
    setSearchTicker('');
    setDateFilter('');
    setSearchState({
      investment: null,
      dailyStocks: [],
      hasSearched: false,
      searchType: null,
      searchParams: null
    });
    // Clear filters in Redux
    dispatch(setFilters({}));
    // Reload all daily stocks only if we're on daily tab
    if (selectedTab === 'daily') {
      dispatch(fetchDailyInvestmentStocksRequest());
    }
  };

  // FIXED: Update search results when Redux state changes - only after explicit search
  useEffect(() => {
    if (searchState.hasSearched && searchState.searchType) {
      console.log('Updating search results:', {
        investmentByTicker,
        dailyInvestmentStocks: dailyInvestmentStocks?.length,
        searchType: searchState.searchType
      });
      
      setSearchState(prev => ({
        ...prev,
        investment: investmentByTicker,
        dailyStocks: Array.isArray(dailyInvestmentStocks) ? dailyInvestmentStocks : []
      }));
    }
  }, [investmentByTicker, dailyInvestmentStocks, searchState.hasSearched, searchState.searchType]);

  const getStockIcon = (ticker, companyName) => {
    if (!ticker && !companyName) return null;
    
    const displayName = companyName || ticker;
    
    return (
      <BankIcon
        institutionName={displayName.toLowerCase()}
        accountType="investment"
        sizeClass="md"
        size={50}
        className="rounded-xl"
        trackingId={`stock-${ticker || companyName}`}
        tickerSymbol={ticker}
      />
    );
  };

  // Format currency
  const formatCurrency = (amount) => {
    if (amount === null || amount === undefined) return '$0.00';
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value) => {
    if (value === null || value === undefined) return '0.00%';
    return `${value.toFixed(2)}%`;
  };

  // Get trend color
  const getTrendColor = (value) => {
    if (value === null || value === undefined) return 'text-slate-500';
    return value >= 0 ? 'text-green-500' : 'text-red-500';
  };

  // Get trend icon
  const getTrendIcon = (value) => {
    if (value === null || value === undefined) {
      return <Activity className="w-4 h-4 text-slate-500" />;
    }
    return value >= 0 ? 
      <ArrowUpRight className="w-4 h-4 text-green-500" /> : 
      <ArrowDownRight className="w-4 h-4 text-red-500" />;
  };

  // Safe access to portfolio summary
  const safePortfolioSummary = portfolioSummary || {};
  const safeUserInvestments = Array.isArray(userInvestments) ? userInvestments : [];
  
  // FIXED: For daily tab, use original data; for search tab, use search results only when searched
  const safeDailyStocks = selectedTab === 'search' && searchState.hasSearched && searchState.searchType
    ? searchState.dailyStocks 
    : selectedTab === 'daily'
    ? Array.isArray(dailyInvestmentStocks) ? dailyInvestmentStocks : []
    : [];

  // Debug logs
  console.log('Dashboard Render - userInvestments:', userInvestments);
  console.log('Dashboard Render - dailyInvestmentStocks:', dailyInvestmentStocks);
  console.log('Dashboard Render - portfolioSummary:', portfolioSummary);
  console.log('Dashboard Render - investmentByTicker:', investmentByTicker);
  console.log('Dashboard Render - searchState:', searchState);
  console.log('Dashboard Render - selectedTab:', selectedTab);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-8">
      <div>
        {/* Header */}
        <div className="p-6 mb-6">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold text-slate-800 flex items-center gap-3">
                <div className="w-10 h-10 [background-color:rgb(124,179,66)] rounded-[12px] flex items-center justify-center">
                  <BarChart3 className="w-6 h-6 text-white" />
                </div>
                Investment 
              </h1>
            </div>
            
            <div className="flex items-center gap-3">
              <button
                className="p-3 rounded-xl transition-all duration-200 hover:scale-110 bg-white border border-slate-200 shadow-lg"
                onClick={() => setShowAIChat(true)}
                title="AI Assistant"
              >
                <svg
                  className="w-6 h-6"
                  style={{ color: 'rgb(139, 195, 74)' }}
                  fill="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
                  <path d="M19.5 8.5L20.5 11L23 12L20.5 13L19.5 15.5L18.5 13L16 12L18.5 11L19.5 8.5Z" />
                  <path d="M4.5 16.5L5.5 19L8 20L5.5 21L4.5 23.5L3.5 21L1 20L3.5 19L4.5 16.5Z" />
                </svg>
              </button>
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 px-4 py-2 bg-slate-100 hover:bg-slate-200 rounded-xl transition-colors"
              >
                <Filter className="w-4 h-4" />
                Filters
              </button>
              
              <button
                onClick={handleSync}
                disabled={syncLoading}
                className="flex items-center gap-2 px-6 py-2 text-white [background-color:rgb(124,179,66)] rounded-xl transition-all shadow-lg disabled:opacity-50"
              >
                <RefreshCw className={`w-4 h-4 ${syncLoading ? 'animate-spin' : ''}`} />
                {syncLoading ? 'Syncing...' : 'Sync Data'}
              </button>
            </div>
          </div>

          {/* IMPROVED Filters Panel */}
          {showFilters && (
            <div className="mt-6 p-4 bg-slate-50 rounded-xl border border-slate-200">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Stock Ticker
                  </label>
                  <input
                    type="text"
                    value={searchTicker}
                    onChange={(e) => setSearchTicker(e.target.value)}
                    placeholder="e.g., AAPL, TSLA"
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Date Filter (Optional)
                  </label>
                  <input
                    type="date"
                    value={dateFilter}
                    onChange={(e) => setDateFilter(e.target.value)}
                    className="w-full px-3 py-2 border border-slate-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
                
                <div className="flex items-end gap-2">
                  <button
                    onClick={handleTickerSearch}
                    disabled={!searchTicker.trim()}
                    className="flex-1 px-4 py-2 text-white [background-color:rgb(124,179,66)] rounded-[12px] transition-colors disabled:opacity-50"
                  >
                    Search
                  </button>
                  <button
                    onClick={handleApplyFilters}
                    disabled={
                      (!dateFilter || !/^\d{4}-\d{2}-\d{2}$/.test(dateFilter)) &&
                      (!searchTicker || searchTicker.trim().length === 0)
                    }
                    className="flex-1 px-4 py-2 text-white bg-slate-600 hover:bg-slate-700 rounded-[12px] transition-colors disabled:opacity-50"
                  >
                    Apply Filters
                  </button>
                </div>
              </div>
              
              <div className="mt-3 text-sm text-slate-600">
                <strong>Search:</strong> Find investment + daily stock data for a ticker<br/>
                <strong>Apply Filters:</strong> Filter daily stocks by date and/or ticker
              </div>
            </div>
          )}
        </div>

        {/* Status Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6 flex items-center gap-3">
            <AlertCircle className="w-5 h-5 text-red-500" />
            <span className="text-red-700">{error}</span>
          </div>
        )}

        {/* Portfolio Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-2xl shadow-lg p-6 border border-slate-200 hover:shadow-xl transition-all">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center" style={{ backgroundColor: 'rgba(139, 195, 74, 0.125)' }}>
                <Wallet className="w-6 h-6 text-green-600" style={{ color: 'rgb(139, 195, 74)' }} />
              </div>
              {getTrendIcon(safePortfolioSummary.totalGainLoss)}
            </div>
            <h3 className="text-slate-600 text-sm font-medium">Total Value</h3>
            <p className="text-2xl font-bold text-slate-800">
              {formatCurrency(safePortfolioSummary.totalValue)}
            </p>
            <p className={`text-sm ${getTrendColor(safePortfolioSummary.totalGainLoss)}`}>
              {formatCurrency(safePortfolioSummary.totalGainLoss)} 
              ({formatPercentage(safePortfolioSummary.totalGainLossPercent)})
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-6 border border-slate-200 hover:shadow-xl transition-all">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center"  style={{ backgroundColor: 'rgba(139, 195, 74, 0.125)' }}>
                <Target className="w-6 h-6 text-blue-600" style={{ color: 'rgb(139, 195, 74)' }}/>
              </div>
              <TrendingUp className="w-5 h-5 text-blue-500" style={{ color: 'rgb(139, 195, 74)' }}/>
            </div>
            <h3 className="text-slate-600 text-sm font-medium">Total Cost Basis</h3>
            <p className="text-2xl font-bold text-slate-800">
              {formatCurrency(safePortfolioSummary.totalCostBasis)}
            </p>
            <p className="text-sm text-slate-500">Principal amount</p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-6 border border-slate-200 hover:shadow-xl transition-all">
            <div className="flex items-center justify-between mb-4">
              <div 
                className="w-12 h-12 rounded-xl flex items-center justify-center"
                style={{ backgroundColor: 'rgba(139, 195, 74, 0.125)' }}
              >
                <PieChart className="w-6 h-6" style={{ color: 'rgb(139, 195, 74)' }} />
              </div>
              <Activity className="w-5 h-5" style={{ color: 'rgb(139, 195, 74)' }} />
            </div>
            <h3 className="text-slate-600 text-sm font-medium">Total Positions</h3>
            <p className="text-2xl font-bold text-slate-800">{safePortfolioSummary.totalPositions || safeUserInvestments.length}</p>
            <p className="text-sm text-slate-500">Active investments</p>
          </div>

          <div className="bg-white rounded-2xl shadow-lg p-6 border border-slate-200 hover:shadow-xl transition-all">
            <div className="flex items-center justify-between mb-4">
              <div className="w-12 h-12 bg-orange-100 rounded-xl flex items-center justify-center" style={{ backgroundColor: 'rgba(139, 195, 74, 0.125)' }}>
                <Clock className="w-6 h-6 text-orange-600" style={{ color: 'rgb(139, 195, 74)' }} />
              </div>
              <Activity className="w-5 h-5 text-orange-500" style={{ color: 'rgb(139, 195, 74)' }} />
            </div>
            <h3 className="text-slate-600 text-sm font-medium">Daily Stocks</h3>
            <p className="text-2xl font-bold text-slate-800">
              {selectedTab === 'daily' ? (Array.isArray(dailyInvestmentStocks) ? dailyInvestmentStocks.length : 0) : safeDailyStocks.length}
            </p>
            <p className="text-sm text-slate-500">
              {selectedTab === 'search' && searchState.hasSearched ? 'Search results' : 'Today\'s tracked stocks'}
            </p>
          </div>
        </div>

        <div className="mb-8">
          <InvestmentsChart />
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-2xl shadow-lg mb-6 border border-slate-200">
          <div className="flex flex-wrap gap-1 p-2">
            {[
              { id: 'holdings', label: 'My Holdings', icon: Wallet },
              { id: 'daily', label: 'Daily Stocks', icon: Clock },
              { id: 'search', label: 'Stock Search', icon: Search }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-3 rounded-xl transition-all
                  ${selectedTab === tab.id
                    ? 'bg-[#7cb342] text-white'
                    : 'text-slate-600 hover:bg-slate-100'}`}
              >
                <tab.icon className="w-4 h-4" />
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Content Area */}
        <div className="bg-white rounded-2xl shadow-lg p-6 border border-slate-200">
          {selectedTab === 'holdings' && (
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold text-slate-800 flex items-center gap-2">
                  <Wallet className="w-5 h-5" style={{ color: 'rgb(139, 195, 74)' }} />
                  My Holdings
                </h2>
                <div className="text-sm text-slate-600 bg-slate-100 px-3 py-1 rounded-full">
                  {safeUserInvestments.length} {safeUserInvestments.length === 1 ? 'Position' : 'Positions'}
                </div>
              </div>
              
              {safeUserInvestments.length > 0 ? (
                <div className="space-y-4">
                  {/* Table Header */}
                  <div className="hidden lg:block">
                    <div className="grid grid-cols-6 gap-6 px-6 py-4 bg-slate-50 rounded-xl border border-slate-200">
                      <div className="text-lg font-bold text-slate-800 uppercase tracking-wide">
                        Ticker Name
                      </div>
                      <div className="text-lg font-bold text-slate-800 uppercase tracking-wide text-center">
                        Shares
                      </div>
                      <div className="text-lg font-bold text-slate-800 uppercase tracking-wide text-center">
                        Cost Basis
                      </div>
                      <div className="text-lg font-bold text-slate-800 uppercase tracking-wide text-center">
                        Current Price
                      </div>
                      <div className="text-lg font-bold text-slate-800 uppercase tracking-wide text-center">
                        Market Value
                      </div>
                      <div className="text-lg font-bold text-slate-800 uppercase tracking-wide text-center">
                        Gain/Loss
                      </div>
                    </div>
                  </div>

                  {/* Investment Rows */}
                  {safeUserInvestments.map((investment, index) => (
                    <div key={investment.id || index} className="p-6 border border-slate-200 rounded-xl hover:shadow-md transition-all bg-white">
                      <div className="flex flex-col lg:grid lg:grid-cols-6 lg:gap-6 lg:items-center gap-4">
                        {/* Stock Info Section */}
                        <div className="flex items-center gap-4 min-w-0">
                          {getStockIcon(
                            investment.ticker || investment.formattedTicker, 
                            investment.security_name || investment.securityName
                          )}
                          <div className="min-w-0 flex-1">
                            <h3 className="font-semibold text-slate-800 text-lg">
                              {investment.ticker || investment.formattedTicker || 'Unknown'}
                            </h3>
                            <p className="text-base text-slate-600 truncate">
                              {investment.security_name || investment.securityName || 'Company Name'}
                            </p>
                            <p className="text-sm text-slate-500">
                              {investment.securityTypeDisplayName || investment.security_type || 'Security Type'}
                            </p>
                          </div>
                        </div>
                        
                        {/* Metrics Grid */}
                        <div className="grid grid-cols-2 lg:contents gap-4 lg:gap-6">
                          <div className="text-center lg:text-center">
                            <div className="text-sm text-slate-500 uppercase tracking-wide font-medium mb-1 lg:hidden">Shares</div>
                            <div className="font-semibold text-slate-800 text-lg">{investment.quantity || 0}</div>
                          </div>
                          
                          <div className="text-center lg:text-center">
                            <div className="text-sm text-slate-500 uppercase tracking-wide font-medium mb-1 lg:hidden">Cost Basis</div>
                            <div className="font-semibold text-slate-800 text-lg">
                              {formatCurrency(investment.cost_basis || investment.costBasis)}
                            </div>
                          </div>
                          
                          <div className="text-center lg:text-center">
                            <div className="text-sm text-slate-500 uppercase tracking-wide font-medium mb-1 lg:hidden">Current Price</div>
                            <div className="font-semibold text-slate-800 text-lg">
                              {formatCurrency(investment.current_price || investment.currentPrice)}
                            </div>
                          </div>
                          
                          <div className="text-center lg:text-center">
                            <div className="text-sm text-slate-500 uppercase tracking-wide font-medium mb-1 lg:hidden">Market Value</div>
                            <div className="font-semibold text-slate-800 text-lg">
                              {formatCurrency(investment.value || investment.marketValue)}
                            </div>
                          </div>
                          
                          <div className="text-center lg:text-center col-span-2 lg:col-span-1">
                            <div className="text-sm text-slate-500 uppercase tracking-wide font-medium mb-1 lg:hidden">Gain/Loss</div>
                            <div className={`font-semibold text-base ${getTrendColor(investment.total_gain || investment.unrealizedGainLoss || investment.gainLoss)}`}>
                              {formatCurrency(investment.total_gain || investment.unrealizedGainLoss || investment.gainLoss)}
                              <div className="text-sm mt-0.5">
                                ({formatPercentage(investment.total_gain_percent || investment.unrealizedGainLossPercent || investment.gainLossPercent)})
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-16 text-slate-500">
                  <div className="w-20 h-20 mx-auto mb-4 rounded-full bg-slate-100 flex items-center justify-center">
                    <Wallet className="w-10 h-10 opacity-50" />
                  </div>
                  <p className="text-lg font-medium text-slate-600 mb-2">No holdings found</p>
                  <p className="text-slate-500 max-w-md mx-auto">
                    Your investment holdings will appear here once you sync your data with your brokerage account.
                  </p>
                  <button
                    onClick={handleSync}
                    className="mt-4 px-6 py-2 bg-[#7cb342] text-white rounded-xl hover:bg-[#689f38] transition-colors"
                  >
                    Sync Your Data
                  </button>
                </div>
              )}
            </div>
          )}

          {selectedTab === 'daily' && (
            <div>
              <h2 className="text-xl font-bold text-slate-800 mb-6 flex items-center gap-2">
                <Clock className="w-5 h-5" style={{ color: 'rgb(139, 195, 74)' }}  />
                Daily Investment Stocks ({Array.isArray(dailyInvestmentStocks) ? dailyInvestmentStocks.length : 0})
              </h2>
              
              {Array.isArray(dailyInvestmentStocks) && dailyInvestmentStocks.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-slate-200">
                        <th className="text-left py-3 px-4 font-semibold text-slate-700">Ticker</th>
                        <th className="text-left py-3 px-4 font-semibold text-slate-700">Date</th>
                        <th className="text-right py-3 px-4 font-semibold text-slate-700">Price</th>
                        <th className="text-right py-3 px-4 font-semibold text-slate-700">Change %</th>
                        <th className="text-right py-3 px-4 font-semibold text-slate-700">Volume</th>
                      </tr>
                    </thead>
                    <tbody>
                      {dailyInvestmentStocks.map((stock, index) => (
                        <tr key={stock.id || index} className="border-b border-slate-100 hover:bg-slate-50 transition-colors">
                          <td className="py-3 px-4">
                            <div className="flex items-center gap-3">
                              {getStockIcon(
                                stock.ticker, 
                                stock.security_name || stock.securityName || stock.companyName
                              )}
                              <span className="font-medium">{stock.ticker}</span>
                            </div>
                          </td>
                          <td className="py-3 px-4 text-slate-600">
                            {stock.timestamp
                              ? new Date(stock.timestamp).toLocaleDateString('en-US', { day: 'numeric', month: 'long', year: 'numeric' })
                              : stock.date
                                ? new Date(stock.date).toLocaleDateString('en-US', { day: 'numeric', month: 'long', year: 'numeric' })
                                : 'N/A'}
                          </td>
                          <td className="py-3 px-4 text-right font-medium">
                            {formatCurrency(stock.price || stock.close)}
                          </td>
                          <td className={`py-3 px-4 text-right font-medium ${getTrendColor(stock.percentChange || stock.change || stock.changePercent)}`}>
                            {(stock.percentChange !== null && stock.percentChange !== undefined) ? 
                              `${stock.percentChange > 0 ? '+' : ''}${stock.percentChange.toFixed(2)}%` :
                              (stock.change || stock.changePercent) ? 
                                `${(stock.change || stock.changePercent) > 0 ? '+' : ''}${(stock.change || stock.changePercent).toFixed(2)}%` : 
                                'N/A'
                            }
                          </td>
                          <td className="py-3 px-4 text-right text-slate-600">
                            {stock.quantity ? stock.quantity.toLocaleString() : 
                             stock.volume ? stock.volume.toLocaleString() : 'N/A'}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-12 text-slate-500">
                  <Clock className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">No daily stocks data</p>
                  <p>Daily stock information will appear here</p>
                </div>
              )}
            </div>
          )}

          {selectedTab === 'search' && (
            <div>
              <h2 className="text-xl font-bold text-slate-800 mb-6 flex items-center gap-2">
                <Search className="w-5 h-5" style={{ color: 'rgb(139, 195, 74)' }}  />
                Stock Search Results
              </h2>
              
              <div className="mb-6">
                <div className="flex gap-3 max-w-2xl">
                  <input
                    type="text"
                    value={searchTicker}
                    onChange={(e) => setSearchTicker(e.target.value)}
                    placeholder="Enter ticker symbol (e.g., AAPL, TSLA)"
                    className="flex-1 px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    onKeyPress={(e) => e.key === 'Enter' && handleTickerSearch()}
                  />
                  <input
                    type="date"
                    value={dateFilter}
                    onChange={(e) => setDateFilter(e.target.value)}
                    className="px-4 py-3 border border-slate-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                  <button
                    onClick={handleTickerSearch}
                    disabled={!searchTicker.trim()}
                    className="px-6 py-3 text-white rounded-[12px] transition-all shadow-lg hover:brightness-90 disabled:opacity-50"
                    style={{ backgroundColor: 'rgb(124, 179, 66)' }}
                  >
                    <Search className="w-5 h-5" />
                  </button>
                  {(searchState.hasSearched && searchState.searchType) && (
                    <button
                      onClick={clearSearchResults}
                      className="px-4 py-3 bg-slate-100 text-slate-600 rounded-xl hover:bg-slate-200 transition-colors"
                    >
                      Clear
                    </button>
                  )}
                </div>
                
                {/* FIXED: Display search context */}
                {searchState.hasSearched && searchState.searchParams && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="text-sm text-blue-800">
                      <strong>Search Results for:</strong> 
                      {searchState.searchParams.ticker && (
                        <span className="ml-2 px-2 py-1 bg-blue-100 rounded font-mono">
                          {searchState.searchParams.ticker}
                        </span>
                      )}
                      {searchState.searchParams.date && (
                        <span className="ml-2 px-2 py-1 bg-blue-100 rounded">
                          {new Date(searchState.searchParams.date).toLocaleDateString('en-US', { 
                            day: 'numeric', 
                            month: 'long', 
                            year: 'numeric' 
                          })}
                        </span>
                      )}
                      {searchState.searchType === 'ticker' && !searchState.searchParams.date && (
                        <span className="ml-2 text-xs italic">(All dates)</span>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Search Results */}
              {searchState.hasSearched && searchState.searchType ? (
                <div className="space-y-6">
                  {/* Investment Data Result */}
                  {searchState.investment && (
                    <div className="p-6 border border-slate-200 rounded-xl bg-gradient-to-r from-blue-50 to-green-50">
                      <h3 className="text-lg font-semibold text-slate-800 mb-4 flex items-center gap-2">
                        <Wallet className="w-5 h-5" style={{ color: 'rgb(139, 195, 74)' }} />
                        Investment Holdings for {searchState.investment.ticker}
                      </h3>
                      
                      <div className="flex items-center gap-4 mb-4">
                        {getStockIcon(searchState.investment.ticker, searchState.investment.securityName)}
                        <div>
                          <h4 className="text-xl font-bold text-slate-800">{searchState.investment.ticker}</h4>
                          <p className="text-slate-600">{searchState.investment.securityName || 'Company Name'}</p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div className="p-3 bg-white rounded-lg">
                          <div className="text-slate-600 text-sm">Shares</div>
                          <div className="text-xl font-semibold">{searchState.investment.quantity || 0}</div>
                        </div>
                        <div className="p-3 bg-white rounded-lg">
                          <div className="text-slate-600 text-sm">Cost Basis</div>
                          <div className="text-xl font-semibold">{formatCurrency(searchState.investment.costBasis)}</div>
                        </div>
                        <div className="p-3 bg-white rounded-lg">
                          <div className="text-slate-600 text-sm">Current Price</div>
                          <div className="text-xl font-semibold">{formatCurrency(searchState.investment.currentPrice)}</div>
                        </div>
                        <div className="p-3 bg-white rounded-lg">
                          <div className="text-slate-600 text-sm">Market Value</div>
                          <div className="text-xl font-semibold">{formatCurrency(searchState.investment.value)}</div>
                        </div>
                      </div>
                      
                      <div className="mt-4 p-3 bg-white rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-slate-600">Total Gain/Loss</span>
                          <div className="text-right">
                            <div className={`text-xl font-semibold ${getTrendColor(searchState.investment.totalGain)}`}>
                              {formatCurrency(searchState.investment.totalGain)}
                            </div>
                            <div className={`text-sm ${getTrendColor(searchState.investment.totalGainPercent)}`}>
                              ({formatPercentage(searchState.investment.totalGainPercent)})
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Daily Stock Data Results */}
                  {searchState.dailyStocks && searchState.dailyStocks.length > 0 && (
                    <div className="p-6 border border-slate-200 rounded-xl">
                      <h3 className="text-lg font-semibold text-slate-800 mb-4 flex items-center gap-2">
                        <Clock className="w-5 h-5" style={{ color: 'rgb(139, 195, 74)' }} />
                        Daily Stock Data
                        {searchState.searchParams?.ticker && ` for ${searchState.searchParams.ticker}`}
                        {searchState.searchParams?.date && ` on ${new Date(searchState.searchParams.date).toLocaleDateString('en-US', { day: 'numeric', month: 'long', year: 'numeric' })}`}
                        <span className="text-sm font-normal text-slate-500">({searchState.dailyStocks.length} records)</span>
                      </h3>
                      
                      {/* FIXED: Add date range summary for ticker-only searches */}
                      {searchState.searchParams?.ticker && !searchState.searchParams?.date && searchState.dailyStocks.length > 1 && (
                        <div className="mb-4 p-3 bg-slate-50 rounded-lg">
                          <div className="text-sm text-slate-600">
                            <strong>Date Range:</strong> {' '}
                            {(() => {
                              const dates = searchState.dailyStocks
                                .map(stock => new Date(stock.timestamp || stock.date))
                                .filter(date => !isNaN(date))
                                .sort((a, b) => a - b);
                              
                              if (dates.length > 0) {
                                const earliest = dates[0].toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
                                const latest = dates[dates.length - 1].toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
                                return `${earliest} to ${latest}`;
                              }
                              return 'N/A';
                            })()}
                          </div>
                        </div>
                      )}
                      
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b border-slate-200">
                              <th className="text-left py-3 px-4 font-semibold text-slate-700">Ticker</th>
                              <th className="text-left py-3 px-4 font-semibold text-slate-700">Date</th>
                              <th className="text-right py-3 px-4 font-semibold text-slate-700">Price</th>
                              <th className="text-right py-3 px-4 font-semibold text-slate-700">Value</th>
                              <th className="text-right py-3 px-4 font-semibold text-slate-700">Change %</th>
                              <th className="text-right py-3 px-4 font-semibold text-slate-700">Quantity</th>
                            </tr>
                          </thead>
                          <tbody>
                            {searchState.dailyStocks.map((stock, index) => (
                              <tr key={stock.id || index} className="border-b border-slate-100 hover:bg-slate-50 transition-colors">
                                <td className="py-3 px-4">
                                  <div className="flex items-center gap-3">
                                    {getStockIcon(stock.ticker, stock.security_name || stock.securityName)}
                                    <span className="font-medium">{stock.ticker}</span>
                                  </div>
                                </td>
                                <td className="py-3 px-4 text-slate-600">
                                  {stock.timestamp
                                    ? new Date(stock.timestamp).toLocaleDateString('en-US', { 
                                        day: 'numeric', 
                                        month: 'short', 
                                        year: 'numeric' 
                                      })
                                    : stock.date
                                      ? new Date(stock.date).toLocaleDateString('en-US', { 
                                          day: 'numeric', 
                                          month: 'short', 
                                          year: 'numeric' 
                                        })
                                      : 'N/A'}
                                </td>
                                <td className="py-3 px-4 text-right font-medium">
                                  {formatCurrency(stock.price || stock.close)}
                                </td>
                                <td className="py-3 px-4 text-right font-medium">
                                  {formatCurrency(stock.value)}
                                </td>
                                <td className={`py-3 px-4 text-right font-medium ${getTrendColor(stock.percentChange || stock.change || stock.changePercent)}`}>
                                  {(stock.percentChange !== null && stock.percentChange !== undefined) ? 
                                    `${stock.percentChange > 0 ? '+' : ''}${stock.percentChange.toFixed(2)}%` :
                                    (stock.change || stock.changePercent) ? 
                                      `${(stock.change || stock.changePercent) > 0 ? '+' : ''}${(stock.change || stock.changePercent).toFixed(2)}%` : 
                                      'N/A'
                                  }
                                </td>
                                <td className="py-3 px-4 text-right text-slate-600">
                                  {stock.quantity ? stock.quantity.toLocaleString() : 
                                   stock.volume ? stock.volume.toLocaleString() : 'N/A'}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}

                  {/* No Results Message */}
                  {searchState.hasSearched && !searchState.investment && (!searchState.dailyStocks || searchState.dailyStocks.length === 0) && (
                    <div className="text-center py-12 text-slate-500">
                      <Search className="w-16 h-16 mx-auto mb-4 opacity-50" />
                      <p className="text-lg font-medium">No results found</p>
                      <p>No investment or daily stock data found for:
                        {searchState.searchParams?.ticker && (
                          <span className="font-mono mx-1 px-2 py-1 bg-slate-100 rounded">
                            {searchState.searchParams.ticker}
                          </span>
                        )}
                        {searchState.searchParams?.date && (
                          <span className="mx-1">
                            on {new Date(searchState.searchParams.date).toLocaleDateString('en-US', { day: 'numeric', month: 'long', year: 'numeric' })}
                          </span>
                        )}
                      </p>
                      <p className="text-sm mt-2">
                        Try searching for a different ticker symbol or check if the stock is in your portfolio.
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12 text-slate-500">
                  <Search className="w-16 h-16 mx-auto mb-4 opacity-50" />
                  <p className="text-lg font-medium">Search for a stock</p>
                  <p>Enter a ticker symbol above to find investment holdings and daily stock data</p>
                  <div className="text-sm mt-4 text-slate-400 max-w-md mx-auto">
                    <p className="font-medium mb-2">Search Options:</p>
                    <p>• <strong>Ticker only:</strong> Shows all daily data for that stock</p>
                    <p>• <strong>Ticker + Date:</strong> Shows data for specific date only</p>
                    <p>• Always shows your investment holdings (if any)</p>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* AI Chat Modal */}
        {showAIChat && (
          <>
            <div
              className="fixed inset-0 bg-black/10 z-40"
              onClick={() => setShowAIChat(false)}
            />
            <div
              className="fixed top-0 right-0 h-full w-96 border-l shadow-2xl z-50 transform transition-transform duration-300 ease-in-out"
              style={{
                backgroundColor: "#fff",
                borderColor: "#e2e8f0"
              }}
            >
              <div className="flex justify-between items-center p-4 border-b border-gray-200">
                <div className="flex items-center gap-3">
                  <div className="p-2 rounded-lg bg-purple-500">
                    <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                      <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
                      <path d="M19.5 8.5L20.5 11L23 12L20.5 13L19.5 15.5L18.5 13L16 12L18.5 11L19.5 8.5Z" />
                      <path d="M4.5 16.5L5.5 19L8 20L5.5 21L4.5 23.5L3.5 21L1 20L3.5 19L4.5 16.5Z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">AI Assistant</h3>
                    <p className="text-xs text-gray-600">
                      Investment insights & advice
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setShowAIChat(false)}
                  className="p-2 rounded-lg transition-all duration-200 hover:scale-110 text-gray-600 hover:text-gray-800 hover:bg-gray-100"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="flex-1 p-4 overflow-y-auto bg-gray-50/30">
                <div className="space-y-4">
                  <div className="flex items-start gap-2">
                    <div className="p-1.5 rounded-full bg-purple-500 flex-shrink-0 mt-1">
                      <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
                      </svg>
                    </div>
                    <div className="rounded-xl p-3 bg-white text-gray-800 shadow-sm">
                      <p className="text-sm">👋 Hi! I can help you with:</p>
                      <ul className="mt-2 space-y-1 text-xs">
                        <li>• Investment analysis</li>
                        <li>• Portfolio optimization</li>
                        <li>• Stock insights</li>
                        <li>• Risk management</li>
                      </ul>
                      <p className="mt-2 text-xs">What can I help you with?</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 gap-2">
                    {[
                      "Analyze portfolio",
                      "Investment tips",
                      "Stock insights",
                      "Risk advice"
                    ].map((suggestion) => (
                      <button
                        key={suggestion}
                        className="px-3 py-2 rounded-lg text-sm transition-all duration-200 hover:scale-[1.02] text-left bg-white hover:bg-gray-50 text-gray-700 border border-gray-200 shadow-sm"
                      >
                        {suggestion}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
              <div className="p-4 border-t border-gray-200">
                <div className="space-y-3">
                  <textarea
                    placeholder="Ask about your investments..."
                    className="w-full p-3 rounded-xl resize-none transition-all duration-200 text-sm bg-white border border-gray-300 text-gray-900 placeholder-gray-500 focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 focus:outline-none"
                    rows={2}
                  />
                  <button className="w-full py-2.5 bg-purple-500 hover:bg-purple-600 text-white rounded-xl transition-all duration-200 hover:scale-[1.02] shadow-lg text-sm font-medium">
                    Send Message
                  </button>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default InvestmentDashboard;