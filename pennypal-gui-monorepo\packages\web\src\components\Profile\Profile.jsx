import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  User, 
  Settings, 
  Shield, 
  Star, 
  Camera, 
  Check, 
  AlertTriangle,
  MapPin
} from 'lucide-react';

// Import tab components
import UserProfile from './UserProfile';
import Security from './Security';
import PaymentLoader from '../load/PaymentLoader';

// Profile slice actions
import {
  fetchUserStart,
  selectUser,
  selectFetchLoading,
  selectFetchError,
  selectUpdateSuccess,
  selectUserLoaded,
  clearUpdateSuccess
} from '../../../../logic/redux/profileSlice';

// Two-factor auth actions
import {
  getTwoFactorStatusRequest,
} from '../../../../logic/redux/twoFactorAuthSlice';

// Rating actions
import {
  getUserRatingRequest,
  getRatingStatsRequest
} from '../../../../logic/redux/ratingSlice';

const Profile = () => {
  const dispatch = useDispatch();
  
  // Profile state
  const user = useSelector(selectUser);
  const fetchLoading = useSelector(selectFetchLoading);
  const fetchError = useSelector(selectFetchError);
  const updateSuccess = useSelector(selectUpdateSuccess);
  const userLoaded = useSelector(selectUserLoaded);
  
  // Rating state
  const ratingState = useSelector(state => state.rating);
  
  // Local state
  const [activeTab, setActiveTab] = useState('profile');
  const [dataInitialized, setDataInitialized] = useState(false);

  // Initialize data on component mount - FIXED: Only call once
  useEffect(() => {
    // Only fetch if user data hasn't been loaded yet
    if (!userLoaded && !fetchLoading && !dataInitialized) {
      console.log('Initializing profile data...');
      dispatch(fetchUserStart());
      dispatch(getTwoFactorStatusRequest());
      setDataInitialized(true);
    }
  }, [dispatch, userLoaded, fetchLoading, dataInitialized]);

  // FIXED: Separate useEffect for rating data that depends on user
  useEffect(() => {
    // Only fetch rating data when user is available and not already loading/loaded
    if (user && user.id && userLoaded) {
      console.log('Profile: Checking if rating data needs to be fetched for user:', user.id);
      
      // Always dispatch the requests - the epics will handle caching logic
      if (!ratingState.isLoadingUserRating) {
        console.log('Profile: Dispatching getUserRatingRequest');
        dispatch(getUserRatingRequest(user.id));
      }
      
      if (!ratingState.isLoadingStats) {
        console.log('Profile: Dispatching getRatingStatsRequest');
        dispatch(getRatingStatsRequest(user.id));
      }
    }
  }, [dispatch, user?.id, userLoaded]);

  // Clear success message after 3 seconds
  useEffect(() => {
    if (updateSuccess) {
      const timer = setTimeout(() => {
        dispatch(clearUpdateSuccess());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [updateSuccess, dispatch]);

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'security', label: 'Security', icon: Shield },
  ];

  // Render star rating display
  const renderStarRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative">
            <Star className="w-5 h-5 text-gray-300" />
            <Star className="w-5 h-5 text-yellow-400 fill-current absolute top-0 left-0" style={{clipPath: 'inset(0 50% 0 0)'}} />
          </div>
        );
      } else {
        stars.push(
          <Star key={i} className="w-5 h-5 text-gray-300" />
        );
      }
    }
    
    return stars;
  };

  if (fetchLoading) {
    return (
      <div className="flex items-center justify-center">
        <div className="flex items-center space-x-3">
          <PaymentLoader darkMode={false} />
          <span className="text-lg font-medium text-gray-700">Loading your profile...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="bg-white rounded-2xl shadow-xl overflow-hidden mb-8">
          <div className="h-32 " style={{backgroundColor: 'rgb(139, 195, 74)'}}></div>
          <div className="px-8 pb-8">
            <div className="flex items-center space-x-6 -mt-16">
              <div className="relative">
                <div className="w-24 h-24 bg-white rounded-full p-1 shadow-lg">
                  <div className="w-full h-full rounded-full flex items-center justify-center"style={{backgroundColor: 'rgb(139, 195, 74)'}}>
                    <User className="w-10 h-10 text-white" />
                  </div>
                </div>
                <button className="absolute -bottom-1 -right-1 w-8 h-8 rounded-full flex items-center justify-center shadow-lg hover:bg-green-600 transition-colors"style={{backgroundColor: 'rgb(139, 195, 74)'}}>
                  <Camera className="w-4 h-4 text-white" />
                </button>
              </div>
              <div className="flex-1 pt-16">
                <div className="flex items-center justify-between">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900">
                      {user?.name || 'Loading...'}
                    </h1>
                    
                    <div className="flex items-center space-x-4 mt-3">
                      {/* Star Rating Display */}
                      {ratingState.ratingStats && ratingState.ratingStats.averageRating > 0 && (
                        <div className="flex items-center space-x-2 bg-yellow-50 px-3 py-1 rounded-full border border-yellow-200">
                          <div className="flex items-center space-x-1">
                            {renderStarRating(ratingState.ratingStats.averageRating)}
                          </div>
                          <span className="text-sm font-semibold text-gray-700">
                            {ratingState.ratingStats.averageRating}/5
                          </span>
                          <span className="text-xs text-gray-500">
                            ({ratingState.ratingStats.totalRatings} reviews)
                          </span>
                        </div>
                      )}
                      
                      {/* User Rating Display */}
                      {ratingState.userRating && (
                        <div className="flex items-center space-x-2 bg-green-50 px-3 py-1 rounded-full border border-green-200">
                          <Star className="w-4 h-4 fill-green-600 text-green-600" />
                          <span className="text-sm font-semibold text-green-600">
                            Your Rating: {ratingState.userRating.rating}/5
                          </span>
                        </div>
                      )}
                      
                      {/* Address */}
                      {user?.address && (
                        <div className="flex items-center space-x-1 text-sm text-gray-500">
                          <MapPin className="w-4 h-4" />
                          <span>{user.address}</span>
                        </div>
                      )}
                    </div>

                    {/* Bio */}
                    {user?.bio && (
                      <p className="text-gray-600 mt-2 max-w-md">{user.bio}</p>
                    )}
                  </div>
                  
                  {updateSuccess && (
                    <div className="bg-green-50 text-green-600 px-4 py-2 rounded-lg flex items-center space-x-2 border border-green-200">
                      <Check className="w-4 h-4" />
                      <span>Profile updated successfully!</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-green-500 text-green-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.label}</span>
                  </button>
                );
              })}
            </nav>
          </div>

          <div className="p-8">
            {/* Profile Tab */}
            {activeTab === 'profile' && <UserProfile />}
            
            {/* Security Tab */}
            {activeTab === 'security' && <Security />}
          </div>
        </div>

        {/* Global Error Messages */}
        {fetchError && (
          <div className="fixed top-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 max-w-md z-50">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5" />
              <div>
                <h4 className="text-sm font-medium text-red-800">Error</h4>
                <p className="text-sm text-red-700 mt-1">{fetchError}</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Profile;