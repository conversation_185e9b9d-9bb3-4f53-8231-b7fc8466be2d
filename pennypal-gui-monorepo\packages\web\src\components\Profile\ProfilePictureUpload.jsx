// ProfilePictureUpload.js
import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  Camera, 
  Upload, 
  X, 
  Loader2, 
  User, 
  AlertTriangle,
  CheckCircle,
  Trash2
} from 'lucide-react';
import {
  uploadProfilePictureStart,
  fetchProfilePictureStart,
  deleteProfilePictureStart,
  clearUploadSuccess,
  clearDeleteSuccess,
  clearErrors,
  selectProfilePictureUrl,
  selectUploadLoading,
  selectUploadError,
  selectUploadSuccess,
  selectDeleteLoading,
  selectDeleteSuccess,
  selectFetchLoading,
} from '../../../../logic/redux/profilePictureSlice';

const API_URL = import.meta.env.VITE_API_URL;

const ProfilePictureUpload = ({ userId }) => {
  const dispatch = useDispatch();
  const fileInputRef = useRef(null);
  
  // Redux state
  const profilePictureUrl = useSelector(selectProfilePictureUrl);
  const uploadLoading = useSelector(selectUploadLoading);
  const uploadError = useSelector(selectUploadError);
  const uploadSuccess = useSelector(selectUploadSuccess);
  const deleteLoading = useSelector(selectDeleteLoading);
  const deleteSuccess = useSelector(selectDeleteSuccess);
  const fetchLoading = useSelector(selectFetchLoading);
  
  // Local state
  const [previewImage, setPreviewImage] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  // Fetch profile picture on component mount
  useEffect(() => {
    if (userId) {
      dispatch(fetchProfilePictureStart({ userId }));
    }
  }, [dispatch, userId]);

  // Clear success messages after 3 seconds
  useEffect(() => {
    if (uploadSuccess || deleteSuccess) {
      const timer = setTimeout(() => {
        dispatch(clearUploadSuccess());
        dispatch(clearDeleteSuccess());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [uploadSuccess, deleteSuccess, dispatch]);

  // Handle file selection
  const handleFileSelect = (file) => {
    if (!file) return;
    
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      alert('Please select a valid image file (JPEG, PNG, GIF, or WebP)');
      return;
    }
    
    // Validate file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      alert('File size must be less than 5MB');
      return;
    }
    
    setSelectedFile(file);
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewImage(e.target.result);
    };
    reader.readAsDataURL(file);
    
    setShowUploadModal(true);
  };

  // Handle file input change
  const handleFileInputChange = (e) => {
    const file = e.target.files[0];
    handleFileSelect(file);
  };

  // Handle drag and drop
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    const file = e.dataTransfer.files[0];
    handleFileSelect(file);
  };

  // Handle upload
  const handleUpload = () => {
    if (!selectedFile || !userId) return;
    
    dispatch(uploadProfilePictureStart({
      userId,
      file: selectedFile
    }));
    
    setShowUploadModal(false);
    setSelectedFile(null);
    setPreviewImage(null);
  };

  // Handle delete
  const handleDelete = () => {
    if (!userId) return;
    
    if (window.confirm('Are you sure you want to delete your profile picture?')) {
      dispatch(deleteProfilePictureStart({ userId }));
    }
  };

  // Handle modal close
  const handleModalClose = () => {
    setShowUploadModal(false);
    setSelectedFile(null);
    setPreviewImage(null);
    dispatch(clearErrors());
  };

  // Get profile picture URL with fallback
  const getProfilePictureUrl = () => {
    if (profilePictureUrl) {
      return profilePictureUrl.startsWith('http') 
        ? profilePictureUrl 
        : `${API_URL}/api/users/${userId}/profile-picture`;
    }
    return null;
  };

  return (
    <div className="relative">
      {/* Profile Picture Display */}
      <div className="relative group">
        <div className="w-24 h-24 bg-white rounded-full p-1 shadow-lg">
          {fetchLoading ? (
            <div className="w-full h-full bg-gray-200 rounded-full flex items-center justify-center">
              <Loader2 className="w-6 h-6 animate-spin text-gray-400" />
            </div>
          ) : getProfilePictureUrl() ? (
            <img
              src={getProfilePictureUrl()}
              alt="Profile"
              className="w-full h-full rounded-full object-cover"
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'flex';
              }}
            />
          ) : (
            <div className="w-full h-full bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full flex items-center justify-center">
              <User className="w-10 h-10 text-white" />
            </div>
          )}
        </div>
        
        {/* Upload Button */}
        <button
          onClick={() => fileInputRef.current?.click()}
          disabled={uploadLoading || deleteLoading}
          className="absolute -bottom-1 -right-1 w-8 h-8 bg-indigo-500 rounded-full flex items-center justify-center shadow-lg hover:bg-indigo-600 transition-colors disabled:opacity-50"
        >
          {uploadLoading ? (
            <Loader2 className="w-4 h-4 text-white animate-spin" />
          ) : (
            <Camera className="w-4 h-4 text-white" />
          )}
        </button>
        
        {/* Delete Button (shown when profile picture exists) */}
        {getProfilePictureUrl() && (
          <button
            onClick={handleDelete}
            disabled={deleteLoading || uploadLoading}
            className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center shadow-lg hover:bg-red-600 transition-colors disabled:opacity-50"
          >
            {deleteLoading ? (
              <Loader2 className="w-3 h-3 text-white animate-spin" />
            ) : (
              <Trash2 className="w-3 h-3 text-white" />
            )}
          </button>
        )}
      </div>
      
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInputChange}
        className="hidden"
      />
      
      {/* Upload Modal */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold">Upload Profile Picture</h3>
              <button
                onClick={handleModalClose}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>
            
            {/* Preview */}
            {previewImage && (
              <div className="mb-4">
                <img
                  src={previewImage}
                  alt="Preview"
                  className="w-32 h-32 rounded-full object-cover mx-auto border-4 border-gray-200"
                />
              </div>
            )}
            
            {/* Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-6 text-center mb-4 transition-colors ${
                dragActive
                  ? 'border-indigo-500 bg-indigo-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="w-8 h-8 mx-auto mb-2 text-gray-400" />
              <p className="text-sm text-gray-600 mb-2">
                Drag and drop an image, or{' '}
                <button
                  onClick={() => fileInputRef.current?.click()}
                  className="text-indigo-600 hover:text-indigo-500"
                >
                  browse
                </button>
              </p>
              <p className="text-xs text-gray-500">
                PNG, JPG, GIF up to 5MB
              </p>
            </div>
            
            {/* Error Display */}
            {uploadError && (
              <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center">
                  <AlertTriangle className="w-4 h-4 text-red-500 mr-2" />
                  <span className="text-sm text-red-700">{uploadError}</span>
                </div>
              </div>
            )}
            
            {/* Action Buttons */}
            <div className="flex space-x-3">
              <button
                onClick={handleModalClose}
                className="flex-1 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleUpload}
                disabled={!selectedFile || uploadLoading}
                className="flex-1 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {uploadLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    Uploading...
                  </>
                ) : (
                  'Upload'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Success Messages */}
      {uploadSuccess && (
        <div className="fixed top-4 right-4 bg-green-50 border border-green-200 rounded-lg p-4 max-w-md z-50">
          <div className="flex items-center">
            <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
            <span className="text-sm text-green-700">Profile picture updated successfully!</span>
          </div>
        </div>
      )}
      
      {deleteSuccess && (
        <div className="fixed top-4 right-4 bg-green-50 border border-green-200 rounded-lg p-4 max-w-md z-50">
          <div className="flex items-center">
            <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
            <span className="text-sm text-green-700">Profile picture deleted successfully!</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProfilePictureUpload;