import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  Shield,
  Lock,
  Trash2,
  Check,
  Eye,
  EyeOff,
  AlertTriangle,
  Loader2,
  Copy,
  QrCode,
  Key,
  X
} from 'lucide-react';

import {
  setupTwoFactorAuthRequest,
  enableTwoFactorAuthRequest,
  disableTwoFactorAuthRequest,
  verifyTwoFactorCodeRequest,
  getTwoFactorStatusRequest,
  resetTwoFactorAuthStatus,
  clearSetupData,
  clearVerificationStatus
} from '../../../../logic/redux/twoFactorAuthSlice';

import {
  changePassword,
  deleteAccountPermanently,
  clearChangePasswordState,
  clearDeleteAccountState
} from '../../../../logic/redux/accountManagementSlice';

import { getCurrentUserId } from '../../utils/AuthUtil';

const Security = () => {
  const dispatch = useDispatch();
  const userId = getCurrentUserId();

  const twoFactorState = useSelector(state => state.twoFactorAuth);
  const accountState = useSelector(state => state.accountManagement);

  const [showPasswordChange, setShowPasswordChange] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [show2FASetup, setShow2FASetup] = useState(false);
  const [show2FADisable, setShow2FADisable] = useState(false);
  const [verificationCode, setVerificationCode] = useState('');
  const [disableCode, setDisableCode] = useState('');
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
    delete: false
  });

  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    twoFactorCode: ''
  });

  const [deleteForm, setDeleteForm] = useState({
    password: '',
    confirmText: '',
    twoFactorCode: ''
  });

  // Get the current 2FA enabled status
  const is2FAEnabled = twoFactorState.twoFactorStatus?.isEnabled || twoFactorState.isEnabled || false;

  // Load 2FA status on component mount
  useEffect(() => {
    if (userId && userId !== '0') {
      dispatch(getTwoFactorStatusRequest());
    }
  }, [dispatch, userId]);

  // Handle success messages timeout for password change
  useEffect(() => {
    if (accountState.changePasswordSuccess) {
      const timer = setTimeout(() => {
        dispatch(clearChangePasswordState());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [accountState.changePasswordSuccess, dispatch]);

  // Handle success messages timeout for 2FA
  useEffect(() => {
    if (twoFactorState.success && !show2FASetup && !show2FADisable) {
      const timer = setTimeout(() => {
        dispatch(resetTwoFactorAuthStatus());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [twoFactorState.success, show2FASetup, show2FADisable, dispatch]);

  // Handle password change
  const handlePasswordChange = async (e) => {
    e.preventDefault();

    if (!userId || userId === '0') {
      alert('User ID is missing');
      return;
    }

    const { currentPassword, newPassword, confirmPassword, twoFactorCode } = passwordForm;

    if (!currentPassword || !newPassword || !confirmPassword) {
      alert('All fields are required');
      return;
    }

    if (newPassword !== confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    // Check if 2FA is enabled and code is required
    if (is2FAEnabled && !twoFactorCode) {
      alert('Two-factor authentication code is required');
      return;
    }

    try {
      dispatch(clearChangePasswordState());
      const payload = {
        userId: parseInt(userId),
        oldPassword: currentPassword.trim(),
        newPassword: newPassword.trim(),
        confirmPassword: confirmPassword.trim()
      };

      // Add 2FA code if provided
      if (twoFactorCode) {
        payload.twoFactorCode = twoFactorCode.trim();
      }

      await dispatch(changePassword(payload)).unwrap();

      setPasswordForm({ 
        currentPassword: '', 
        newPassword: '', 
        confirmPassword: '', 
        twoFactorCode: '' 
      });
      setShowPasswordChange(false);
    } catch (err) {
      console.error('Password update failed:', err);
    }
  };

  // Handle account deletion
  const handleDeleteAccount = async (e) => {
    e.preventDefault();

    if (!userId || userId === '0') return;

    if (deleteForm.confirmText !== 'DELETE') {
      alert('Type DELETE to confirm');
      return;
    }

    // Check if 2FA is enabled and code is required
    if (is2FAEnabled && !deleteForm.twoFactorCode) {
      alert('Two-factor authentication code is required');
      return;
    }

    try {
      const payload = {
        userId: parseInt(userId),
        password: deleteForm.password.trim(),
        confirmDeletion: true,
        reason: 'User requested deletion'
      };

      // Add 2FA code if provided
      if (deleteForm.twoFactorCode) {
        payload.twoFactorCode = deleteForm.twoFactorCode.trim();
      }

      await dispatch(deleteAccountPermanently(payload)).unwrap();

      setShowDeleteConfirm(false);
      setDeleteForm({ password: '', confirmText: '', twoFactorCode: '' });
    } catch (error) {
      console.error('Account deletion failed:', error);
    }
  };

  // Handle 2FA toggle button click
  const handleTwoFactorToggle = () => {
    if (is2FAEnabled) {
      // If 2FA is enabled, show disable modal
      setShow2FADisable(true);
      setDisableCode('');
    } else {
      // If 2FA is disabled, show setup modal
      setShow2FASetup(true);
      dispatch(setupTwoFactorAuthRequest());
    }
  };

  // Handle 2FA setup
  const handleSetup2FA = () => {
    if (verificationCode.trim().length === 6) {
      dispatch(enableTwoFactorAuthRequest({
        verificationCode: verificationCode.trim()
      }));
    } else {
      alert('Enter valid 6-digit code');
    }
  };

  // Handle 2FA disable
  const handleDisable2FA = () => {
    if (!disableCode || disableCode.trim().length !== 6) {
      alert('Please enter a valid 6-digit verification code');
      return;
    }

    dispatch(disableTwoFactorAuthRequest({
      verificationCode: disableCode.trim()
    }));
  };

  // Copy secret to clipboard
  const handleCopySecret = (secret) => {
    navigator.clipboard.writeText(secret).then(() => {
      alert('Secret copied to clipboard!');
    }).catch(() => {
      alert('Failed to copy secret');
    });
  };

  // Close setup modal
  const closeSetupModal = () => {
    setShow2FASetup(false);
    setVerificationCode('');
    dispatch(clearSetupData());
    dispatch(resetTwoFactorAuthStatus());
  };

  // Close disable modal
  const closeDisableModal = () => {
    setShow2FADisable(false);
    setDisableCode('');
    dispatch(resetTwoFactorAuthStatus());
  };

  // Auto-close setup modal on success
  useEffect(() => {
    if (twoFactorState.success && is2FAEnabled && show2FASetup) {
      setTimeout(() => {
        closeSetupModal();
        dispatch(getTwoFactorStatusRequest());
      }, 2000);
    }
  }, [twoFactorState.success, is2FAEnabled, show2FASetup, dispatch]);

  // Auto-close disable modal on success
  useEffect(() => {
    if (twoFactorState.success && !is2FAEnabled && show2FADisable) {
      setTimeout(() => {
        closeDisableModal();
        dispatch(getTwoFactorStatusRequest());
      }, 2000);
    }
  }, [twoFactorState.success, is2FAEnabled, show2FADisable, dispatch]);

  return (
    <div className="space-y-8">
      <h2 className="text-2xl font-bold text-gray-900">Security Settings</h2>

      {/* Success/Error Messages */}
      {accountState.changePasswordSuccess && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Check className="w-5 h-5 text-green-600" />
            <p className="text-green-800">Password updated successfully!</p>
          </div>
        </div>
      )}

      {accountState.changePasswordError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{accountState.changePasswordError}</p>
          </div>
        </div>
      )}

      {twoFactorState.success && !show2FASetup && !show2FADisable && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <Check className="w-5 h-5 text-green-600" />
            <p className="text-green-800">{twoFactorState.message}</p>
          </div>
        </div>
      )}

      {twoFactorState.error && !show2FASetup && !show2FADisable && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{twoFactorState.error}</p>
          </div>
        </div>
      )}

      {/* Password Change Section */}
      <div className="bg-gray-50 rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Password</h3>
            <p className="text-sm text-gray-600">Update your password to keep your account secure</p>
          </div>
          <button
            onClick={() => setShowPasswordChange(!showPasswordChange)}
            className="px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors"
          >
            Change Password
          </button>
        </div>

        {showPasswordChange && (
          <form onSubmit={handlePasswordChange} className="space-y-4 mt-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Current Password</label>
              <div className="relative">
                <input
                  type={showPasswords.current ? 'text' : 'password'}
                  value={passwordForm.currentPassword}
                  onChange={(e) => setPasswordForm({...passwordForm, currentPassword: e.target.value})}
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPasswords({...showPasswords, current: !showPasswords.current})}
                  className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                >
                  {showPasswords.current ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">New Password</label>
              <div className="relative">
                <input
                  type={showPasswords.new ? 'text' : 'password'}
                  value={passwordForm.newPassword}
                  onChange={(e) => setPasswordForm({...passwordForm, newPassword: e.target.value})}
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPasswords({...showPasswords, new: !showPasswords.new})}
                  className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                >
                  {showPasswords.new ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Confirm New Password</label>
              <div className="relative">
                <input
                  type={showPasswords.confirm ? 'text' : 'password'}
                  value={passwordForm.confirmPassword}
                  onChange={(e) => setPasswordForm({...passwordForm, confirmPassword: e.target.value})}
                  className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPasswords({...showPasswords, confirm: !showPasswords.confirm})}
                  className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                >
                  {showPasswords.confirm ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </button>
              </div>
            </div>

            {/* 2FA Code for Password Change */}
            {is2FAEnabled && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Two-Factor Authentication Code
                </label>
                <input
                  type="text"
                  value={passwordForm.twoFactorCode}
                  onChange={(e) => setPasswordForm({
                    ...passwordForm, 
                    twoFactorCode: e.target.value.replace(/\D/g, '').slice(0, 6)
                  })}
                  placeholder="123456"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-center text-lg tracking-widest"
                  maxLength="6"
                  required
                />
              </div>
            )}

            {passwordForm.newPassword && passwordForm.confirmPassword && 
             passwordForm.newPassword !== passwordForm.confirmPassword && (
              <div className="text-red-600 text-sm">
                Passwords do not match
              </div>
            )}

            <div className="flex items-center space-x-4">
              <button
                type="submit"
                disabled={
                  accountState.changePasswordLoading || 
                  passwordForm.newPassword !== passwordForm.confirmPassword ||
                  !passwordForm.currentPassword || 
                  !passwordForm.newPassword || 
                  !passwordForm.confirmPassword ||
                  (is2FAEnabled && !passwordForm.twoFactorCode)
                }
                className="flex items-center space-x-2 px-6 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors disabled:opacity-50"
              >
                {accountState.changePasswordLoading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Lock className="w-4 h-4" />
                )}
                <span>Update Password</span>
              </button>
              <button
                type="button"
                onClick={() => {
                  setShowPasswordChange(false);
                  setPasswordForm({ 
                    currentPassword: '', 
                    newPassword: '', 
                    confirmPassword: '', 
                    twoFactorCode: '' 
                  });
                  dispatch(clearChangePasswordState());
                }}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
            </div>
          </form>
        )}
      </div>

      {/* Two-Factor Authentication Section */}
      <div className="bg-gray-50 rounded-xl p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Two-Factor Authentication</h3>
            <p className="text-sm text-gray-600">
              Add an extra layer of security to your account
            </p>
            <div className="mt-2">
              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                is2FAEnabled 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {is2FAEnabled ? (
                  <>
                    <div className="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></div>
                    Enabled
                  </>
                ) : (
                  <>
                    <div className="w-1.5 h-1.5 bg-gray-400 rounded-full mr-1"></div>
                    Disabled
                  </>
                )}
              </span>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleTwoFactorToggle}
              disabled={twoFactorState.loading}
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50 ${
                is2FAEnabled ? 'bg-indigo-600' : 'bg-gray-200'
              }`}
            >
              {twoFactorState.loading && (
                <Loader2 className="absolute inset-0 w-4 h-4 m-auto animate-spin text-gray-600" />
              )}
              <span
                className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                  is2FAEnabled ? 'translate-x-6' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* 2FA Setup Modal */}
      {show2FASetup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-gray-900">Setup Two-Factor Authentication</h3>
              <button
                onClick={closeSetupModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {twoFactorState.setupLoading ? (
              <div className="text-center py-8">
                <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4" />
                <p className="text-gray-600">Setting up 2FA...</p>
              </div>
            ) : twoFactorState.setupData ? (
              <div className="space-y-6">
                {/* Step 1: Download App */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-blue-900 mb-2">Download an Authenticator App</h4>
                      <p className="text-blue-800 text-sm mb-3">Install one of these apps on your phone:</p>
                      <div className="grid grid-cols-1 gap-2 text-sm">
                        <div className="flex items-center space-x-2">
                          <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                          <span className="text-blue-800"><strong>Google Authenticator</strong> (iOS/Android)</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                          <span className="text-blue-800"><strong>Microsoft Authenticator</strong> (iOS/Android)</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                          <span className="text-blue-800"><strong>Authy</strong> (iOS/Android/Desktop)</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Step 2: Scan QR Code */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-green-900 mb-2">Scan this QR Code</h4>
                      <p className="text-green-800 text-sm mb-3">
                        Open your authenticator app and scan this code:
                      </p>
                      
                      <div className="text-center">
                        {twoFactorState.setupData.qrCode && (
                          <div className="mb-4">
                            <img 
                              src={twoFactorState.setupData.qrCode} 
                              alt="QR Code" 
                              className="mx-auto border-2 border-green-300 rounded-lg shadow-md"
                              style={{ maxWidth: '200px' }}
                            />
                          </div>
                        )}
                        
                        <div className="bg-white p-3 rounded-lg border mb-3">
                          <p className="text-xs text-gray-600 mb-2">Can't scan? Enter this code manually:</p>
                          <div className="flex items-center justify-center space-x-2">
                            <code className="text-sm bg-gray-100 px-2 py-1 rounded border break-all">
                              {twoFactorState.setupData.secret}
                            </code>
                            <button
                              onClick={() => handleCopySecret(twoFactorState.setupData.secret)}
                              className="p-1 text-gray-500 hover:text-gray-700 flex-shrink-0"
                              title="Copy secret"
                            >
                              <Copy className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Step 3: Enter Code */}
                <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-purple-900 mb-2">Enter Verification Code</h4>
                      <p className="text-purple-800 text-sm mb-3">
                        Enter the 6-digit code from your authenticator app:
                      </p>
                      
                      <input
                        type="text"
                        value={verificationCode}
                        onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                        placeholder="123456"
                        className="w-full px-4 py-3 border-2 border-purple-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent text-center text-xl tracking-widest font-mono"
                        maxLength="6"
                        autoFocus
                      />
                      
                      <div className="mt-2 text-xs text-purple-700">
                        💡 <strong>Tip:</strong> The code changes every 30 seconds. Enter it quickly!
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-4">
                  <button
                    onClick={handleSetup2FA}
                    disabled={twoFactorState.enableLoading || verificationCode.length !== 6}
                    className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {twoFactorState.enableLoading ? (
                      <Loader2 className="w-5 h-5 animate-spin" />
                    ) : (
                      <Shield className="w-5 h-5" />
                    )}
                    <span className="font-medium">Enable 2FA</span>
                  </button>
                  <button
                    onClick={closeSetupModal}
                    className="px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                </div>

                {/* Troubleshooting Tips */}
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h5 className="font-semibold text-yellow-900 mb-2 flex items-center">
                    <AlertTriangle className="w-4 h-4 mr-2" />
                    Troubleshooting Tips
                  </h5>
                  <ul className="text-sm text-yellow-800 space-y-1">
                    <li>• Make sure your phone's time is set correctly</li>
                    <li>• Try refreshing the code if it doesn't work</li>
                    <li>• Ensure good lighting when scanning the QR code</li>
                    <li>• If scanning fails, use manual entry instead</li>
                  </ul>
                </div>
              </div>
            ) : twoFactorState.error ? (
              <div className="text-center py-8">
                <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-red-500" />
                <h4 className="text-lg font-semibold text-red-900 mb-2">Setup Failed</h4>
                <p className="text-red-600 mb-4">{twoFactorState.error}</p>
                <button
                  onClick={closeSetupModal}
                  className="px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
                >
                  Close
                </button>
              </div>
            ) : null}
          </div>
        </div>
      )}

      {/* 2FA Disable Modal */}
      {show2FADisable && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Disable Two-Factor Authentication</h3>
              <button
                onClick={closeDisableModal}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              {/* Warning */}
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-yellow-900">Security Warning</h4>
                    <p className="text-yellow-800 text-sm mt-1">
                      Disabling 2FA will reduce your account security. You'll only need your password to log in.
                    </p>
                  </div>
                </div>
              </div>

              {/* Verification Code Input */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Enter verification code from your authenticator app
                </label>
                <input
                  type="text"
                  value={disableCode}
                  onChange={(e) => setDisableCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  placeholder="123456"
                  className="w-full px-3 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-center text-lg tracking-widest font-mono"
                  maxLength="6"
                  autoFocus
                />
                <p className="text-xs text-gray-500 mt-1">
                  Open your authenticator app and enter the current 6-digit code for PennyPal
                </p>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-4 pt-2">
                <button
                  onClick={handleDisable2FA}
                  disabled={twoFactorState.disableLoading || disableCode.length !== 6}
                  className="flex-1 flex items-center justify-center space-x-2 px-4 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {twoFactorState.disableLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <X className="w-4 h-4" />
                  )}
                  <span>Disable 2FA</span>
                </button>
                <button
                  onClick={closeDisableModal}
                  disabled={twoFactorState.disableLoading}
                  className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                >
                  Cancel
                </button>
              </div>

              {/* Additional Info */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <div className="w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center mt-0.5">
                    <span className="text-white text-xs">?</span>
                  </div>
                  <div className="text-sm text-blue-800">
                    <p className="font-medium">Can't access your authenticator app?</p>
                    <p>Contact support with your backup codes to disable 2FA.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Account Section */}
      <div className="bg-red-50 rounded-xl p-6 border border-red-200">
        <div className="flex items-start space-x-3">
          <AlertTriangle className="w-6 h-6 text-red-500 mt-0.5" />
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-red-900">Delete Account</h3>
            <p className="text-sm text-red-700 mt-1">
              Once you delete your account, there is no going back. Please be certain.
            </p>
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="mt-4 px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors"
            >
              Delete Account
            </button>
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 max-w-md w-full mx-4">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="w-6 h-6 text-red-500" />
              <h3 className="text-lg font-semibold text-gray-900">Confirm Account Deletion</h3>
            </div>
            <p className="text-gray-600 mb-4">
              This action cannot be undone. Type "DELETE" to confirm.
            </p>
            <form onSubmit={handleDeleteAccount} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Password</label>
                <div className="relative">
                  <input
                    type={showPasswords.delete ? 'text' : 'password'}
                    placeholder="Enter your password"
                    value={deleteForm.password}
                    onChange={(e) => setDeleteForm({...deleteForm, password: e.target.value})}
                    className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPasswords({...showPasswords, delete: !showPasswords.delete})}
                    className="absolute right-3 top-2.5 text-gray-400 hover:text-gray-600"
                  >
                    {showPasswords.delete ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                  </button>
                </div>
              </div>

              {/* 2FA Code for Account Deletion */}
              {is2FAEnabled && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Two-Factor Authentication Code
                  </label>
                  <input
                    type="text"
                    value={deleteForm.twoFactorCode}
                    onChange={(e) => setDeleteForm({
                      ...deleteForm, 
                      twoFactorCode: e.target.value.replace(/\D/g, '').slice(0, 6)
                    })}
                    placeholder="123456"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent text-center text-lg tracking-widest"
                    maxLength="6"
                    required
                  />
                </div>
              )}

              <input
                type="text"
                placeholder="Type DELETE to confirm"
                value={deleteForm.confirmText}
                onChange={(e) => setDeleteForm({...deleteForm, confirmText: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
                required
              />
              
              <div className="flex items-center space-x-4">
                <button
                  type="submit"
                  disabled={
                    accountState.deleteAccountLoading || 
                    deleteForm.confirmText !== 'DELETE' ||
                    !deleteForm.password ||
                    (is2FAEnabled && !deleteForm.twoFactorCode)
                  }
                  className="flex items-center space-x-2 px-6 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors disabled:opacity-50"
                >
                  {accountState.deleteAccountLoading ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <Trash2 className="w-4 h-4" />
                  )}
                  <span>Delete Account</span>
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setShowDeleteConfirm(false);
                    setDeleteForm({ password: '', confirmText: '', twoFactorCode: '' });
                  }}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Additional Error Messages */}
      {accountState.deleteAccountError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <p className="text-red-800">{accountState.deleteAccountError}</p>
          </div>
        </div>
      )}

      {/* Success Notification for 2FA Enable */}
      {twoFactorState.success && is2FAEnabled && !show2FASetup && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-3 rounded-lg shadow-lg">
          <div className="flex items-center space-x-2">
            <Check className="w-4 h-4" />
            <span>Two-factor authentication enabled</span>
          </div>
        </div>
      )}

      {/* Success Notification for 2FA Disable */}
      {twoFactorState.success && !is2FAEnabled && !show2FADisable && (
        <div className="fixed bottom-4 right-4 bg-orange-500 text-white px-4 py-3 rounded-lg shadow-lg">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="w-4 h-4" />
            <span>Two-factor authentication disabled</span>
          </div>
        </div>
      )}
    </div>
  );
};

export default Security;