import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { 
  User, 
  Star, 
  Edit3, 
  Check, 
  X, 
  Al<PERSON>Triangle,
  Loader2,
  StarOff,
  TrendingUp,
  MessageCircle
} from 'lucide-react';

// Profile slice actions
import {
  updateUserStart,
  clearFetchError,
  clearUpdateError,
  clearUpdateSuccess,
  selectUser,
  selectFetchLoading,
  selectFetchError,
  selectUserLoaded,
  selectUpdateLoading,
  selectUpdateError,
  selectUpdateSuccess
} from '../../../../logic/redux/profileSlice';

// Rating slice actions
import {
  submitRatingRequest,
  updateRatingRequest,
  getUserRatingRequest,
  getRatingStatsRequest,
  resetSubmitState,
  resetUpdateState,
  resetDeleteState
} from '../../../../logic/redux/ratingSlice';

// Interactive Star Rating Component
const InteractiveStarRating = ({ 
  currentRating = 0, 
  onRatingChange, 
  onCommentChange, 
  comment = '',
  isSubmitting = false,
  isEditing = false,
  showComment = true 
}) => {
  const [hoverRating, setHoverRating] = useState(0);
  const [localComment, setLocalComment] = useState(comment);

  useEffect(() => {
    setLocalComment(comment);
  }, [comment]);

  const handleStarClick = (rating) => {
    if (isEditing) {
      onRatingChange(rating);
    }
  };

  const handleCommentChange = (e) => {
    setLocalComment(e.target.value);
    onCommentChange(e.target.value);
  };

  const renderStar = (index) => {
    const rating = hoverRating || currentRating;
    const isFilled = index < rating;
    
    return (
      <button
        key={index}
        type="button"
        onClick={() => handleStarClick(index + 1)}
        onMouseEnter={() => isEditing && setHoverRating(index + 1)}
        onMouseLeave={() => setHoverRating(0)}
        className={`transition-all duration-200 ${isEditing ? 'hover:scale-110 cursor-pointer' : 'cursor-default'}`}
        disabled={isSubmitting || !isEditing}
      >
        <Star
          className={`w-8 h-8 ${
            isFilled 
              ? 'text-yellow-400 fill-yellow-400' 
              : 'text-gray-300 hover:text-yellow-200'
          } transition-colors duration-200`}
        />
      </button>
    );
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center space-x-2">
        <div className="flex space-x-1">
          {[...Array(5)].map((_, index) => renderStar(index))}
        </div>
        {currentRating > 0 && (
          <span className="text-lg font-semibold text-gray-700 ml-2">
            {currentRating}/5
          </span>
        )}
      </div>
      {showComment && isEditing && (
        <div className="mt-4">
          <textarea
            value={localComment}
            onChange={handleCommentChange}
            placeholder="Add a comment about your rating..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            rows={3}
            disabled={isSubmitting}
          />
        </div>
      )}
    </div>
  );
};

const UserProfile = () => {
  const dispatch = useDispatch();
  
  // Profile state
  const user = useSelector(selectUser);
  const fetchLoading = useSelector(selectFetchLoading);
  const fetchError = useSelector(selectFetchError);
  const userLoaded = useSelector(selectUserLoaded);
  const updateLoading = useSelector(selectUpdateLoading);
  const updateError = useSelector(selectUpdateError);
  const updateSuccess = useSelector(selectUpdateSuccess);
  
  // Rating state
  const ratingState = useSelector(state => state.rating);
  
  // Local state
  const [isEditing, setIsEditing] = useState(false);
  const [tempRating, setTempRating] = useState(0);
  const [tempComment, setTempComment] = useState('');
  const [originalRating, setOriginalRating] = useState(0);
  const [originalComment, setOriginalComment] = useState('');
  const [ratingDataInitialized, setRatingDataInitialized] = useState(false);
  
  // Profile form state
  const [profileForm, setProfileForm] = useState({
    name: '',
    email: '',
    phone: '',
    bio: '',
    address: '',
  });

  // Original profile data for reset
  const [originalProfile, setOriginalProfile] = useState({
    name: '',
    email: '',
    phone: '',
    bio: '',
    address: '',
  });

  // FIXED: Only fetch rating data when user is available and not already cached
  useEffect(() => {
    if (user && user.id && userLoaded && !ratingDataInitialized) {
      console.log('UserProfile: Initializing rating data for user:', user.id);
      
      // Always dispatch - the epics will handle caching logic
      dispatch(getUserRatingRequest(user.id));
      dispatch(getRatingStatsRequest(user.id));
      
      setRatingDataInitialized(true);
    }
  }, [dispatch, user?.id, userLoaded, ratingDataInitialized]);

  // Update form when user data loads
  useEffect(() => {
    if (user && userLoaded) {
      const profileData = {
        name: user.name || '',
        email: user.emailId || user.email || '',
        phone: user.phoneNumber || user.phone || '',
        bio: user.bio || '',
        address: user.address || '',
      };
      setProfileForm(profileData);
      setOriginalProfile(profileData);
    }
  }, [user, userLoaded]);

  // Initialize rating form when rating data loads
  useEffect(() => {
    if (ratingState.userRating) {
      const rating = ratingState.userRating.rating || 0;
      const comment = ratingState.userRating.comment || '';
      setTempRating(rating);
      setTempComment(comment);
      setOriginalRating(rating);
      setOriginalComment(comment);
    }
  }, [ratingState.userRating]);

  // Handle successful profile update
  useEffect(() => {
    if (updateSuccess) {
      setIsEditing(false);
      const timer = setTimeout(() => {
        dispatch(clearUpdateSuccess());
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [updateSuccess, dispatch]);

  // Handle successful rating submission/update
  useEffect(() => {
    if (ratingState.submitSuccess || ratingState.updateSuccess) {
      setIsEditing(false);
      
      // Update original values to current values
      setOriginalRating(tempRating);
      setOriginalComment(tempComment);
      
      // The rating data should already be updated in the store from the success action
      // No need to refetch unless there's a specific reason
      
      // Clear success states after a delay
      const timer = setTimeout(() => {
        dispatch(resetSubmitState());
        dispatch(resetUpdateState());
      }, 3000);
      
      return () => clearTimeout(timer);
    }
  }, [ratingState.submitSuccess, ratingState.updateSuccess, dispatch, tempRating, tempComment]);

  // Clear error message after 5 seconds
  useEffect(() => {
    if (updateError) {
      const timer = setTimeout(() => {
        dispatch(clearUpdateError());
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [updateError, dispatch]);

  // Clear fetch error after 5 seconds
  useEffect(() => {
    if (fetchError) {
      const timer = setTimeout(() => {
        dispatch(clearFetchError());
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [fetchError, dispatch]);

  // Check if profile data has changed
  const hasProfileChanged = () => {
    return (
      profileForm.name !== originalProfile.name ||
      profileForm.email !== originalProfile.email ||
      profileForm.phone !== originalProfile.phone ||
      profileForm.bio !== originalProfile.bio ||
      profileForm.address !== originalProfile.address
    );
  };

  // Check if rating data has changed
  const hasRatingChanged = () => {
    return (
      tempRating !== originalRating ||
      tempComment !== originalComment
    );
  };

  const handleSaveChanges = async (e) => {
    e.preventDefault();
    
    const profileChanged = hasProfileChanged();
    const ratingChanged = hasRatingChanged();
    
    // Only update profile if it has changed
    if (profileChanged) {
      const updatePayload = {
        name: profileForm.name,
        emailId: profileForm.email,
        phoneNumber: profileForm.phone,
        bio: profileForm.bio,
        address: profileForm.address,
      };
      dispatch(updateUserStart(updatePayload));
    }
    
    // Only update rating if it has changed and has a valid rating
    if (ratingChanged && tempRating > 0) {
      const ratingPayload = {
        rating: tempRating,
        comment: tempComment,
        userId: user.id
      };

      if (ratingState.userRating) {
        dispatch(updateRatingRequest(ratingPayload));
      } else {
        dispatch(submitRatingRequest(ratingPayload));
      }
    }
    
    // If no changes were made, just exit edit mode
    if (!profileChanged && !ratingChanged) {
      setIsEditing(false);
    }
  };

  const handleCancelEdit = () => {
    // Reset profile form to original values
    setProfileForm(originalProfile);
    
    // Reset rating to original values
    setTempRating(originalRating);
    setTempComment(originalComment);
    
    setIsEditing(false);
    dispatch(clearUpdateError());
  };

  const handleEditToggle = () => {
    if (isEditing) {
      handleCancelEdit();
    } else {
      setIsEditing(true);
    }
  };

  const handleRetryRating = () => {
    if (user && user.id) {
      dispatch(getUserRatingRequest(user.id));
      dispatch(getRatingStatsRequest(user.id));
    }
  };

  const handleRatingChange = (rating) => {
    setTempRating(rating);
  };

  const handleCommentChange = (comment) => {
    setTempComment(comment);
  };

  // Render star rating display (read-only)
  const renderStarRating = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;
    
    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative">
            <Star className="w-5 h-5 text-gray-300" />
            <Star className="w-5 h-5 text-yellow-400 fill-current absolute top-0 left-0" style={{clipPath: 'inset(0 50% 0 0)'}} />
          </div>
        );
      } else {
        stars.push(
          <Star key={i} className="w-5 h-5 text-gray-300" />
        );
      }
    }
    
    return stars;
  };

  // Show loading only when initially fetching user data
  if (fetchLoading && !userLoaded) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <Loader2 className="w-12 h-12 animate-spin text-green-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading profile...</p>
        </div>
      </div>
    );
  }

  // Show error state if fetch failed and no user data
  if (fetchError && !user) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <p className="text-red-600 mb-4">Failed to load profile</p>
          <p className="text-gray-600 mb-6">{fetchError}</p>
        </div>
      </div>
    );
  }

  const isLoading = updateLoading || ratingState.isSubmitting || ratingState.isUpdating;
  const hasAnyChanges = hasProfileChanged() || hasRatingChanged();

  return (
    <div className="space-y-6">
      {/* Header with unified Edit/Save button */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Personal Information</h2>
        <div className="flex space-x-2">
          {isEditing && (
            <button
              onClick={handleCancelEdit}
              disabled={isLoading}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <X className="w-4 h-4" />
              <span>Cancel</span>
            </button>
          )}
          <button
            onClick={isEditing ? handleSaveChanges : handleEditToggle}
            disabled={isLoading || (isEditing && !hasAnyChanges)}
            className="flex items-center space-x-2 px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? (
              <Loader2 className="w-4 h-4 animate-spin" />
            ) : isEditing ? (
              <Check className="w-4 h-4" />
            ) : (
              <Edit3 className="w-4 h-4" />
            )}
            <span>
              {isLoading ? 'Saving...' : isEditing ? 'Save Changes' : 'Edit'}
            </span>
          </button>
        </div>
      </div>

      {/* Profile Form */}
      <form onSubmit={handleSaveChanges} className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Name</label>
          <input
            type="text"
            value={profileForm.name}
            onChange={(e) => setProfileForm({...profileForm, name: e.target.value})}
            disabled={!isEditing || isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent disabled:bg-gray-50"
            placeholder="Enter your name"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
          <input
            type="email"
            value={profileForm.email}
            onChange={(e) => setProfileForm({...profileForm, email: e.target.value})}
            disabled={!isEditing || isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent disabled:bg-gray-50"
            placeholder="Enter your email"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
          <input
            type="tel"
            value={profileForm.phone}
            onChange={(e) => setProfileForm({...profileForm, phone: e.target.value})}
            disabled={!isEditing || isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent disabled:bg-gray-50"
            placeholder="Enter your phone number"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
          <input
            type="text"
            value={profileForm.address}
            onChange={(e) => setProfileForm({...profileForm, address: e.target.value})}
            disabled={!isEditing || isLoading}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent disabled:bg-gray-50"
            placeholder="Enter your address"
          />
        </div>

        <div className="md:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">Bio</label>
          <textarea
            value={profileForm.bio}
            onChange={(e) => setProfileForm({...profileForm, bio: e.target.value})}
            disabled={!isEditing || isLoading}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent disabled:bg-gray-50"
            placeholder="Tell us about yourself..."
          />
        </div>
      </form>

      {/* Ratings Section */}
      <div className="mt-8">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900">
            {ratingState.userRating ? 'Your Rating' : 'Give Rating'}
          </h3>
          {(ratingState.userRatingError || ratingState.statsError) && (
            <button
              onClick={handleRetryRating}
              className="px-3 py-1 text-sm bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              Retry
            </button>
          )}
        </div>

        {/* Error State */}
        {(ratingState.userRatingError || ratingState.statsError) && (
          <div className="text-center py-12">
            <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
            <p className="text-red-600 mb-2">Failed to load ratings</p>
            <p className="text-gray-600 text-sm">
              {ratingState.userRatingError || ratingState.statsError}
            </p>
          </div>
        )}

        {/* Rating Section */}
        {!ratingState.userRatingError && (
          <div className="mb-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              {ratingState.userRating && !isEditing ? (
                // Display existing rating (read-only)
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        {renderStarRating(ratingState.userRating.rating)}
                      </div>
                      <span className="text-lg font-semibold text-gray-900">
                        {ratingState.userRating.rating}/5
                      </span>
                    </div>
                    <div className="text-sm text-gray-500">
                      {ratingState.userRating.createdAt && 
                        new Date(ratingState.userRating.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                  {ratingState.userRating.comment && (
                    <div className="mt-3 p-3 bg-green-50 rounded border border-green-200">
                      <div className="flex items-start space-x-2">
                        <MessageCircle className="w-4 h-4 text-green-600 mt-0.5" />
                        <p className="text-gray-700">{ratingState.userRating.comment}</p>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                // Interactive rating component
                <div className="space-y-4">
                  <div className="text-center">
                    <h4 className="text-lg font-semibold text-gray-900 mb-2">
                      {ratingState.userRating ? 'Update Your Rating' : 'Rate Your Experience'}
                    </h4>
                    <p className="text-gray-600 text-sm mb-4">
                      {isEditing 
                        ? 'Click on stars to change your rating' 
                        : 'Click Edit to rate or update your rating'}
                    </p>
                  </div>
                  
                  <div className="flex justify-center">
                    <InteractiveStarRating
                      currentRating={tempRating}
                      onRatingChange={handleRatingChange}
                      onCommentChange={handleCommentChange}
                      comment={tempComment}
                      isSubmitting={isLoading}
                      isEditing={isEditing}
                      showComment={true}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Success Message */}
      {(updateSuccess || ratingState.submitSuccess || ratingState.updateSuccess) && (
        <div className="fixed top-4 right-4 bg-green-50 border border-green-200 rounded-lg p-4 max-w-md z-50">
          <div className="flex items-start space-x-3">
            <Check className="w-5 h-5 text-green-500 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-green-800">Success</h4>
              <p className="text-sm text-green-700 mt-1">
                {updateSuccess && (ratingState.submitSuccess || ratingState.updateSuccess)
                  ? 'Profile and rating updated successfully!'
                  : updateSuccess
                  ? 'Profile updated successfully!'
                  : `Rating ${ratingState.userRating ? 'updated' : 'submitted'} successfully!`}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Error Messages */}
      {(updateError || ratingState.submitError || ratingState.updateError) && (
        <div className="fixed top-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 max-w-md z-50">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-red-800">Error</h4>
              <p className="text-sm text-red-700 mt-1">
                {updateError || ratingState.submitError || ratingState.updateError}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Fetch Error */}
      {fetchError && user && (
        <div className="fixed top-4 right-4 bg-red-50 border border-red-200 rounded-lg p-4 max-w-md z-50">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-red-800">Fetch Error</h4>
              <p className="text-sm text-red-700 mt-1">{fetchError}</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserProfile;