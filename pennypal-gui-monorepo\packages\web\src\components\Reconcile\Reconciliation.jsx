import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { logEvent } from '../../utils/EventLogger';
import { CheckSquare, Square, Eye, EyeOff, Filter, ChevronDown, ChevronUp, Calendar, CreditCard, Trash2, RefreshCw, X, Search, DollarSign } from 'lucide-react';
import { fetchReconcileRequest, manualReconcileRequest, resetReconcileStatus } from '../../../../logic/redux/reconcileSlice';
import PaymentLoader from '../load/PaymentLoader';
import { themeClasses } from '../../utils/tailwindUtils';

function groupTransactions(transactions) {
  const groups = {};
  
  // First group by reconcileId if it exists
  transactions.forEach(transaction => {
    if (transaction.reconcileId) {
      if (!groups[transaction.reconcileId]) {
        groups[transaction.reconcileId] = [];
      }
      groups[transaction.reconcileId].push(transaction);
    }
  });
  
  // Then group remaining transactions by description and matching amounts
  const ungroupedTransactions = transactions.filter(t => !t.reconcileId);
  const amountMap = {};
  
  ungroupedTransactions.forEach(transaction => {
    const key = `${transaction.description}_${Math.abs(transaction.amount)}`;
    if (!amountMap[key]) {
      amountMap[key] = [];
    }
    amountMap[key].push(transaction);
  });
  
  // Create groups for matching amounts
  Object.values(amountMap).forEach(group => {
    if (group.length > 1) {
      // Check if we have both positive and negative amounts
      const hasCredit = group.some(t => t.amount > 0);
      const hasDebit = group.some(t => t.amount < 0);
      
      if (hasCredit && hasDebit) {
        // Create a new group with a generated reconcileId
        const reconcileId = `group_${group[0].description}_${Math.abs(group[0].amount)}`;
        group.forEach(t => {
          t.reconcileId = reconcileId; // This is just for UI grouping, won't affect backend
          if (!groups[reconcileId]) {
            groups[reconcileId] = [];
          }
          groups[reconcileId].push(t);
        });
      }
    }
  });
  
  // Sort groups by date (newest first)
  return Object.values(groups).sort((a, b) => {
    return new Date(b[0].transactionDate) - new Date(a[0].transactionDate);
  });
}

function FilterPanel({ darkMode, filters, onFiltersChange, onClose, transactions }) {
  const [localFilters, setLocalFilters] = useState(filters);
  
  const categories = [...new Set(transactions.map(t => t.category).filter(Boolean))].sort();
  const merchants = [...new Set(transactions.map(t => t.description).filter(Boolean))].sort();

  const handleFilterChange = (key, value) => {
    setLocalFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
    onClose();
  };

  const handleClearFilters = () => {
    const clearedFilters = {
      search: '',
      category: '',
      merchant: '',
      amountMin: '',
      amountMax: '',
      dateFrom: '',
      dateTo: '',
      amountType: 'all'
    };
    setLocalFilters(clearedFilters);
    onFiltersChange(clearedFilters);
  };

  return (
    <div className={`absolute top-full left-0 right-0 mt-2 p-6 rounded-xl shadow-lg border z-50 ${themeClasses.cardContainer(darkMode)} ${themeClasses.border(darkMode)}`}>
      <div className="flex justify-between items-center mb-4">
        <h3 className={`text-lg font-semibold ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>
          Filter Transactions
        </h3>
        <button
          onClick={onClose}
          className={`p-1 rounded-md transition-colors ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-100'}`}
        >
          <X size={16} className={`${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div className="col-span-full">
          <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Search Description
          </label>
          <div className="relative">
            <Search size={16} className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
            <input
              type="text"
              placeholder="Search transactions..."
              value={localFilters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className={`w-full pl-10 pr-4 py-2 border rounded-lg transition-colors ${
                darkMode 
                  ? 'bg-gray-700 border-gray-600 text-gray-200 placeholder-gray-400 focus:border-blue-500' 
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20`}
            />
          </div>
        </div>

        <div>
          <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Category
          </label>
          <select
            value={localFilters.category}
            onChange={(e) => handleFilterChange('category', e.target.value)}
            className={`w-full px-4 py-2 border rounded-lg transition-colors ${
              darkMode 
                ? 'bg-gray-700 border-gray-600 text-gray-200 focus:border-blue-500' 
                : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
            } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20`}
          >
            <option value="">All Categories</option>
            {categories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
        </div>

        <div>
          <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Merchant
          </label>
          <select
            value={localFilters.merchant}
            onChange={(e) => handleFilterChange('merchant', e.target.value)}
            className={`w-full px-4 py-2 border rounded-lg transition-colors ${
              darkMode 
                ? 'bg-gray-700 border-gray-600 text-gray-200 focus:border-blue-500' 
                : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
            } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20`}
          >
            <option value="">All Merchants</option>
            {merchants.map(merchant => (
              <option key={merchant} value={merchant}>{merchant}</option>
            ))}
          </select>
        </div>

        <div>
          <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Amount Type
          </label>
          <select
            value={localFilters.amountType}
            onChange={(e) => handleFilterChange('amountType', e.target.value)}
            className={`w-full px-4 py-2 border rounded-lg transition-colors ${
              darkMode 
                ? 'bg-gray-700 border-gray-600 text-gray-200 focus:border-blue-500' 
                : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
            } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20`}
          >
            <option value="all">All Amounts</option>
            <option value="positive">Credits Only</option>
            <option value="negative">Debits Only</option>
          </select>
        </div>

        <div>
          <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Min Amount
          </label>
          <div className="relative">
            <DollarSign size={16} className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
            <input
              type="number"
              placeholder="0.00"
              value={localFilters.amountMin}
              onChange={(e) => handleFilterChange('amountMin', e.target.value)}
              className={`w-full pl-10 pr-4 py-2 border rounded-lg transition-colors ${
                darkMode 
                  ? 'bg-gray-700 border-gray-600 text-gray-200 placeholder-gray-400 focus:border-blue-500' 
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20`}
            />
          </div>
        </div>

        <div>
          <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            Max Amount
          </label>
          <div className="relative">
            <DollarSign size={16} className={`absolute left-3 top-1/2 transform -translate-y-1/2 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`} />
            <input
              type="number"
              placeholder="0.00"
              value={localFilters.amountMax}
              onChange={(e) => handleFilterChange('amountMax', e.target.value)}
              className={`w-full pl-10 pr-4 py-2 border rounded-lg transition-colors ${
                darkMode 
                  ? 'bg-gray-700 border-gray-600 text-gray-200 placeholder-gray-400 focus:border-blue-500' 
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500 focus:border-blue-500'
              } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20`}
            />
          </div>
        </div>

        <div>
          <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            From Date
          </label>
          <input
            type="date"
            value={localFilters.dateFrom}
            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
            className={`w-full px-4 py-2 border rounded-lg transition-colors ${
              darkMode 
                ? 'bg-gray-700 border-gray-600 text-gray-200 focus:border-blue-500' 
                : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
            } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20`}
          />
        </div>

        <div>
          <label className={`block text-sm font-medium mb-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>
            To Date
          </label>
          <input
            type="date"
            value={localFilters.dateTo}
            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
            className={`w-full px-4 py-2 border rounded-lg transition-colors ${
              darkMode 
                ? 'bg-gray-700 border-gray-600 text-gray-200 focus:border-blue-500' 
                : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
            } focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20`}
          />
        </div>
      </div>

      <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
        <button
          onClick={handleClearFilters}
          className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
            darkMode 
              ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700' 
              : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
          }`}
        >
          Clear All Filters
        </button>
        <div className="flex space-x-3">
          <button
            onClick={onClose}
            className={`px-4 py-2 text-sm font-medium rounded-lg border transition-colors ${
              darkMode 
                ? 'border-gray-600 text-gray-300 hover:bg-gray-700' 
                : 'border-gray-300 text-gray-700 hover:bg-gray-50'
            }`}
          >
            Cancel
          </button>
          <button
            onClick={handleApplyFilters}
            className={`px-4 py-2 text-sm font-medium rounded-lg transition-colors ${themeClasses.reconcileButton(darkMode)}`}
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>
  );
}

export default function TransactionPage({ darkMode }) {
  useEffect(() => {
    logEvent('TransactionPage', 'PageLoad', {});
  }, []);
  return (
    <>
      <style jsx global>{`
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
      `}</style>
      <TransactionPageContent darkMode={darkMode} />
    </>
  );
}

function TransactionPageContent({ darkMode }) {
  const dispatch = useDispatch();
  const { transactions, loading, error, reconciling, status } = useSelector((state) => state.reconcile);
  const [selectedTransactions, setSelectedTransactions] = useState([]);
  const [expandedGroups, setExpandedGroups] = useState({});
  const [hiddenTransactions, setHiddenTransactions] = useState([]);
  const [showFilters, setShowFilters] = useState(false);
  const [showReconciled, setShowReconciled] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    merchant: '',
    amountMin: '',
    amountMax: '',
    dateFrom: '',
    dateTo: '',
    amountType: 'all'
  });

  useEffect(() => {
    dispatch(fetchReconcileRequest());
  }, [dispatch]);

  useEffect(() => {
    if (status === 'success') {
      dispatch(fetchReconcileRequest());
      setSelectedTransactions([]);
      alert('Transactions reconciled and excluded from budget.');
      dispatch(resetReconcileStatus());
    } else if (status === 'error') {
      alert(`Failed to reconcile transactions: ${error}`);
      dispatch(resetReconcileStatus());
    }
  }, [status, error, dispatch]);

  const filteredTransactions = transactions.filter(transaction => {
    // First filter by reconcile flag
    if (showReconciled) {
      if (transaction.reconcileFlag !== 'yes') return false;
    } else {
      if (transaction.reconcileFlag !== 'D') return false;
    }
    
    // Then apply other filters
    if (filters.search && !transaction.description.toLowerCase().includes(filters.search.toLowerCase())) {
      return false;
    }
    if (filters.category && transaction.category !== filters.category) {
      return false;
    }
    if (filters.merchant && transaction.description !== filters.merchant) {
      return false;
    }
    const absAmount = Math.abs(transaction.amount);
    if (filters.amountType === 'positive' && transaction.amount < 0) {
      return false;
    }
    if (filters.amountType === 'negative' && transaction.amount >= 0) {
      return false;
    }
    if (filters.amountMin && absAmount < parseFloat(filters.amountMin)) {
      return false;
    }
    if (filters.amountMax && absAmount > parseFloat(filters.amountMax)) {
      return false;
    }
    const transactionDate = new Date(transaction.transactionDate);
    if (filters.dateFrom && transactionDate < new Date(filters.dateFrom)) {
      return false;
    }
    if (filters.dateTo && transactionDate > new Date(filters.dateTo)) {
      return false;
    }
    return true;
  });

  const transactionGroups = groupTransactions(filteredTransactions);

  const activeFiltersCount = Object.values(filters).filter(value => value !== '' && value !== 'all').length;

  const toggleTransaction = (transactionId, e) => {
    e.stopPropagation(); // Prevent group-level event propagation
    logEvent('TransactionPage', 'ToggleTransaction', {
      transactionId,
      wasSelected: selectedTransactions.includes(transactionId),
    });
    console.log('Toggling transaction:', transactionId, 'Current selected:', selectedTransactions);
    setSelectedTransactions(prev =>
      prev.includes(transactionId)
        ? prev.filter(id => id !== transactionId)
        : [...prev, transactionId]
    );
  };

  const toggleGroup = (groupIndex) => {
    const isExpanding = !expandedGroups[groupIndex];
    logEvent('TransactionPage', isExpanding ? 'ExpandGroup' : 'CollapseGroup', {
      groupIndex,
      groupId: transactionGroups[groupIndex][0]?.reconcileId,
    });
    setExpandedGroups(prev => ({
      ...prev,
      [groupIndex]: !prev[groupIndex],
    }));
  };

  const toggleSelectAllInGroup = (groupTransactions, e) => {
    e.stopPropagation(); // Prevent group click from affecting other elements
    const groupIds = groupTransactions.map(t => t.transactionId);
    const allSelected = groupIds.every(id => selectedTransactions.includes(id));
    logEvent('TransactionPage', allSelected ? 'DeselectAllInGroup' : 'SelectAllInGroup', {
      groupId: groupTransactions[0]?.reconcileId,
      transactionCount: groupTransactions.length,
    });
    console.log('Toggling group:', groupTransactions[0]?.reconcileId, 'Group IDs:', groupIds, 'All selected:', allSelected);
    if (allSelected) {
      setSelectedTransactions(prev => prev.filter(id => !groupIds.includes(id)));
    } else {
      setSelectedTransactions(prev => [...new Set([...prev, ...groupIds])]);
    }
  };

  const hideTransactions = (groupTransactions, e) => {
    e.stopPropagation(); // Prevent group click propagation
    const groupIds = groupTransactions.map(t => t.transactionId);
    logEvent('TransactionPage', 'HideTransactions', {
      groupId: groupTransactions[0]?.reconcileId,
      transactionCount: groupIds.length,
    });
    console.log('Hiding transactions:', groupIds);
    setHiddenTransactions(prev => [...prev, ...groupIds]);
    setSelectedTransactions(prev => prev.filter(id => !groupIds.includes(id)));
  };

  const formatAmount = (amount) => {
    const absAmount = Math.abs(amount);
    return `${amount < 0 ? '-' : ''}$${absAmount.toFixed(2)}`;
  };

  const getAmountColor = (amount) => {
    return amount < 0 ? 'text-rose-600' : 'text-lime-600';
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const handleReconcile = () => {
    if (!showReconciled && selectedTransactions.length === 0) {
      alert('Please select at least one transaction to reconcile.');
      return;
    }
    
    if (showReconciled) {
      // Just refresh the data for reconciled view
      dispatch(fetchReconcileRequest());
      return;
    }

    const invalidSelections = selectedTransactions.filter(id => {
      const txn = filteredTransactions.find(t => t.transactionId === id);
      return !txn || txn.reconcileFlag !== 'D';
    });
    if (invalidSelections.length > 0) {
      alert(`Cannot reconcile: Selected transactions ${invalidSelections.join(', ')} do not have reconcileFlag 'D'`);
      return;
    }
    
    logEvent('TransactionPage', 'ReconcileNow', {
      transactionCount: selectedTransactions.length,
      transactionIds: selectedTransactions,
    });
    dispatch(manualReconcileRequest(selectedTransactions));
  };

  const handleFiltersChange = (newFilters) => {
    setFilters(newFilters);
    logEvent('TransactionPage', 'FiltersApplied', {
      activeFilters: Object.keys(newFilters).filter(key => newFilters[key] !== '' && newFilters[key] !== 'all'),
    });
  };

  const handleShowReconciled = () => {
    logEvent('TransactionPage', showReconciled ? 'ShowPendingReconciled' : 'ShowReconciled', {});
    setShowReconciled(!showReconciled);
    setSelectedTransactions([]);
    setHiddenTransactions([]);
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <PaymentLoader darkMode={darkMode} />
      </div>
    );
  }

  if (error && status !== 'error') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className={`border-l-4 p-6 rounded-md max-w-lg ${themeClasses.error(darkMode)}`}>
          <div className="flex items-center">
            <svg className={`h-6 w-6 ${themeClasses.errorText(darkMode)} mr-3`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <p className={`font-medium ${themeClasses.errorText(darkMode)}`}>Error: {error}</p>
          </div>
          <button
            className={`mt-4 px-4 py-2 rounded-md transition-colors ${themeClasses.actionButton(darkMode)}`}
            onClick={() => dispatch(fetchReconcileRequest())}
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`p-6 w-full min-h-screen ${themeClasses.container(darkMode)}`}>
      <div className="mb-8 flex justify-between items-center">
        <div>
          <h1 className={`flex text-2xl items-center ${themeClasses.container(darkMode)}`}>
            {showReconciled ? 'Reconciled Transactions' : 'Pending Reconciliation'}
          </h1>
          <p className={`mt-1 ${themeClasses.loadingText(darkMode)}`}>
            {showReconciled ? 'View your reconciled transactions' : 'Review and reconcile your transactions'}
          </p>
        </div>
        <div className="flex space-x-2">
          <button
            onClick={handleShowReconciled}
            className={`flex items-center px-4 py-2 rounded-lg text-sm transition-colors ${
              showReconciled 
                ? themeClasses.actionButton(darkMode) 
                : themeClasses.reconcileButton(darkMode)
            }`}
          >
            {showReconciled ? (
              <>
                <Eye size={16} className="mr-2" />
                Show Pending
              </>
            ) : (
              <>
                <CheckSquare size={16} className="mr-2" />
                Show Reconciled
              </>
            )}
          </button>
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${themeClasses.badgeDate(darkMode)}`}>
            <Calendar size={14} className="mr-1" />
            {new Date().toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
          </span>
          <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm ${themeClasses.badgeGroup(darkMode)}`}>
            <CreditCard size={14} className="mr-1" />
            {transactionGroups.length} Groups
          </span>
        </div>
      </div>

      <div className={`rounded-xl shadow-sm p-4 mb-6 border ${themeClasses.cardContainer(darkMode)} ${themeClasses.border(darkMode)} relative`}>
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-3">
            <button 
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center px-4 py-2 rounded-lg shadow-sm text-sm transition-colors ${
                showFilters || activeFiltersCount > 0 
                  ? themeClasses.reconcileButton(darkMode) 
                  : themeClasses.filterButton(darkMode)
              }`}
            >
              <Filter size={16} className="mr-2" />
              Filter
              {activeFiltersCount > 0 && (
                <span className={`ml-2 px-2 py-1 text-xs rounded-full ${
                  darkMode ? 'bg-white bg-opacity-20' : 'bg-black bg-opacity-10'
                }`}>
                  {activeFiltersCount}
                </span>
              )}
              {showFilters ? (
                <ChevronUp size={16} className="ml-2" />
              ) : (
                <ChevronDown size={16} className="ml-2" />
              )}
            </button>
            {selectedTransactions.length > 0 && (
              <span className={`text-sm px-3 py-1 rounded-lg font-medium border ${themeClasses.badgeSelected(darkMode)}`}>
                {selectedTransactions.length} selected
              </span>
            )}
          </div>
          <div className="flex items-center space-x-3">
            {selectedTransactions.length > 0 && (
              <button className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-colors border ${themeClasses.deleteButton(darkMode)}`}>
                <Trash2 size={16} className="mr-2" />
                Delete
              </button>
            )}
            <button
              className={`flex items-center px-4 py-2 rounded-lg text-sm transition-colors ${
                selectedTransactions.length > 0 ? themeClasses.reconcileButton(darkMode) : themeClasses.actionButtonDisabled(darkMode)
              }`}
              disabled={selectedTransactions.length === 0}
              onClick={() => {
                setHiddenTransactions(prev => [...prev, ...selectedTransactions]);
                setSelectedTransactions([]);
              }}
            >
              <EyeOff size={16} className="mr-2" />
              Hide Selected
            </button>
            <button
              onClick={handleReconcile}
              disabled={reconciling || (selectedTransactions.length === 0 && !showReconciled)}
              className={`inline-flex items-center px-4 py-2 rounded-lg text-sm transition-colors ${
                reconciling || (selectedTransactions.length === 0 && !showReconciled) 
                  ? themeClasses.reconcileButtonDisabled(darkMode) 
                  : themeClasses.reconcileButton(darkMode)
              }`}
            >
              {reconciling ? (
                <>
                  <RefreshCw size={14} className="mr-1 animate-spin" />
                  {showReconciled ? 'Updating...' : 'Reconciling...'}
                </>
              ) : (
                <>
                  <RefreshCw size={14} className="mr-1" />
                  {showReconciled ? 'Update Reconciled' : 'Reconcile Now'}
                </>
              )}
            </button>
          </div>
        </div>

        {showFilters && (
          <FilterPanel
            darkMode={darkMode}
            filters={filters}
            onFiltersChange={handleFiltersChange}
            onClose={() => setShowFilters(false)}
            transactions={transactions}
          />
        )}
      </div>

      <div className="space-y-5">
        {transactionGroups.map((group, groupIndex) => {
          if (group.every(t => hiddenTransactions.includes(t.transactionId))) {
            return null;
          }
          const expanded = expandedGroups[groupIndex] || false;
          const groupAmount = Math.abs(group[0].amount);
          const merchant = group[0].description;
          const groupDates = [...new Set(group.map(t => formatDate(t.transactionDate)))].join(" & ");
          const allSelected = group.every(t => selectedTransactions.includes(t.transactionId) || hiddenTransactions.includes(t.transactionId));
          const someSelected = group.some(t => selectedTransactions.includes(t.transactionId) && !hiddenTransactions.includes(t.transactionId)) && !allSelected;

          return (
            <div key={groupIndex} className={`border rounded-xl shadow-sm hover:shadow transition-shadow duration-200 ${themeClasses.cardContainer(darkMode)} ${themeClasses.border(darkMode)}`}>
              <div className={`flex justify-between items-center p-5 cursor-pointer rounded-t-xl ${darkMode ? 'hover:bg-gray-700' : 'hover:bg-gray-50'}`}
                onClick={() => toggleGroup(groupIndex)}>
                <div className="flex items-center">
                  <button
                    onClick={(e) => toggleSelectAllInGroup(group, e)}
                    className={`${themeClasses.icon(darkMode)} ${themeClasses.iconHover(darkMode)} transition-colors mr-4`}
                  >
                    {allSelected ? (
                      <CheckSquare size={22} className={`${darkMode ? 'text-lime-400' : 'text-lime-600'}`} />
                    ) : someSelected ? (
                      <Square size={22} className={`${darkMode ? 'text-lime-400' : 'text-lime-600'}`} strokeDasharray="2,2" />
                    ) : (
                      <Square size={22} />
                    )}
                  </button>
                  <div>
                    <h3 className={`font-semibold text-lg ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>{merchant}</h3>
                    <p className={`text-sm flex items-center mt-1 ${darkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      <Calendar size={14} className={`${darkMode ? 'text-gray-500' : 'text-gray-400'} mr-1`} />
                      {groupDates}
                      <>
                        <span className="mx-2"></span>
                        <span className={`text-xs py-1 px-2 rounded-full ${darkMode ? 'bg-purple-900 text-purple-300' : 'bg-purple-50 text-purple-700'}`}>
                          {group[0].category}
                        </span>
                      </>
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <span className={`font-semibold text-lg ${getAmountColor(group[0].amount)}`}>
                    {formatAmount(groupAmount)}
                  </span>
                  {!showReconciled && (
                    <button
                      onClick={(e) => hideTransactions(group, e)}
                      className={`px-3 py-1 text-sm rounded-md transition-colors ${darkMode ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                    >
                      <EyeOff size={14} className="inline mr-1" />
                      Hide
                    </button>
                  )}
                  {expanded ? (
                    <ChevronUp size={20} className={`${darkMode ? 'text-gray-500' : 'text-gray-400'}`} />
                  ) : (
                    <ChevronDown size={20} className={`${darkMode ? 'text-gray-500' : 'text-gray-400'}`} />
                  )}
                </div>
              </div>
              {expanded && (
                <div className={`border-t px-5 py-3 rounded-b-xl ${darkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-100'}`}>
                  {group.map(transaction => {
                    if (hiddenTransactions.includes(transaction.transactionId)) {
                      return null;
                    }
                    return (
                      <div key={transaction.transactionId} className={`flex justify-between items-center py-4 px-3 border-b last:border-0 rounded-lg my-1 ${darkMode ? 'border-gray-600 hover:bg-gray-600' : 'border-gray-100 hover:bg-white'}`}>
                        <div className="flex items-center">
                          {!showReconciled && (
                            <button
                              onClick={(e) => toggleTransaction(transaction.transactionId, e)}
                              className={`${darkMode ? 'text-gray-500 hover:text-lime-400' : 'text-gray-400 hover:text-lime-600'} transition-colors mr-4`}
                            >
                              {selectedTransactions.includes(transaction.transactionId) ? (
                                <CheckSquare size={18} className={`${darkMode ? 'text-lime-400' : 'text-lime-600'}`} />
                              ) : (
                                <Square size={18} />
                              )}
                            </button>
                          )}
                          <div>
                            <p className={`text-sm font-medium ${darkMode ? 'text-gray-200' : 'text-gray-800'}`}>{transaction.description}</p>
                            <div className="flex items-center mt-1">
                              <span className={`text-xs py-1 px-2 rounded-full font-medium ${themeClasses.badgeTransaction(darkMode)}`}>
                               ID: {transaction.transactionId} (Reconcile ID: {transaction.reconcileId})
                              </span>
                              {transaction.category && (
                                <span className={`text-xs py-1 px-2 rounded-full font-medium ml-2 ${themeClasses.badgeCategory(darkMode)}`}>
                                  {transaction.category}
                                </span>
                              )}
                              <span className={`text-xs py-1 px-2 rounded-full ${themeClasses.badgeDateTransaction(darkMode)}`}>
                                {formatDate(transaction.transactionDate)}
                              </span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center">
                          <span className={`text-sm font-medium ${transaction.amount < 0 ? themeClasses.amountCredit(darkMode) : themeClasses.amountDebit(darkMode)}`}>
                            {formatAmount(transaction.amount)}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}
      </div>
      {hiddenTransactions.length > 0 && (
        <div className="mt-8 text-center">
          <button
            className={`inline-flex items-center px-4 py-2 rounded-lg transition-colors ${themeClasses.actionButton(darkMode)}`}
            onClick={() => setHiddenTransactions([])}
          >
            <Eye size={16} className="mr-2" />
            Show hidden transactions ({hiddenTransactions.length})
          </button>
        </div>
      )}
    </div>
  );
}