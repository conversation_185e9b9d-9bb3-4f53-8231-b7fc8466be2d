
import { useMemo } from 'react';
import Tooltip from '@mui/material/Tooltip';
import Calendar from 'react-calendar';
import 'react-calendar/dist/Calendar.css';

const CalendarView = ({ transactions, colors, darkMode, fontClass }) => {
  // Log transactions to debug
  console.log('Transactions received:', transactions);

  // Group transactions by date and calculate credit/debit sums
  const { dailyTotals, dailyTransactions } = useMemo(() => {
    const totals = {};
    const transactionsByDate = {};

    (Array.isArray(transactions) ? transactions : []).forEach((tx) => {
      // Validate transactionDate
      if (!tx.transactionDate || isNaN(new Date(tx.transactionDate))) {
        console.warn('Invalid transaction date:', tx);
        return;
      }

      const date = new Date(tx.transactionDate).toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      });

      // Initialize accumulators
      if (!totals[date]) {
        totals[date] = { credits: 0, debits: 0 };
        transactionsByDate[date] = [];
      }

      // Calculate totals
      const amount = parseFloat(tx.transactionAmount) || 0;
      if (amount >= 0) {
        totals[date].credits += amount;
      } else {
        totals[date].debits += Math.abs(amount);
      }

      // Store transaction details
      transactionsByDate[date].push(tx);
    });

    return { dailyTotals: totals, dailyTransactions: transactionsByDate };
  }, [transactions]);

  // Custom tile content to show credit/debit sums with hover tooltip
  const tileContent = ({ date, view }) => {
    if (view !== 'month') return null;
    const dateStr = date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
    const totals = dailyTotals[dateStr];
    const transactionsForDate = dailyTransactions[dateStr] || [];

    if (!totals) return null;

    const tooltipContent = transactionsForDate.length > 0 ? (
      <div className={`p-4 rounded-xl shadow-2xl max-w-sm ${darkMode ? 'bg-gray-900 text-white border border-gray-700' : 'bg-white text-gray-800 border border-gray-200'}`}>
        <h4 className={`text-sm font-bold ${fontClass} mb-3 pb-2 border-b ${darkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          📅 {dateStr}
        </h4>
        <div className="space-y-2 max-h-48 overflow-y-auto">
          {transactionsForDate.map((tx, index) => (
            <div key={index} className={`p-2 rounded-lg ${darkMode ? 'bg-gray-800' : 'bg-gray-50'} hover:bg-opacity-80 transition-all`}>
              <div className="flex justify-between items-start">
                <span className="text-xs font-medium truncate max-w-[150px]" title={tx.description || 'No description'}>
                  {tx.description || 'No description'}
                </span>
                <span className={`text-xs font-bold ml-2 px-2 py-1 rounded-full ${
                  parseFloat(tx.transactionAmount) >= 0 
                    ? 'text-green-700 bg-green-100 dark:text-green-300 dark:bg-green-900' 
                    : 'text-red-700 bg-red-100 dark:text-red-300 dark:bg-red-900'
                }`}>
                  {parseFloat(tx.transactionAmount) >= 0 ? '+' : '-'}${Math.abs(parseFloat(tx.transactionAmount) || 0).toFixed(0)}
                </span>
              </div>
              {tx.category && (
                <span className={`text-xs ${colors.textSecondary} block mt-1 italic`}>
                  🏷️ {tx.category}
                </span>
              )}
            </div>
          ))}
        </div>
        <div className={`mt-3 pt-2 border-t ${darkMode ? 'border-gray-700' : 'border-gray-200'} text-xs ${colors.textSecondary}`}>
          {transactionsForDate.length} transaction{transactionsForDate.length !== 1 ? 's' : ''}
        </div>
      </div>
    ) : null;

    return (
      <Tooltip 
        title={tooltipContent || ''} 
        placement="top" 
        disableHoverListener={transactionsForDate.length === 0}
        arrow
        enterDelay={300}
        leaveDelay={200}
        componentsProps={{
          tooltip: {
            sx: {
              backgroundColor: 'transparent',
              padding: 0,
              maxWidth: 'none',
            },
          },
        }}
      >
        <div className="mt-1 space-y-1 w-full">
          {totals.credits > 0 && (
            <div className="text-xs font-bold text-green-800 bg-gradient-to-r from-green-100 via-green-200 to-green-100 dark:from-green-900 dark:via-green-800 dark:to-green-900 dark:text-green-200 px-2 py-1 rounded-lg shadow-sm border border-green-300 dark:border-green-600 transform hover:scale-105 transition-transform">
              +${totals.credits.toFixed(0)}
            </div>
          )}
          {totals.debits > 0 && (
            <div className="text-xs font-bold text-red-800 bg-gradient-to-r from-red-100 via-red-200 to-red-100 dark:from-red-900 dark:via-red-800 dark:to-red-900 dark:text-red-200 px-2 py-1 rounded-lg shadow-sm border border-red-300 dark:border-red-600 transform hover:scale-105 transition-transform">
              -${totals.debits.toFixed(0)}
            </div>
          )}
        </div>
      </Tooltip>
    );
  };

  // Custom tile className for styling dates with transactions
  const tileClassName = ({ date, view }) => {
    if (view !== 'month') return '';
    const dateStr = date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
    const isToday = date.toDateString() === new Date().toDateString();
    const hasTransactions = !!dailyTotals[dateStr];
    
    let className = 'relative flex items-center justify-center p-2 text-sm rounded-xl transition-all duration-300 ease-in-out';
    
    if (isToday) {
      className += ' bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900 dark:to-blue-800 font-semibold border-2 border-blue-300 dark:border-blue-600';
    } else if (hasTransactions) {
      className += ' bg-gradient-to-br from-purple-50 to-indigo-50 dark:from-purple-900 dark:to-indigo-900 border-2 border-purple-200 dark:border-purple-700';
    } else {
      className += ' border-2 border-gray-200 dark:border-gray-700';
    }
    
    className += ' hover:shadow-lg hover:shadow-blue-500/25 hover:border-blue-400 dark:hover:border-blue-500 hover:scale-105 hover:z-10';
    
    return className;
  };

  return (
    <div className={`${colors.cardBg} rounded-2xl border ${colors.border} ${colors.shadow} p-8 max-w-6xl mx-auto`}>
      <div className="mb-6">
        <h3 className={`text-xl font-bold ${colors.text} ${fontClass}`}>
          Transaction Calendar
        </h3>
        <p className={`text-sm ${colors.textSecondary} mt-1`}>
          View your transactions across the month • Hover over dates to see details
        </p>
      </div>

      {transactions.length === 0 ? (
        <div className="text-center py-12">
          <div className="text-4xl mb-4">📅</div>
          <p className={`text-lg ${colors.textSecondary} mb-2`}>No transactions available</p>
          <p className={`text-sm ${colors.textSecondary}`}>
            Your transactions will appear here once you have some data
          </p>
        </div>
      ) : (
        <div className="calendar-container w-full">
          <Calendar
            tileContent={tileContent}
            tileClassName={tileClassName}
            className={`
              enhanced-calendar w-full
              ${darkMode ? 'dark-theme' : 'light-theme'}
            `}
            locale="en-US"
            showNeighboringMonth={true}
            minDetail="month"
            maxDetail="month"
          />
        </div>
      )}

      <style jsx>{`
        .calendar-container {
          --calendar-bg: ${darkMode ? '#1f2937' : '#ffffff'};
          --calendar-text: ${darkMode ? '#f9fafb' : '#1f2937'};
          --calendar-border: ${darkMode ? '#374151' : '#e5e7eb'};
          --calendar-hover: ${darkMode ? '#374151' : '#f3f4f6'};
          --calendar-active: ${darkMode ? '#3b82f6' : '#2563eb'};
        }

        .enhanced-calendar {
          background: var(--calendar-bg) !important;
          border: 1px solid var(--calendar-border) !important;
          border-radius: 16px !important;
          padding: 24px !important;
          font-family: inherit !important;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
          width: 100% !important;
          max-width: none !important;
        }

        .enhanced-calendar .react-calendar__navigation {
          margin-bottom: 20px !important;
          height: 56px !important;
          display: flex !important;
          justify-content: space-between !important;
          align-items: center !important;
        }

        .enhanced-calendar .react-calendar__navigation button {
          background: transparent !important;
          border: 2px solid var(--calendar-border) !important;
          color: var(--calendar-text) !important;
          font-size: 16px !important;
          font-weight: 600 !important;
          padding: 12px 20px !important;
          border-radius: 12px !important;
          transition: all 0.2s ease !important;
          min-width: 48px !important;
          height: 48px !important;
        }

        .enhanced-calendar .react-calendar__navigation button:hover {
          background: var(--calendar-hover) !important;
          border-color: var(--calendar-active) !important;
          transform: translateY(-1px) !important;
        }

        .enhanced-calendar .react-calendar__navigation button:disabled {
          opacity: 0.5 !important;
        }

        .enhanced-calendar .react-calendar__month-view__weekdays {
          text-transform: uppercase !important;
          font-weight: 700 !important;
          font-size: 14px !important;
          color: var(--calendar-text) !important;
          margin-bottom: 12px !important;
          border-bottom: 2px solid var(--calendar-border) !important;
          padding-bottom: 12px !important;
        }

        .enhanced-calendar .react-calendar__month-view__weekdays__weekday {
          padding: 16px 8px !important;
          text-align: center !important;
          letter-spacing: 0.5px !important;
        }

        .enhanced-calendar .react-calendar__month-view__days {
          gap: 8px !important;
          display: grid !important;
          grid-template-columns: repeat(7, 1fr) !important;
        }

        .enhanced-calendar .react-calendar__tile {
          background: transparent !important;
          border: 2px solid var(--calendar-border) !important;
          border-radius: 12px !important;
          color: var(--calendar-text) !important;
          font-size: 16px !important;
          font-weight: 600 !important;
          height: 100px !important;
          padding: 12px 8px !important;
          transition: all 0.2s ease !important;
          display: flex !important;
          flex-direction: column !important;
          justify-content: flex-start !important;
          align-items: center !important;
          position: relative !important;
          overflow: visible !important;
          margin: 2px !important;
        }

        .enhanced-calendar .react-calendar__tile:hover {
          background: var(--calendar-hover) !important;
          transform: translateY(-3px) !important;
          box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
          border-color: var(--calendar-active) !important;
        }

        .enhanced-calendar .react-calendar__tile--active {
          background: var(--calendar-active) !important;
          color: white !important;
          border-color: var(--calendar-active) !important;
          transform: translateY(-2px) !important;
          box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3) !important;
        }

        .enhanced-calendar .react-calendar__tile--now {
          background: rgba(132, 204, 22, 0.2) !important;
          color: #84cc16 !important;
          font-weight: 800 !important;
          border: 3px solid rgba(132, 204, 22, 0.4) !important;
          box-shadow: 0 0 20px rgba(132, 204, 22, 0.3), 0 0 40px rgba(132, 204, 22, 0.2) !important;
          transform: scale(1.05) !important;
          position: relative !important;
          z-index: 10 !important;
          text-shadow: none !important;
        }

        .enhanced-calendar .react-calendar__tile--now .react-calendar__tile__label {
          color: #84cc16 !important;
          font-weight: 800 !important;
        }

        .enhanced-calendar .react-calendar__tile--now::before {
          content: '●' !important;
          position: absolute !important;
          top: 4px !important;
          right: 4px !important;
          color: #84cc16 !important;
          font-size: 14px !important;
          animation: pulse 2s infinite !important;
          text-shadow: none !important;
        }

        .enhanced-calendar .react-calendar__tile--now::after {
          content: 'TODAY' !important;
          position: absolute !important;
          bottom: 4px !important;
          left: 50% !important;
          transform: translateX(-50%) !important;
          font-size: 9px !important;
          font-weight: 700 !important;
          color: #84cc16 !important;
          letter-spacing: 0.5px !important;
          text-shadow: none !important;
          background: rgba(132, 204, 22, 0.1) !important;
          padding: 1px 4px !important;
          border-radius: 4px !important;
          border: 1px solid rgba(132, 204, 22, 0.3) !important;
        }

        @keyframes pulse {
          0%, 100% { opacity: 1; transform: scale(1); }
          50% { opacity: 0.7; transform: scale(1.2); }
        }

        .enhanced-calendar .react-calendar__tile.has-transactions {
          border-color: #3b82f6 !important;
          background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%) !important;
          box-shadow: 0 2px 10px rgba(59, 130, 246, 0.1) !important;
        }

        .enhanced-calendar.dark-theme .react-calendar__tile.has-transactions {
          border-color: #60a5fa !important;
          background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%) !important;
          box-shadow: 0 2px 10px rgba(96, 165, 250, 0.2) !important;
        }

        .enhanced-calendar .react-calendar__tile--neighboringMonth {
          opacity: 0.4 !important;
          color: var(--calendar-text) !important;
        }

        .enhanced-calendar .react-calendar__navigation__label {
          font-size: 20px !important;
          font-weight: 700 !important;
          pointer-events: none !important;
          color: var(--calendar-text) !important;
          flex-grow: 1 !important;
          text-align: center !important;
        }

        .enhanced-calendar .react-calendar__navigation__arrow {
          font-size: 20px !important;
          font-weight: bold !important;
        }

        /* Responsive adjustments */
        @media (max-width: 1024px) {
          .enhanced-calendar .react-calendar__tile {
            height: 85px !important;
            font-size: 15px !important;
          }
        }

        @media (max-width: 768px) {
          .enhanced-calendar .react-calendar__tile {
            height: 75px !important;
            font-size: 14px !important;
            padding: 8px 4px !important;
          }
          
          .enhanced-calendar {
            padding: 16px !important;
          }
          
          .enhanced-calendar .react-calendar__month-view__days {
            gap: 4px !important;
          }
        }

        @media (max-width: 480px) {
          .enhanced-calendar .react-calendar__tile {
            height: 65px !important;
            font-size: 12px !important;
            padding: 6px 2px !important;
          }
          
          .enhanced-calendar .react-calendar__navigation__label {
            font-size: 16px !important;
          }
          
          .enhanced-calendar .react-calendar__month-view__weekdays {
            font-size: 12px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default CalendarView;