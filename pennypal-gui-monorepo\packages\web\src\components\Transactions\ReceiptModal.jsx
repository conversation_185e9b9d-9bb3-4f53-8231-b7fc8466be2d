import React, { useState } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Tabs,
  Tab,
  Grid2,
  Paper,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Divider,
  Dialog
} from '@mui/material';
// import Grid2 from '@mui/material/Unstable_Grid2';

import ReceiptIcon from '@mui/icons-material/Receipt';
import ListAltIcon from '@mui/icons-material/ListAlt';
import CloseIcon from '@mui/icons-material/Close';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
import AccessTimeIcon from '@mui/icons-material/AccessTime';

const API_URL = import.meta.env.VITE_API_URL;

const ReceiptModal = ({ receiptData, onClose, handleFieldDoubleClick, isEmbedded }) => {
  const [view, setView] = useState('original');

  if (!receiptData) return null;

  const content = (
    <>
      <Box
        sx={{
          backgroundColor: '#8BC34A',
          padding: '16px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', justifyContent: 'center' }}>
          <ReceiptIcon sx={{ marginRight: '8px', color: '#333' }} />
          <Typography variant="h6" sx={{ color: '#333', fontWeight: 'bold' }}>
            {receiptData.merchantName || '-'}
          </Typography>
        </Box>
        <IconButton onClick={onClose} sx={{ color: '#333', position: 'absolute', right: '8px' }}>
          <CloseIcon />
        </IconButton>
      </Box>
      <Box sx={{ padding: '16px', overflow: 'auto' }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs
            value={view}
            onChange={(e, newValue) => setView(newValue)}
            aria-label="receipt tabs"
            sx={{ '& .MuiTabs-indicator': { backgroundColor: '#8BC34A' } }}
          >
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <ReceiptIcon sx={{ mr: 1, fontSize: '18px' }} />
                  <span>Original Receipt</span>
                </Box>
              }
              value="original"
              sx={{
                fontFamily: 'Architects Daughter, cursive',
                '&.Mui-selected': { color: '#8BC34A', fontWeight: 'bold' },
              }}
            />
            <Tab
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <ListAltIcon sx={{ mr: 1, fontSize: '18px' }} />
                  <span>Text Receipt</span>
                </Box>
              }
              value="response"
              sx={{
                fontFamily: 'Architects Daughter, cursive',
                '&.Mui-selected': { color: '#8BC34A', fontWeight: 'bold' },
              }}
            />
          </Tabs>
        </Box>
        {view === 'original' && (
          <Box sx={{ display: 'flex', mb: 3, justifyContent: 'center', alignItems: 'center', width: '100%' }}>
            {receiptData.savedFilePath ? (
              <img
                src={
                  receiptData.savedFilePath?.startsWith('/uploads')
                    ? `${API_URL}/pennypal${receiptData.savedFilePath}`
                    : `${API_URL}/pennypal/uploads/${receiptData.savedFilePath || ''}`
                }
                alt="Uploaded Receipt"
                style={{ maxWidth: '100%', maxHeight: '400px', display: 'block', margin: '0 auto' }}
                onError={(e) => {
                  e.target.onerror = null;
                  e.target.src = '/placeholder.png';
                }}
              />
            ) : (
              <Typography>No image available</Typography>
            )}
          </Box>
        )}
        {view === 'response' && (
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, width: '100%', alignItems: 'center' }}>
            <Box
              sx={{
                textAlign: 'center',
                marginBottom: '16px',
                padding: '16px',
                borderBottom: '1px solid #8BC34A',
                borderTop: '1px solid #8BC34A',
                backgroundColor: '#f6fbf3',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                width: '90%',
              }}
            >
              <Typography variant="h5" sx={{ fontWeight: 'bold', textAlign: 'center', width: '100%' }}>
                {receiptData.merchantName || ' '}
              </Typography>
              <Typography variant="body1" sx={{ textAlign: 'center', width: '100%' }}>
                {receiptData.merchantAddress || ' '}
              </Typography>
              <Typography variant="body2" sx={{ textAlign: 'center', width: '100%' }}>
                {receiptData.merchantPhno || ' '}
              </Typography>
            </Box>
            <Grid2 container spacing={2} sx={{ marginBottom: '24px', justifyContent: 'center', width: '100%' }}>
              <Grid2 item xs={6} sx={{ textAlign: 'center', display: 'flex', justifyContent: 'center' }}>
                <Paper
                  sx={{
                    p: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#f0f7e6',
                    width: '100%',
                    textAlign: 'center',
                  }}
                >
                  <CalendarTodayIcon sx={{ mr: 1, color: '#8BC34A' }} />
                  <Box
                    sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}
                    onDoubleClick={() => handleFieldDoubleClick('TransactionDate')}
                  >
                    <Typography variant="caption" color="text.secondary">
                      Date
                    </Typography>
                    <Typography variant="body1">{receiptData.transactionDate || '-'}</Typography>
                  </Box>
                </Paper>
              </Grid2>
              <Grid2 item xs={6} sx={{ textAlign: 'center', display: 'flex', justifyContent: 'center' }}>
                <Paper
                  sx={{
                    p: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#f0f7e6',
                    width: '100%',
                    textAlign: 'center',
                  }}
                >
                  <AccessTimeIcon sx={{ mr: 1, color: '#8BC34A' }} />
                  <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
                    <Typography variant="caption" color="text.secondary">
                      Time
                    </Typography>
                    <Typography variant="body1">{receiptData.insertTime || '-'}</Typography>
                  </Box>
                </Paper>
              </Grid2>
            </Grid2>
            <Paper elevation={3} sx={{ mb: 3, overflow: 'hidden', border: '1px solid #e0e0e0', borderRadius: '8px' }}>
              <Table sx={{ minWidth: '100%' }}>
                <TableHead>
                  <TableRow sx={{ backgroundColor: '#8BC34A' }}>
                    <TableCell sx={{ color: '#333', fontWeight: 'bold' }}>Item</TableCell>
                    <TableCell sx={{ color: '#333', fontWeight: 'bold' }}>Qty</TableCell>
                    <TableCell sx={{ color: '#333', fontWeight: 'bold' }}>Price</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {receiptData.items && receiptData.items.length > 0 ? (
                    receiptData.items.map((item, index) => (
                      <TableRow key={index} sx={{ '&:hover': { backgroundColor: '#f5f5f5' } }}>
                        <TableCell>{item.item}</TableCell>
                        <TableCell>{item.quantity}</TableCell>
                        <TableCell>${item.price}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={3} sx={{ textAlign: 'center', py: 2 }}>
                        No items found
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </Paper>
            <Paper sx={{ p: 2, backgroundColor: '#f9f9f9' }}>
              <Grid2 container>
                <Grid2 item xs={6}></Grid2>
                <Grid2 item xs={6}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body1">Subtotal:</Typography>
                    <Typography
                      variant="body1"
                      sx={{ cursor: 'pointer', textAlign: 'right', minWidth: '100px' }}
                    >
                      ${receiptData.transactionSubtotal || '0.00'}
                    </Typography>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="body1">Tax:</Typography>
                    <Typography
                      variant="body1"
                      sx={{ cursor: 'pointer', textAlign: 'right', minWidth: '100px' }}
                    >
                      ${receiptData.transTax || '0.00'}
                    </Typography>
                  </Box>
                  <Divider sx={{ my: 1 }} />
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body1" sx={{ fontWeight: 'bold' }}>
                      Total:
                    </Typography>
                    <Typography
                      variant="body1"
                      sx={{ fontWeight: 'bold', textAlign: 'right', minWidth: '400px' }}
                    >
                      ${receiptData.transTotal || '0.00'}
                    </Typography>
                  </Box>
                </Grid2>
              </Grid2>
            </Paper>
          </Box>
        )}
      </Box>
    </>
  );

  return isEmbedded ? (
    content
  ) : (
    <Dialog
      open={true}
      onClose={onClose}
      sx={{
        '& .MuiDialog-paper': {
          maxWidth: '600px',
          fontFamily: 'Architects Daughter, cursive',
          width: '90%',
          boxSizing: 'border-box',
          position: 'relative',
          overflow: 'hidden',
          backgroundColor: 'white',
          display: 'flex',
          flexDirection: 'column',
          padding: 0,
          clipPath: `polygon(
            0% 0%, 100% 0%, 100% 95%, 95% 98%, 90% 95%, 85% 98%, 80% 95%, 75% 98%, 
            70% 95%, 65% 98%, 60% 95%, 55% 98%, 50% 95%, 45% 98%, 40% 95%, 35% 98%, 
            30% 95%, 25% 98%, 20% 95%, 15% 98%, 10% 95%, 5% 98%, 0% 95%)`,
        },
      }}
    >
      {content}
    </Dialog>
  );
};

export default ReceiptModal;