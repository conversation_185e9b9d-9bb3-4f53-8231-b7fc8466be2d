//with receipt, split popup in trans dtails

import React, { useEffect, useRef,useState ,useCallback} from 'react';
import { useSelector, useDispatch ,} from 'react-redux';
import { DataGrid } from '@mui/x-data-grid';
import './TransactionPage.css';
import { useCacheAwareness } from '../../../logic/components/CacheAwareWrapper';

import CloseIcon from '@mui/icons-material/Close';
import {
  AddCircleOutline,
  VisibilityOff,
  FilterList,
  Upload,
  Receipt,
  Download
} from '@mui/icons-material';
import ReceiptModal from './ReceiptModal';
import SubCategoryIcon from './SubCategoryIcon';
import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import  PictureAsPdf from '@mui/icons-material/PictureAsPdf';
import AccountBalanceWalletIcon from '@mui/icons-material/AccountBalanceWallet';
import LocalOfferIcon from '@mui/icons-material/LocalOffer';
import NotesIcon from '@mui/icons-material/Notes';
import Visibility from '@mui/icons-material/Visibility';
import SaveIcon from '@mui/icons-material/Save';
import  AttachMoneyIcon  from '@mui/icons-material/AttachMoney';
import  AccountBalanceIcon  from '@mui/icons-material/AccountBalance';
import  CategoryIcon  from '@mui/icons-material/Category';
import  VisibilityOffIcon  from '@mui/icons-material/VisibilityOff';
import SplitTransactionPopup from './SplitTransactionPopup';
import { LocalizationProvider, DatePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import ReceiptIcon from '@mui/icons-material/Receipt';
import ReceiptLongIcon from '@mui/icons-material/ReceiptLong';
import DeleteIcon from '@mui/icons-material/Delete';
import CalendarTodayIcon from '@mui/icons-material/CalendarToday';
 import ReceiptHandler from './ReceiptHandler';
 import Grid2 from '@mui/material/Unstable_Grid2';

import CategoryChartModal from './CategoryChartModal';
import {
  Dialog, DialogActions, DialogContent, DialogTitle, Button, IconButton, InputAdornment,FormControl,InputLabel,
  LinearProgress, CircularProgress, TextField, Table, TableHead,Tooltip,  Select,Grid,Paper,ListSubheader ,
  TableBody, TableRow, TableCell, Tabs, Tab, MenuItem, Radio,Box,Typography,FormControlLabel,Checkbox
} from '@mui/material';
import { FaChevronDown } from 'react-icons/fa';
import { 
  FaMoneyBillWave, 
  FaArrowDown, 
  FaArrowUp, 
  FaDollarSign, 
  FaTrophy, 
  FaExclamationTriangle 
} from 'react-icons/fa';
import receipt from '../../assets/receipt.png'
import split from '../../assets/split 1.png'
import BankIcon from '../Accounts/BankIcon';
import { logEvent } from '../../utils/EventLogger';
import { getCurrentUserId } from '../../utils/AuthUtil';
import { fetchHiddenTransactionsStart } from '@pp-logic/redux/cacheSlice';
// Import actions from slices
import {
  fetchTransactionsStart,
  setSelectedTransaction,
  setOpenModal,
  toggleCardVisibility,
  setSearchName,
  setStartDate,
  setEndDate,
  setSearchDate,
  setSelectedDateRange,
  toggleDateFilter,
  setSortOrder,
  toggleSelectTransaction,
  toggleSelectAll,
  toggleAddTransactionModal,
  updateNewTransaction,
  resetNewTransaction,
  applyFilters,
  addTransactionRequest,
  fetchTransactionSummaryStart ,
  setCustomSearchText,
  selectAllTransactions,
  deselectAllTransactions,
  setSelectedCategory,
   setPage,
     fetchCategoryMonthlyExpensesStart, 
  setOpenCategoryModal ,
  setSelectedSubCategoryId,
  fetchAllIconsStart,
  clearSubCategoryIcon,
  
  // setSelectedCategoryId

} from '../../../../logic/redux/transactionSlice';

import { fetchAccountsRequest } from '../../../../logic/redux/transactionSlice';

import {
  setCurrentTab,
  setReceiptUploadModal,
  // setSelectedFile,
  setErrorMessage,
  setShowError,
  // setFileMetadata,
  setBlinkError,
  setIsReceiptModalOpen,
  setSelectedReceipt,
  // setSelectedTransaction,
  setEditingField,
  setEditedValue,
  setEditedItemIndex,
  setSelectedDate,
  setUploadProgress,
  setIsUploading,
  setIsProcessing,
  // setIsPopupVisible,
  // setReceiptNewTransaction,
  uploadReceiptRequest,
  saveReceiptRequest,
  addNewTransaction,
  fetchReceiptTransactionIdsRequest,
  fetchReceiptDetailsRequest,
  // addTransactionRequest,
  updateReceiptField,
  setJsonResponse, 
  setIsMatchingTransactionAdd,
  
} from '../../../../logic/redux/receiptSlice';


export const TransactionNameCell = ({ name, icon }) => {
  // Use the transaction name as fallback when icon prop is null
  const iconName = icon || name;

  return (
      <div className="bank-icon-with-text" style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px', // Consistent spacing between icon and text
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis'
      }}
      >
        <BankIcon
            institutionName={iconName}
            accountType="merchant"
            size={32} // Use numeric size instead of "sm"
            sizeClass="sm" // Add this prop for class-based sizing
            className="transaction-icon"
            style={{ flexShrink: 0 }}
        />
        <span style={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          flex: '1'
        }}>
        {name}
      </span>
      </div>
  );
};

// Component for account cell with bank icon
export const AccountCell = ({ bankName }) => {

  console.log("*************");
  // console.log("Inside AccountCell");
  return (
      <div className="bank-icon-with-text" style={{
        display: 'flex',
        alignItems: 'center',
        gap: '8px',
        whiteSpace: 'nowrap',
        overflow: 'hidden',
        textOverflow: 'ellipsis'
      }}>
        <BankIcon
            institutionName={bankName}
            size={12} // Use numeric size instead of "sm"
            sizeClass="sm" // Add this prop for class-based sizing
            className="account-icon"
            style={{ flexShrink: 0 }}
        />
        <span style={{
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          flex: '1'
        }}>
        {bankName}
      </span>
      </div>
  );
};

export const getTransactionColumns = (toggleTransactionModal, handleReceiptClick) => [
  {
    field: 'date',
    headerName: 'Date',
    flex: 1,
    minWidth: 120,
    headerClassName: 'data-grid-header',
    renderCell: (params) => {
      return params.row.isGroupHeader ? (
        <strong className="text-base">{params.value}</strong>
      ) : (
        params.value
      );
    },
  },
  {
    field: 'name',
    headerName: 'Transaction Name',
    flex: 2,
    minWidth: 180,
    headerClassName: 'data-grid-header',
    renderCell: (params) => {
      if (params.row.isGroupHeader) return params.value;

      return (
        <div
          className="cursor-pointer w-full"
          onClick={() => toggleTransactionModal(params.row)}
        >
          <TransactionNameCell
            name={params.value}
            icon={params.row.icon}
            onClick={() => toggleTransactionModal(params.row)}
          />
        </div>
      );
    },
  },
  {
    field: 'category',
    headerName: 'Category',
    flex: 1.5,
    minWidth: 150,
    headerClassName: 'data-grid-header'
  },
  {
    field: 'bank',
    headerName: 'Account',
    flex: 1.5,
    minWidth: 120,
    headerClassName: 'data-grid-header',
    renderCell: (params) => {
      if (params.row.isGroupHeader) return null;

      return <AccountCell bankName={params.value} />;
    }
  },
  {
    field: 'amount',
    headerName: 'Price',
    flex: 1,
    minWidth: 100,
    headerClassName: 'data-grid-header',
    type: 'number',
    renderCell: (params) => {
      return params.row.isGroupHeader ? (
        <strong>{params.value}</strong>
      ) : (
        params.value
      );
    },
  },
  {
    field: 'receipts',
    headerName: (
      <div className="flex items-center justify-center w-full">
        <span>Receipts</span>
      </div>
    ),
    flex: 1,
    minWidth: 120,
    headerClassName: 'data-grid-header',
    renderCell: (params) => {
      if (params.row.isGroupHeader) return null;
  
      return (
        <div className="flex items-center justify-center w-full h-full">
          <img
            src={receipt}
            alt="Receipt"
            className="w-5 h-5 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              handleReceiptClick(params.row.transaction_id);
            }}          />
          <img
            src={split}
            alt="Split Icon"
            className="w-5 h-5 ml-5 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              handleSplitIconClick(params.row);
            }}          />
        </div>
      );
    },
  }
];

const TransactionPage1 = ({ darkMode }) => {
  // Redux state selectors
  const dispatch = useDispatch();

    const [splitModalOpen, setSplitModalOpen] = useState(false);
      const [selectedSplitTransaction, setSelectedSplitTransaction] = useState(null);

  const handleSplitIconClick = useCallback((transaction) => {
    setSelectedSplitTransaction(transaction);
    setSplitModalOpen(true);
    // Close any other open modals if needed
    dispatch(setOpenModal(false));
  }, [dispatch]);

  const handleCloseSplitModal = useCallback(() => {
    setSplitModalOpen(false);
    setSelectedSplitTransaction(null);
  }, []);

  const handleSubmitSplit = (splitData) => {
    console.log("Split request submitted:", splitData);
    // The actual submission is now handled by the epic
  };
  // Transactions state
  const transactions = useSelector(state => state.transactions.transactions);
   const page = useSelector(state => state.transactions.page);
const pageSize = useSelector(state => state.transactions.pageSize);
const totalElements = useSelector(state => state.transactions.totalElements);
const totalPages = useSelector(state => state.transactions.totalPages);
  const [selectedCategoryId, setSelectedCategoryId] = useState(null);
const [rows, setRows] = useState([]);
const [showHidden, setShowHidden] = useState(false);
// Get cached data instead of using transaction slice data
const cache = useSelector(state => state.cache);
const summary = cache?.transactionSummaryLoaded ? cache.transactionSummary : useSelector(state => state.transactions.summary);
const hiddenTransactions = cache?.hiddenTransactionsLoaded ? cache.hiddenTransactions : useSelector(state => state.transactions.hiddenTransactions);
const accounts = useSelector((state) => state.transactions.accounts); // same slice
const loadingAccounts = useSelector((state) => state.transactions.loadingAccounts);
const categoryId = useSelector((state) => state.transactions.selectedCategoryId);
  const selectedTransaction = useSelector(state => state.transactions.selectedTransaction);
  const openModal = useSelector(state => state.transactions.openModal);
  const isCardVisible = useSelector(state => state.transactions.isCardVisible);
  const searchName = useSelector(state => state.transactions.searchName);
  const searchDate = useSelector(state => state.transactions.searchDate);
  const sortOrder = useSelector(state => state.transactions.sortOrder);
  const dateFilterOpen = useSelector(state => state.transactions.dateFilterOpen);
  const selectedDateRange = useSelector(state => state.transactions.selectedDateRange);
  const selectedTransactions = useSelector(state => state.transactions.selectedTransactions);
  const allChecked = useSelector(state => state.transactions.allChecked);
  const isAddTransactionOpen = useSelector(state => state.transactions.isAddTransactionOpen);
   const newTransaction = useSelector(state => state.transactions.newTransaction);
   const hideFromBudgetSuccess = useSelector(state => state.transactions.hideFromBudgetSuccess);
  const hideFromBudgetError = useSelector(state => state.transactions.hideFromBudgetError);
 const [expandedRows, setExpandedRows] = useState({});
 const [showEmbeddedReceiptUpload, setShowEmbeddedReceiptUpload] = useState(false);
  const reconciledTransactionsById = useSelector(state => state.transactions.reconciledTransactionsById);
  const [expandedTransactionIds, setExpandedTransactionIds] = useState([]);
const [customFilterOpen, setCustomFilterOpen] = useState(false);
const customText = useSelector((state) => state.transactions.customSearchText);
const handleHideTransaction = (transactionId) => {
  if (!transactionId) return;
  dispatch({ type: 'transactions/hideTransactions', payload: [transactionId] });
  closeTransactionModal();
};
const user = useSelector((state) => state.auth.user); 

useEffect(() => {
  if (user && user.id) {
    dispatch(fetchAccountsRequest({ userId: user.id }));
  }
}, [dispatch, user]);



 const subCategoryIcons = useSelector(state => state.transactions.subCategoryIcons || {});
  
  useEffect(() => {
     dispatch(fetchReceiptTransactionIdsRequest());
   }, [dispatch]);
   // Handle receipt click
   const handleReceiptClick = (transactionId) => {
     dispatch(fetchReceiptDetailsRequest(transactionId));
   };

 
   const handleFieldDoubleClick = (field) => {
     dispatch(setEditingField(field));
     dispatch(setEditedItemIndex(null));
     dispatch(setEditedValue(jsonResponse?.[field] || ''));
   };
 
   // Handle date change
   const handleDateChange = (date) => {
     dispatch(setSelectedDate(date));
     dispatch(setEditedValue(date.toISOString().split('T')[0]));
   };
//dropdown for export data
const [exportDropdownOpen, setExportDropdownOpen] = useState(false);
const exportDropdownRef = useRef(null);

useEffect(() => {
  const handleClickOutside = (event) => {
    if (exportDropdownRef.current && !exportDropdownRef.current.contains(event.target)) {
      setExportDropdownOpen(false);
    }
  };

  document.addEventListener('mousedown', handleClickOutside);
  return () => {
    document.removeEventListener('mousedown', handleClickOutside);
  };
}, []);

//pagination 

const CustomPagination = () => {
  const handlePageChange = (newPage) => {
    console.log('Changing page to:', newPage);
    dispatch(setPage(newPage));
  };

    const getVisiblePages = () => {
      const visiblePages = [];
      const maxVisiblePages = 5;

      if (totalPages <= maxVisiblePages) {
        for (let i = 0; i < totalPages; i++) {
          visiblePages.push(i);
        }
      } else {
        const startPage = Math.max(0, Math.min(page - 2, totalPages - maxVisiblePages));
        const endPage = Math.min(totalPages, startPage + maxVisiblePages);
        for (let i = startPage; i < endPage; i++) {
          visiblePages.push(i);
        }
      }
      return visiblePages;
    };

    if (totalPages <= 1) return null;

    return (
      <div className="flex items-center space-x-1">
        <button
          onClick={() => handlePageChange(page - 1)}
          disabled={page === 0}
          className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            page === 0
              ? 'text-gray-400 cursor-not-allowed'
              : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
          }`}
        >
          Previous
        </button>
        {getVisiblePages().map((pageNum) => (
          <button
            key={pageNum}
            onClick={() => handlePageChange(pageNum)}
            className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
              page === pageNum
                ? 'bg-lime-500 text-white shadow-sm'
                : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
            }`}
          >
            {pageNum + 1}
          </button>
        ))}
        <button
          onClick={() => handlePageChange(page + 1)}
          disabled={page === totalPages - 1}
          className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
            page === totalPages - 1
              ? 'text-gray-400 cursor-not-allowed'
              : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
          }`}
        >
          Next
        </button>
        <span className="text-sm text-gray-500 ml-4">
          Page {page + 1} of {totalPages} ({totalElements} total)
        </span>
      </div>
    );
  };


  // Receipts state
   const {
    errorMessage,
    jsonResponse,
    currentTab,
    receiptUploadModal,
    showError,
    blinkError,
    isReceiptModalOpen,
    selectedReceipt,
    // selectedFile,
    fileMetadata,
    // selectedTransaction,
    uploadPopupWidth,
    editingField,
    editedValue,
    editedItemIndex,
    selectedDate,
    uploadProgress,
    isUploading,
    isProcessing,
    isPopupVisible,
    
    //  receiptNewTransaction,
    receiptTransactionIds,
  } = useSelector((state) => state.receipts);
  const receiptNewTransaction = useSelector((state) => state.receipts.receiptNewTransaction);

const { addTransactionSuccess, addTransactionError } = useSelector((state) => state.receipts);
useEffect(() => {
  if (addTransactionSuccess) {
    alert('Transaction added successfully!');
    dispatch({
      type: 'receipts/resetAddTransactionStatus', // Add a new action to reset status
      payload: { addTransactionSuccess: false, addTransactionError: null },
    });
  }
}, [addTransactionSuccess, dispatch]);

useEffect(() => {
  if (addTransactionError) {
    alert(`Error adding transaction: ${addTransactionError}`);
    dispatch({
      type: 'receipts/resetAddTransactionStatus',
      payload: { addTransactionSuccess: false, addTransactionError: null },
    });
  }
}, [addTransactionError, dispatch]);

const { iconMap, loaded, loading } = useSelector(state => state.transactions);

   const handleCloseReceiptModal = () => {
    dispatch(setIsReceiptModalOpen(false));
  };

 useEffect(() => {
  console.log('Current iconMap:', iconMap); // Add this line
  if (!loaded && !loading) {
    dispatch(fetchAllIconsStart());
  }
}, [dispatch, loaded, loading, iconMap]);

const { saveSuccess, saveError } = useSelector((state) => state.receipts);
  const [sortModel, setSortModel] = useState([]);

  const handleSortModelChange = (newSortModel) => {
    setSortModel(newSortModel);
  };

  // Update sortComparator to use sortModel
  const getSortDirection = (field) => {
    const sortItem = sortModel.find((model) => model.field === field);
    return sortItem ? sortItem.sort : 'asc';
  };
  // Refs
  const dateFilterRef = useRef(null);
const [activeTab, setActiveTab] = useState(0);
const hasReceipt = receiptTransactionIds.includes(selectedTransaction?.transaction_id);

  // Fetch transactions on component mount
  useEffect(() => {
    dispatch(fetchTransactionsStart());
  }, [dispatch,page, pageSize]);

  // Cache awareness for transactions
  const {
    isReady: cacheReady,
    isCacheLoaded,
    isCacheLoading
  } = useCacheAwareness(['transactionSummary', 'hiddenTransactions'], false);

  // Use cached data if available, otherwise fetch
  useEffect(() => {
    const userId = getCurrentUserId();

    // Only fetch transaction summary if not already cached
    if (!cache?.transactionSummaryLoaded && userId) {
      console.log("🔄 Transaction summary not cached, dispatching fetch");
      dispatch(fetchTransactionSummaryStart({ userId }));
    } else if (cache?.transactionSummaryLoaded) {
      console.log("✅ Using cached transaction summary");
    }

    // Only fetch hidden transactions if not already cached
    if (!cache?.hiddenTransactionsLoaded && userId) {
      console.log("🔄 Hidden transactions not cached, dispatching fetch");
      dispatch(fetchHiddenTransactionsStart({ userId }));
    } else if (cache?.hiddenTransactionsLoaded) {
      console.log("✅ Using cached hidden transactions");
    }
  }, [dispatch, cache?.transactionSummaryLoaded, cache?.hiddenTransactionsLoaded]);  

  const handleSelectAll = () => {
    dispatch(toggleSelectAll());
  };

  const handleCheckboxChange = (transactionId) => {
    dispatch(toggleSelectTransaction(transactionId));
  };

  const toggleTransactionModal = (transaction) => {
    dispatch(setSelectedTransaction(transaction));
    dispatch(setOpenModal(true));
  };
   const toggleReceiptModal = () => {
      dispatch(setReceiptUploadModal(true));
      dispatch(setJsonResponse(null));
      dispatch(setErrorMessage('')); // Clear error message
    dispatch(setShowError(false));
    };


  const closeTransactionModal = () => {
    dispatch(setOpenModal(false));
    dispatch(setSelectedTransaction(null));
  };
//transaction detail model
const handleFieldChange = (field, value) => {
  dispatch(setSelectedTransaction({
    ...selectedTransaction,
    [field]: value
  }));
};
  const isMatchingTransactionAdd = useSelector(state => state.receipts.isMatchingTransactionAdd);

// In your TransactionPage1 component
const handleSaveChanges = () => {
  if (!selectedTransaction) return;

  // Validation for required fields
  if (
    !selectedTransaction.date ||
    !selectedTransaction.name ||
    !selectedTransaction.subcategory ||
    !selectedTransaction.bank ||
    !selectedTransaction.amount
  ) {
    alert('Please fill in all required fields: Date, Description, Category, Account, Amount.');
    return;
  }

   const matchedSub = subcategories.find(
    (sub) => sub.subCategory === selectedTransaction.subcategory
  );
  const categoryId = matchedSub?.categoryId || null;

  // ✅ Resolve accountId from selected account (stored as string)
  const accountId = Number(selectedTransaction.bank) || null;

  const formattedTransaction = {
    transactionId: selectedTransaction.transaction_id || `temp-${Date.now()}`,
    transactionDate: new Date(selectedTransaction.date).toISOString(),
    description: selectedTransaction.name,
    category: selectedTransaction.subcategory,
 categoryId,
     account: selectedTransaction.bank,
    accountId,  // ✅ use resolved accountId
    transactionAmount: parseFloat(selectedTransaction.amount) || 0,
    tax: parseFloat(selectedTransaction.tax) || 0,
    notes: selectedTransaction.notes || '',
    tag: selectedTransaction.tag || '',
    hideFromBudget: selectedTransaction.hideFromBudget || false,
    hidden: selectedTransaction.hidden || false,
    userId: getCurrentUserId(), // Adjust based on your auth logic
  };


  if (isMatchingTransactionAdd) {
    // Add to matchingTransactions in receiptSlice
    dispatch(addNewTransaction(formattedTransaction));
  } else if (selectedTransaction.transaction_id) {
    // Update existing transaction
    dispatch({
      type: 'transactions/updateTransaction',
      payload: {
        id: selectedTransaction.transaction_id,
        data: formattedTransaction,
      },
    });
  } else {
    // Add new transaction to database
    dispatch(addTransactionRequest({ transactionData: formattedTransaction }));
  }

  dispatch(setOpenModal(false));
  dispatch(setSelectedTransaction(null));
  dispatch(setIsMatchingTransactionAdd(false));
};
 const handleOpenAddTransaction = () => {
  dispatch(setSelectedTransaction({
    date: new Date().toISOString().split('T')[0],
    name: '',
    subcategory: '',
    bank: '',
    amount: '',
    tax: '',
    notes: '',
    tag: '',
    hideFromBudget: false,
    hidden: false,
  }));
  dispatch(setIsMatchingTransactionAdd(false));
  dispatch(setOpenModal(true));
};
const handletranssave = () => {
  let formattedDate = null;

  if (newTransaction.date) {
    const fullDate = new Date(newTransaction.date);
    if (!isNaN(fullDate)) {
      formattedDate = fullDate.toISOString();
    } else {
      console.warn('Invalid date format:', newTransaction.date);
    }
  }

  const transactionData = {
    transactionId: `temp-${Date.now()}`,
    transactionDate: formattedDate,
    description: newTransaction.description,
    category: newTransaction.category,
    account: newTransaction.account,
    transactionAmount: newTransaction.amount ? parseFloat(newTransaction.amount) : 0,
      userId: getCurrentUserId(),
  };

  dispatch(addTransactionRequest({ transactionData }));
};

//for trans detail popup

const formatDateForInput = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

//fetch category , subcategory 

const categories = useSelector(state => state.budget.categories);
const subcategories = useSelector(state => state.budget.subcategories);

// Fetch categories/subcategories on mount
useEffect(() => {
  dispatch({ type: 'budget/fetchCategories' });
  dispatch({ type: 'budget/fetchSubcategories' });
}, [dispatch]);

useEffect(() => {
  if (isPopupVisible) {
    dispatch({ type: 'budget/fetchCategories' });
    dispatch({ type: 'budget/fetchSubcategories' });
  }
}, [dispatch, isPopupVisible]);



    // Replace with actual data source


  const selectDateRange = (range) => {
  dispatch(setSelectedDateRange(range));
   logEvent('DateFilter', 'SelectRange', { range });

  const today = new Date();
  let start = null;
  let end = null;

  switch (range) {
    case 'last7days':
      start = new Date(today);
      start.setDate(today.getDate() - 6); // Include today
      end = today;
      break;
    case 'currentMonth':
      start = new Date(today.getFullYear(), today.getMonth(), 1);
      end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
      break;
    case 'lastMonth':
      start = new Date(today.getFullYear(), today.getMonth() - 1, 1);
      end = new Date(today.getFullYear(), today.getMonth(), 0);
      break;
 case 'custom':
 setCustomFilterOpen(true); // Open the input field
  dispatch(setSelectedDateRange('custom')); // ✅ Important!
  // dispatch(applyFilters());  
   return;


  
      case 'all':
      dispatch(setSearchDate({ start: '', end: '' }));
      dispatch(applyFilters());
      dispatch(toggleDateFilter());
      return;
  }

  if (start && end) {
    dispatch(setSearchDate({
      start: start.toISOString().split('T')[0],
      end: end.toISOString().split('T')[0]
    }));
    dispatch(applyFilters());
  }

  dispatch(toggleDateFilter());
};


  const applyFiltersHandler = () => {
    dispatch(applyFilters());
  };

  const exportToCSV = () => {
    const headers = ['Transaction Date', 'Description', 'Amount', 'Category', 'Account Name'];

  const rows = transactions.map(tx => [
    tx.transactionDate || '',
    tx.description || '',
    tx.transactionAmount || '',
    tx.category || '',
    tx.accountName || ''
  ]);
   const csvContent = "data:text/csv;charset=utf-8,"
    + [headers, ...rows].map(e => e.join(",")).join("\n");

  const encodedUri = encodeURI(csvContent);
  const link = document.createElement("a");
  link.setAttribute("href", encodedUri);
  link.setAttribute("download", "transactions.csv");
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const exportToPDF = () => {
  const doc = new jsPDF({ orientation: 'landscape' });

  if (!transactions.length) return;

  const headers = [['Transaction Date', 'Description', 'Amount', 'Category', 'Account Name']];

  const data = transactions.map(tx => [
    tx.transactionDate || '',
    tx.description || '',
    tx.transactionAmount || '',
    tx.category || '',
    tx.accountName || ''
  ]);

  autoTable(doc, {
    head: headers,
    body: data,
    styles: {
      fontSize: 7,
      cellPadding: 2,
      overflow: 'linebreak',
      cellWidth: 'wrap',
    },
    headStyles: {
      fillColor: [139, 195, 74],
      fontSize: 7,
    },
    startY: 10,
    margin: { left: 5, right: 5 },
    theme: 'striped',
  });

  doc.save('transactions.pdf');
};

  // Helper functions
  
  // Group transactions by date
 const handleRowClick = (transactionId) => {
    console.log('1. Row clicked - transactionId:', transactionId);
    
    setExpandedRows(prev => {
      const newState = {...prev, [transactionId]: !prev[transactionId]};
      console.log('2. Expanded rows state:', newState);
      return newState;
    });
  
    const transaction = rows.find(row => row.transaction_id === transactionId);
    console.log('3. Found transaction:', transaction);
    
    if (!reconciledTransactionsById[transaction.reconcileId]) {
      console.log('Dispatching to fetch reconciled transactions');
      dispatch({ 
        type: 'transactions/getReconciledTransactions', 
        payload: transaction.reconcileId 
      });
    } else {
      console.log('4. No reconcileId found for transaction');
    }
  };
  
  useEffect(() => {
      if (!Array.isArray(transactions)) return;

    const buildRows = () => {
      console.log('Building rows with state:', {
        transactions,
        reconciledTransactionsById,
        expandedRows
      });
      
      console.log('Transactions data:', transactions.map(t => ({
        id: t.id,
        description: t.description,
        reconcileId: t.reconcileId,
      })));
      const groupedTransactions = transactions.reduce((acc, transaction) => {
        const transactionDate = new Date(transaction.transactionDate).toLocaleDateString();
        if (!acc[transactionDate]) {
          acc[transactionDate] = { totalAmount: 0, transactions: [] };
        }
        acc[transactionDate].transactions.push(transaction);
        acc[transactionDate].totalAmount += transaction.transactionAmount || 0;
        return acc;
      }, {});
  
      let rowsArray = [];
      let idCounter = 1;
  
      Object.keys(groupedTransactions)
        .sort((a, b) => new Date(b) - new Date(a))
        .forEach((date) => {
          rowsArray.push({
            id: `header-${date}`,
            date,
            isGroupHeader: true,
            amount: groupedTransactions[date].totalAmount?.toFixed(2) || '0.00',
            name: '', // Ensure name is set for group header
            category: '',
            bank: '',
          });
  
          groupedTransactions[date].transactions.forEach((transaction) => {
            const isExpanded = expandedRows[transaction.id];
            rowsArray.push({
              id: idCounter++,
              transaction_id: transaction.id,
              date,
              name: transaction.description,
              category: transaction.category,
              amount: transaction.transactionAmount?.toFixed(2) || 'N/A',
              bank: transaction.accountName || 'Unknown Bank',
              receipts: transaction.receiptUrl,
              reconcileId: transaction.reconcileId,
              categoryId: transaction.categoryId,
              subCategoryId: transaction.subCategoryId,   // <--- add this line
              isExpanded,
            });
  
            if (isExpanded && transaction.reconcileId && reconciledTransactionsById[transaction.reconcileId]) {
              console.log('Adding reconciled rows for:', transaction.reconcileId);
              console.log('Reconciled transactions:', reconciledTransactionsById[transaction.reconcileId]);
                const originalAmount = transaction.transactionAmount;

              reconciledTransactionsById[transaction.reconcileId].forEach((match, index) => {
if (
      match.id !== transaction.id &&
      (originalAmount * match.amount < 0) // ensures opposite sign
    ) {             rowsArray.push({
                    id: `reconcile-${transaction.id}-${index}`,
                    isReconciledDetail: true,
                    date: new Date(match.transactionDate).toLocaleDateString(),
                    name: match.description,
                    category: match.category,
                    amount: match.amount?.toFixed(2) || 'N/A', // Use match.amount and add null check
                    bank: match.accountName || 'Unknown',
                    parentId: transaction.id,
                  });
                }
              });
            }
          });
        });
  
      console.log('Final rows built:', rowsArray);
      return rowsArray;
    };
  
    setRows(buildRows());
  }, [transactions, reconciledTransactionsById, expandedRows]);
  

 const hiddenColumns = [
  {
    field: 'transactionDate',
    headerName: 'Date',
    width: 300,
    // minWidth: 120,
    headerClassName: 'lime-header',
    renderCell: (params) => (
      <span className="text-gray-700 text-sm">
        {params.value}
      </span>
    ),
  },
  {
    field: 'description',
    headerName: 'Description',
    width: 320,
    minWidth: 180,
    flex: 1,
    headerClassName: 'lime-header',
    renderCell: (params) => {
      const handleNameClick = (e) => {
        e.stopPropagation();
        dispatch(setSelectedTransaction(params.row));
        dispatch(setOpenModal(true));
      };

      return (
        <div onClick={handleNameClick} className="cursor-pointer w-full">
          <TransactionNameCell 
            name={params.value}
            icon={params.row.icon}
          />
        </div>
      );
    },
  },
  {
    field: 'category',
    headerName: 'Category',
    width: 250,
    // minWidth: 120,
    headerClassName: 'lime-header',
    renderCell: (params) => (
      <span className="text-gray-700 text-sm">
        {params.value}
      </span>
    ),
  },
  {
    field: 'accountName',
    headerName: 'Account',
    width: 300,
    // minWidth: 150,
    headerClassName: 'lime-header',
    renderCell: (params) => (
      <AccountCell bankName={params.value} />
    ),
  },
  { 
    field: 'transactionAmount', 
    headerName: 'Amount', 
    width: 220,
    // minWidth: 120,
    headerClassName: 'lime-header',
    align: 'right',
    headerAlign: 'right',
    renderCell: (params) => (
      <span className="text-gray-700 text-sm font-medium">
        {params.value}
      </span>
    ),
  }
];
  // Additional data for the cards
const additionalData1 = [
  { 
    label: 'Total Transaction', 
    value: summary?.totalTransaction || '$0',
    icon: FaMoneyBillWave,
    color: 'from-lime-400 to-lime-500',
    bgColor: 'bg-lime-50',
    darkBgColor: 'bg-lime-900/20'
  },
  { 
    label: 'Total Debit', 
    value: `$${summary?.totalDebit || 0}`,
    icon: FaArrowDown,
    color: 'from-red-400 to-red-500',
    bgColor: 'bg-red-50',
    darkBgColor: 'bg-red-900/20'
  },
  { 
    label: 'Total Credit', 
    value: `$${summary?.totalCredit || 0}`,
    icon: FaArrowUp,
    color: 'from-green-400 to-green-500',
    bgColor: 'bg-green-50',
    darkBgColor: 'bg-green-900/20'
  },
];

const additionalData2 = [
  { 
    label: 'Total Amount', 
    value: `$${summary?.totalAmount || 0}`,
    icon: FaDollarSign,
    color: 'from-lime-400 to-lime-600',
    bgColor: 'bg-lime-50',
    darkBgColor: 'bg-lime-900/20'
  },
  { 
    label: 'Largest Transaction', 
    value: `$${summary?.largestTransaction || 0}`,
    icon: FaTrophy,
    color: 'from-yellow-400 to-yellow-500',
    bgColor: 'bg-yellow-50',
    darkBgColor: 'bg-yellow-900/20'
  },
  { 
    label: 'Dispute Transaction', 
    value: `${summary?.disputeTransaction || 0}`,
    icon: FaExclamationTriangle,
    color: 'from-orange-400 to-orange-500',
    bgColor: 'bg-orange-50',
    darkBgColor: 'bg-orange-900/20'
  },
];

  const headerBaseClass = 'font-[Architects Daughter] text-lg';
  const headerBgClass = darkMode ? 'bg-gray-800 text-white' : 'bg-white text-black';
  
  // Define DataGrid columns
const columns = [
 {
    field: 'checkbox',
    headerName: '',
    width: 60,
    minWidth: 60,
    maxWidth: 60,
    sortable: false,
    filterable: false,
    disableColumnMenu: true,
    renderHeader: (params) => {
      const selectableRows = rows.filter(row =>
        !row.isGroupHeader &&
        !row.isReconciledDetail &&
        row.transaction_id
      );

      const allSelected = selectableRows.length > 0 &&
        selectableRows.every(row => selectedTransactions.includes(row.transaction_id));

      const someSelected = selectableRows.some(row =>
        selectedTransactions.includes(row.transaction_id)
      );

      return (
         <div className="flex items-center justify-center h-full">
          <input
            type="checkbox"
            checked={allSelected}
            ref={(el) => {
              if (el) el.indeterminate = !allSelected && someSelected;
            }}
            onChange={(e) => {
              const isChecked = e.target.checked;
              const transactionIds = selectableRows.map(row => row.transaction_id);

              if (isChecked) {
                dispatch(selectAllTransactions(transactionIds));
              } else {
                dispatch(deselectAllTransactions(transactionIds));
              }
            }}
            className="h-4 w-4 text-lime-500 border-gray-300 rounded focus:ring-lime-500"
            onClick={(e) => e.stopPropagation()}
          />
        </div>
      );
    },
    renderCell: (params) => {
      if (params.row.isGroupHeader || params.row.isReconciledDetail || !params.row.transaction_id) return null;
      const isSelected = selectedTransactions.includes(params.row.transaction_id);
      return (
        <div className={`${isSelected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'} transition-opacity checkbox-cell`}>
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => dispatch(toggleSelectTransaction(params.row.transaction_id))}
            className="h-4 w-4 text-lime-500 border-gray-300 rounded focus:ring-lime-500"
            onClick={(e) => e.stopPropagation()}
          />
        </div>
      );
    },
  },
  {
    field: 'date',
    headerName: 'Date',
    width: 180,
    minWidth: 150,
    maxWidth: 180,
    headerClassName: 'font-mono text-lg text-lime-700',
    renderCell: (params) =>
      params.row.isGroupHeader ? (
        <strong className="text-base text-lime-700">{params.value}</strong>
      ) : (
        <span className={params.row.isReconciledDetail ? "text-gray-500 pl-4" : ""}>{params.value}</span>
      ),
  },
  {
    field: 'name',
    headerName: 'Description',
    // width: 300,
    minWidth: 200,
    flex: 1,
    maxWidth: 300,
    headerClassName: 'font-mono text-lg text-lime-700',
    renderCell: (params) => {
      if (params.row.isGroupHeader) {
        return null;
      }
      if (params.row.isReconciledDetail) {
        return (
          <div className="pl-8 text-gray-500 italic">
            <TransactionNameCell name={params.value} icon={params.row.icon} />
          </div>
        );
      }
      const hasReconcile = params.row.reconcileId;
      const isLoading =
        hasReconcile &&
        expandedRows[params.row.transaction_id] &&
        !reconciledTransactionsById[params.row.reconcileId];

      const handleArrowClick = (e) => {
        e.stopPropagation(); // Prevent row click
        handleRowClick(params.row.transaction_id); // Trigger expansion
      };

      const handleNameClick = (e) => {
        e.stopPropagation(); // Prevent row click
        dispatch(setSelectedTransaction(params.row));
        dispatch(setOpenModal(true));
      };

      return (
        <Tooltip
          title={
            hasReconcile ? (
              <div className="p-2">
                <span className="text-white">Click the arrow to view reconciled transactions</span>
              </div>
            ) : (
              <span>No reconciled transactions available</span>
            )
          }
          placement="top"
          arrow
          disableInteractive={!hasReconcile}
        >
          <div className="flex items-center space-x-2">
            {hasReconcile ? (
              <div onClick={handleArrowClick} className="cursor-pointer text-lime-600">
                {isLoading ? (
                  <CircularProgress size={20} className="text-lime-500" />
                ) : (
                  expandedRows[params.row.transaction_id] ? '▼' : '▶'
                )}
              </div>
            ) : (
              <div className="w-5" />
            )}
            <div onClick={handleNameClick} className="cursor-pointer">
              <TransactionNameCell name={params.value} icon={params.row.icon} />
            </div>
          </div>
        </Tooltip>
      );
    },
    sortComparator: (v1, v2, param1, param2) => {
      const row1 = param1.api.getRow(param1.id);
      const row2 = param2.api.getRow(param2.id);

      const dateComparison = new Date(row2.date) - new Date(row1.date);
      if (row1.date !== row2.date) {
        return dateComparison;
      }

      if (row1.isGroupHeader && !row2.isGroupHeader) return -1;
      if (!row1.isGroupHeader && row2.isGroupHeader) return 1;
      if (row1.isGroupHeader && row2.isGroupHeader) return 0;

      if (row1.isReconciledDetail || row2.isReconciledDetail) {
        if (row1.isReconciledDetail && !row2.isReconciledDetail) return 1;
        if (!row1.isReconciledDetail && row2.isReconciledDetail) return -1;
        if (row1.isReconciledDetail && row2.isReconciledDetail && row1.parentId === row2.parentId) {
          return (v1 || '').localeCompare(v2 || '');
        }
        return 0;
      }

      return (v1 || '').localeCompare(v2 || '');
    },
  },
  {
    field: 'category',
    headerName: 'Category',
width: 200,
    minWidth: 150,
    maxWidth: 200,
        headerClassName: 'font-mono text-lg text-lime-700',
     renderCell: (params) => {
    if (params.row.isGroupHeader) return null;

    const isReconciled = params.row.isReconciledDetail;
    const categoryId = params.row.categoryId;
    const subCategoryId = params.row.subCategoryId;
  

// const IconComponent = iconMapping[iconKey] || FaQuestionCircle;
    const handleCategoryClick = (e) => {
      e.stopPropagation(); // Prevent row selection
      console.log("Row Data:", params.row); // Debugging

      // const category = params.value;
     setSelectedCategoryId(params.row.categoryId); // Store categoryId

dispatch(fetchCategoryMonthlyExpensesStart({
            categoryId: params.row.categoryId,
            subCategoryId: params.row.subCategoryId, // Include subCategoryId
            userId: 1, // Hardcoded for user 1
            months: 3
        }));
            dispatch(setSelectedCategory(params.row.category));
            dispatch(setSelectedSubCategoryId(params.row.subCategoryId)); // Add this
            dispatch(setOpenCategoryModal(true));
    };

    return (
      <span
    className={`flex items-center gap-2 cursor-pointer hover:text-lime-600 ${
      isReconciled ? 'text-gray-500 pl-4' : ''
    }`}
    onClick={handleCategoryClick}
  >
  <SubCategoryIcon 
          subCategoryId={subCategoryId} 
          size={16}
          className="flex-shrink-0"
        />
        <span>{params.value}</span>
      </span>
    );
  },
    sortComparator: (v1, v2, param1, param2) => {
      const row1 = param1.api.getRow(param1.id);
      const row2 = param2.api.getRow(param2.id);

      const dateComparison = new Date(row2.date) - new Date(row1.date);
      if (row1.date !== row2.date) {
        return dateComparison;
      }

      if (row1.isGroupHeader && !row2.isGroupHeader) return -1;
      if (!row1.isGroupHeader && row2.isGroupHeader) return 1;
      if (row1.isGroupHeader && row2.isGroupHeader) return 0;

      if (row1.isReconciledDetail || row2.isReconciledDetail) {
        if (row1.isReconciledDetail && !row2.isReconciledDetail) return 1;
        if (!row1.isReconciledDetail && row2.isReconciledDetail) return -1;
        if (row1.isReconciledDetail && row2.isReconciledDetail && row1.parentId === row2.parentId) {
          return (v1 || '').localeCompare(v2 || '');
        }
        return 0;
      }

      return (v1 || '').localeCompare(v2 || '');
    },
  },
  {
    field: 'bank',
    headerName: 'Account',
width: 200,
    minWidth: 150,
    maxWidth: 200,
        headerClassName: 'font-mono text-lg text-lime-700',
    renderCell: (params) => {
      if (params.row.isGroupHeader) return null;
      return <AccountCell bankName={params.value} />;
    },
    sortComparator: (v1, v2, param1, param2) => {
      const row1 = param1.api.getRow(param1.id);
      const row2 = param2.api.getRow(param2.id);

      const dateComparison = new Date(row2.date) - new Date(row1.date);
      if (row1.date !== row2.date) {
        return dateComparison;
      }

      if (row1.isGroupHeader && !row2.isGroupHeader) return -1;
      if (!row1.isGroupHeader && row2.isGroupHeader) return 1;
      if (row1.isGroupHeader && row2.isGroupHeader) return 0;

      if (row1.isReconciledDetail || row2.isReconciledDetail) {
        if (row1.isReconciledDetail && !row2.isReconciledDetail) return 1;
        if (!row1.isReconciledDetail && row2.isReconciledDetail) return -1;
        if (row1.isReconciledDetail && row2.isReconciledDetail && row1.parentId === row2.parentId) {
          return (v1 || '').localeCompare(v2 || '');
        }
        return 0;
      }

      return (v1 || '').localeCompare(v2 || '');
    },
  },
  {
    field: 'amount',
    headerName: 'Price',
width: 140,
    minWidth: 100,
    maxWidth: 140,
        headerClassName: 'font-mono text-lg text-lime-700',
    type: 'number',
    renderCell: (params) =>
      params.row.isGroupHeader ? (
        <strong className="text-lime-700">{params.value}</strong>
      ) : (
        <span className={params.row.isReconciledDetail ? "text-gray-500 pl-4" : ""}>
          {params.value}
        </span>
      ),
    sortComparator: (v1, v2, param1, param2) => {
      const row1 = param1.api.getRow(param1.id);
      const row2 = param2.api.getRow(param2.id);

      const dateComparison = new Date(row2.date) - new Date(row1.date);
      if (row1.date !== row2.date) {
        return dateComparison;
      }

      if (row1.isGroupHeader && !row2.isGroupHeader) return -1;
      if (!row1.isGroupHeader && row2.isGroupHeader) return 1;
      if (row1.isGroupHeader && row2.isGroupHeader) return 0;

      if (row1.isReconciledDetail || row2.isReconciledDetail) {
        if (row1.isReconciledDetail && !row2.isReconciledDetail) return 1;
        if (!row1.isReconciledDetail && row2.isReconciledDetail) return -1;
        if (row1.isReconciledDetail && row2.isReconciledDetail && row1.parentId === row2.parentId) {
          return parseFloat(v1 || 0) - parseFloat(v2 || 0);
        }
        return 0;
      }

      return parseFloat(v1 || 0) - parseFloat(v2 || 0);
    },
  },
  {
    field: 'receipts',
    headerName: 'Receipts',
    width: 140,
    minWidth: 120,
    maxWidth: 140,
    headerClassName: 'font-mono text-lg text-lime-700',
    renderCell: (params) => {
        if (params.row.isGroupHeader) return null;
        const receiptImageUrl = params.value || receipt;
        const splitIconUrl = split;
        const hasReceipt = receiptTransactionIds.includes(params.row.transaction_id);
        return (
        <div className="flex items-center justify-center h-full">
        <Tooltip title={hasReceipt ? '' : 'No receipt available'}>

           <img
                src={receiptImageUrl}
                alt="Receipt"
                style={{
                  width: '20px',
                  height: '20px',
                  cursor: hasReceipt ? 'pointer' : 'not-allowed',
                  opacity: hasReceipt ? 1 : 0.5,
                }}
                onClick={() => {
                  if (hasReceipt) {
                    handleReceiptClick(params.row.transaction_id);
                  }
                }}
              />
            </Tooltip>
          <img
            src={split}
            alt="Split Icon"
            className="w-5 h-5 ml-3 cursor-pointer"
            onClick={(e) => {
              e.stopPropagation(); // Prevent row click
              handleSplitIconClick(params.row);
            }}
          />
        </div>
      );
    },
  },
];


  // Update renderCardLayout function to accept darkMode
const renderCardLayout = (data, idPrefix, darkMode) => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
    {data.map((item, index) => {
      const IconComponent = item.icon;
      return (
        <div
          key={`${idPrefix}-${index}`}
          className={`
            group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl
            transform hover:-translate-y-2 transition-all duration-300 ease-out cursor-pointer
            border border-lime-200/50 hover:border-lime-300 w-full
            ${darkMode
               ? `${item.darkBgColor} hover:bg-opacity-40 backdrop-blur-lg`
               : `${item.bgColor} hover:bg-opacity-80`
            }
          `}
        >
          {/* Top Decorative Bar */}
          <div className={`
            h-1 w-full bg-gradient-to-r ${item.color}
            transform scale-x-0 group-hover:scale-x-100 
            transition-transform duration-300 origin-left
          `}></div>
                     
          <div className="p-4 relative z-10">
            {/* Header with Icon and Label on Left */}
            <div className="flex items-center mb-4">
              <div className={`
                p-2 rounded-lg bg-gradient-to-br ${item.color}
                shadow-lg transform group-hover:rotate-6 group-hover:scale-105
                transition-all duration-300 mr-3
              `}>
                <IconComponent className="text-white text-lg" />
              </div>
              
              {/* Label next to icon */}
              <label className={`
                text-sm font-bold uppercase tracking-wide
                ${darkMode ? 'text-gray-300' : 'text-gray-600'}
                group-hover:text-lime-600 transition-colors duration-300
              `}>
                {item.label}
              </label>

              {/* Fixed Dot - moved to far right */}
              <div className="ml-auto">
                <div className={`
                  w-2 h-2 rounded-full bg-gradient-to-r ${item.color}
                `}></div>
              </div>
            </div>
                         
            {/* Value - Centered */}
            <div className="text-center">
              <p className={`
                text-2xl font-bold tracking-tight
                ${darkMode ? 'text-white' : 'text-gray-900'}
                group-hover:text-lime-700 transition-colors duration-300
              `}>
                {item.value}
              </p>
            </div>
          </div>
        </div>
      );
    })}
  </div>
);

 return (
  <div className={`
    p-8 min-h-screen w-full font-roboto relative overflow-hidden
    ${darkMode
       ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900'
       : 'bg-gradient-to-br from-lime-50 via-white to-lime-100'
    }
  `}>
    {/* Fixed Background Elements */}
    <div className="absolute inset-0 overflow-hidden pointer-events-none">
      <div className="absolute -top-40 -right-40 w-80 h-80 rounded-full bg-lime-300 opacity-10"></div>
      <div className="absolute -bottom-40 -left-40 w-96 h-96 rounded-full bg-lime-400 opacity-5"></div>
    </div>
         
    {/* Header */}
    <div className="relative z-10 mb-8">
      <h1 className={`
        flex items-center text-2xl font-bold tracking-tight
        ${darkMode ? 'text-white' : 'text-gray-900'}
      `}>
        Transactions
        <FaChevronDown
          className={`
            ml-4 cursor-pointer transition-all duration-300 ease-in-out
            hover:scale-110 active:scale-95
            ${isCardVisible ? 'rotate-180' : 'rotate-0'}
            ${darkMode ? 'text-gray-300 hover:text-white' : 'text-gray-600 hover:text-gray-900'}
          `}
          onClick={() => dispatch(toggleCardVisibility())}
        />
      </h1>
    </div>
 
    {/* Cards Container */}
    {isCardVisible && (
      <div className="relative z-10 max-w-7xl mx-auto">
        {/* Single grid for all cards in 3 columns */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {additionalData1.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <div
                key={`card-${index}`}
                className={`
                  group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl
                  transform hover:-translate-y-2 transition-all duration-300 ease-out cursor-pointer
                  border border-lime-200/50 hover:border-lime-300 w-full
                  ${darkMode
                     ? `${item.darkBgColor} hover:bg-opacity-40 backdrop-blur-lg`
                     : `${item.bgColor} hover:bg-opacity-80`
                  }
                `}
              >
                {/* Top Decorative Bar */}
                <div className={`
                  h-1 w-full bg-gradient-to-r ${item.color}
                  transform scale-x-0 group-hover:scale-x-100 
                  transition-transform duration-300 origin-left
                `}></div>
                           
                <div className="p-4 relative z-10">
                  {/* Header with Icon and Label on Left */}
                  <div className="flex items-center mb-4">
                    <div className={`
                      p-2 rounded-lg bg-gradient-to-br ${item.color}
                      shadow-lg transform group-hover:rotate-6 group-hover:scale-105
                      transition-all duration-300 mr-3
                    `}>
                      <IconComponent className="text-white text-lg" />
                    </div>
                    
                    {/* Label next to icon */}
                    <label className={`
                      text-sm font-bold uppercase tracking-wide
                      ${darkMode ? 'text-gray-300' : 'text-gray-600'}
                      group-hover:text-lime-600 transition-colors duration-300
                    `}>
                      {item.label}
                    </label>

                    {/* Fixed Dot - moved to far right */}
                    <div className="ml-auto">
                      <div className={`
                        w-2 h-2 rounded-full bg-gradient-to-r ${item.color}
                      `}></div>
                    </div>
                  </div>
                               
                  {/* Value - Centered */}
                  <div className="text-center">
                    <p className={`
                      text-2xl font-bold tracking-tight
                      ${darkMode ? 'text-white' : 'text-gray-900'}
                      group-hover:text-lime-700 transition-colors duration-300
                    `}>
                      {item.value}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
          
          {additionalData2.map((item, index) => {
            const IconComponent = item.icon;
            return (
              <div
                key={`card-${index + 3}`}
                className={`
                  group relative overflow-hidden rounded-xl shadow-lg hover:shadow-xl
                  transform hover:-translate-y-2 transition-all duration-300 ease-out cursor-pointer
                  border border-lime-200/50 hover:border-lime-300 w-full
                  ${darkMode
                     ? `${item.darkBgColor} hover:bg-opacity-40 backdrop-blur-lg`
                     : `${item.bgColor} hover:bg-opacity-80`
                  }
                `}
              >
                {/* Top Decorative Bar */}
                <div className={`
                  h-1 w-full bg-gradient-to-r ${item.color}
                  transform scale-x-0 group-hover:scale-x-100 
                  transition-transform duration-300 origin-left
                `}></div>
                           
                <div className="p-4 relative z-10">
                  {/* Header with Icon and Label on Left */}
                  <div className="flex items-center mb-4">
                    <div className={`
                      p-2 rounded-lg bg-gradient-to-br ${item.color}
                      shadow-lg transform group-hover:rotate-6 group-hover:scale-105
                      transition-all duration-300 mr-3
                    `}>
                      <IconComponent className="text-white text-lg" />
                    </div>
                    
                    {/* Label next to icon */}
                    <label className={`
                      text-sm font-bold uppercase tracking-wide
                      ${darkMode ? 'text-gray-300' : 'text-gray-600'}
                      group-hover:text-lime-600 transition-colors duration-300
                    `}>
                      {item.label}
                    </label>

                    {/* Fixed Dot - moved to far right */}
                    <div className="ml-auto">
                      <div className={`
                        w-2 h-2 rounded-full bg-gradient-to-r ${item.color}
                      `}></div>
                    </div>
                  </div>
                               
                  {/* Value - Centered */}
                  <div className="text-center">
                    <p className={`
                      text-2xl font-bold tracking-tight
                      ${darkMode ? 'text-white' : 'text-gray-900'}
                      group-hover:text-lime-700 transition-colors duration-300
                    `}>
                      {item.value}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    )}

  {isReceiptModalOpen && (
        <ReceiptModal 
          receiptData={selectedReceipt} 
          onClose={handleCloseReceiptModal} 
          handleFieldDoubleClick={handleFieldDoubleClick}
        />
      )}

      <SplitTransactionPopup
        open={splitModalOpen}
        onClose={handleCloseSplitModal}
        transaction={selectedSplitTransaction}
        onSubmitSplit={handleSubmitSplit}
      />
       <CategoryChartModal categoryId={selectedCategoryId} />
       <ReceiptHandler/>
      {/* <CategoryChartMiniPopup darkMode={darkMode} categoryId={selectedCategoryId} /> */}


      {/* Filters */}
<div className="flex-1 p-4 overflow-visible relative">
  {/* Search and Button Container - Fixed positioning */}
<div className="flex items-center justify-between gap-4 mb-4 w-full max-w-7xl mx-auto">    {/* Left side - Search */}
<div className="flex-shrink-0 w-60">
        <input
        type="text"
        placeholder="Search by description (e.g., KFC, Spotify)"
        className="border border-gray-300 rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 w-60"
        value={customText}
        onChange={(e) => {
          dispatch(setCustomSearchText(e.target.value));
          dispatch(setSelectedDateRange('custom'));
          dispatch(applyFilters());
        }}
      />
    </div>
  {/* Add Transaction Button */}
    <div className="flex flex-wrap items-center gap-2 flex-shrink-0">
      <Tooltip title="Add New Transaction" placement="top">
        <button
          className="bg-[#8bc34a] hover:bg-[#7cb342] text-black p-2 rounded flex items-center"
          onClick={handleOpenAddTransaction}
        >
          <AddCircleOutline fontSize="small" />
        </button>
      </Tooltip>


  {/* Hide Selected Button */}
 <Tooltip title="Hide Selected Transactions" placement="top">
        <button
          className="bg-[#8bc34a] hover:bg-[#7cb342] text-black p-2 rounded flex items-center disabled:opacity-50"
          onClick={() => {
            if (selectedTransactions.length === 0) return;
            console.log('Selected transactions:', selectedTransactions);
            if (window.confirm('Are you sure you want to hide the selected transactions?')) {
              dispatch({
                type: 'transactions/hideTransactions',
                payload: selectedTransactions
              });
            }
          }}
          disabled={selectedTransactions.length === 0 || isProcessing}
        >
          {isProcessing ? (
            <CircularProgress size={20} className="mr-2" />
          ) : (
            <VisibilityOff fontSize="small" />
          )}
        </button>
      </Tooltip>

{/* Hide from Budget Button */}
        <Tooltip title="Hide Selected Transactions from Budget" placement="top">
        <button
          className="bg-[#8bc34a] hover:bg-[#7cb342] text-black p-2 rounded flex items-center disabled:opacity-50"
          onClick={() => {
            if (selectedTransactions.length === 0) return;
            console.log('Selected transactions to hide from budget:', selectedTransactions);
            if (window.confirm('Are you sure you want to hide the selected transactions from budget?')) {
              dispatch({
                type: 'transactions/hideFromBudget',
                payload: selectedTransactions,
              });
            }
          }}
          disabled={selectedTransactions.length === 0 || isProcessing}
        >
          {isProcessing ? (
            <CircularProgress size={20} className="mr-2" />
          ) : (
            <AccountBalanceWalletIcon fontSize="small" />
          )}
        </button>
      </Tooltip>


  {/* Filter By Button */}
       <div className="relative z-50">
        <Tooltip title="Filter Transactions" placement="top">
          <div
            className="bg-[#8bc34a] hover:bg-[#7cb342] text-black p-2 rounded flex items-center cursor-pointer relative"
            onClick={() => dispatch(toggleDateFilter())}
          >
            <FilterList fontSize="small" />
            
            {/* Date Filter Dropdown */}
            {dateFilterOpen && (
              <div
                ref={dateFilterRef}
                className="absolute top-full right-0 bg-white shadow-xl border border-gray-200 rounded-xl overflow-hidden z-50 min-w-48 animate-in slide-in-from-top-2 duration-200 mt-1"
              >
                <div className="py-2">
                  <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100">
                    Date Range
                  </div>
                  
                  <button 
                    onClick={() => selectDateRange('all')} 
                    className="w-full px-4 py-3 text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 text-left transition-all duration-150 flex items-center gap-3 group"
                  >
                    <div className="w-2 h-2 rounded-full bg-gray-300 group-hover:bg-blue-500 transition-colors duration-150"></div>
                    <span className="font-medium">All</span>
                  </button>
                  
                  <button 
                    onClick={() => selectDateRange('last7days')} 
                    className="w-full px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 text-left transition-all duration-150 flex items-center gap-3 group"
                  >
                    <div className="w-2 h-2 rounded-full bg-gray-300 group-hover:bg-green-500 transition-colors duration-150"></div>
                    <span className="font-medium">Last 7 Days</span>
                  </button>
                  
                  <button 
                    onClick={() => selectDateRange('currentMonth')} 
                    className="w-full px-4 py-3 text-sm text-gray-700 hover:bg-purple-50 hover:text-purple-700 text-left transition-all duration-150 flex items-center gap-3 group"
                  >
                    <div className="w-2 h-2 rounded-full bg-gray-300 group-hover:bg-purple-500 transition-colors duration-150"></div>
                    <span className="font-medium">Current Month</span>
                  </button>
                  
                  <button 
                    onClick={() => selectDateRange('lastMonth')} 
                    className="w-full px-4 py-3 text-sm text-gray-700 hover:bg-orange-50 hover:text-orange-700 text-left transition-all duration-150 flex items-center gap-3 group"
                  >
                    <div className="w-2 h-2 rounded-full bg-gray-300 group-hover:bg-orange-500 transition-colors duration-150"></div>
                    <span className="font-medium">Last Month</span>
                  </button>
                </div>
              </div>
            )}
          </div>
        </Tooltip>
      </div>

      {/* Upload Receipt Button */}
      <Tooltip title="Upload Receipt" placement="top">
        <button
          className="bg-[#8bc34a] hover:bg-[#7cb342] text-black p-2 rounded flex items-center"
          onClick={toggleReceiptModal}
        >
          <Receipt fontSize="small" />
        </button>
      </Tooltip>

      {/* Export Data Button with Dropdown */}
      <div className="relative z-50">
        <Tooltip title="Export Transactions" placement="top">
          <button
            className="bg-[#8bc34a] hover:bg-[#7cb342] text-black p-2 rounded flex items-center"
            onClick={() => setExportDropdownOpen(!exportDropdownOpen)}
          >
            <Download fontSize="small" />
          </button>
        </Tooltip>

        {/* Export Options Dropdown */}
        {exportDropdownOpen && (
          <div
            ref={exportDropdownRef}
            className="absolute top-full right-0 bg-white shadow-xl border border-gray-200 rounded-xl overflow-hidden z-50 min-w-40 animate-in slide-in-from-top-2 duration-200 mt-1"
          >
            <div className="py-2">
              <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider border-b border-gray-100">
                Export Format
              </div>
              
              {/* CSV Export Option */}
              <button 
                onClick={() => {
                  exportToCSV();
                  setExportDropdownOpen(false);
                }} 
                className="w-full px-4 py-3 text-sm text-gray-700 hover:bg-green-50 hover:text-green-700 text-left transition-all duration-150 flex items-center gap-3 group"
              >
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center group-hover:bg-green-200 transition-colors duration-150">
                  <svg className="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2M18,20H6V4H13V9H18V20Z" />
                  </svg>
                </div>
                <div>
                  <div className="font-medium">Export as CSV</div>
                  <div className="text-xs text-gray-500">Spreadsheet format</div>
                </div>
              </button>
              
              {/* PDF Export Option */}
              <button 
                onClick={() => {
                  exportToPDF();
                  setExportDropdownOpen(false);
                }} 
                className="w-full px-4 py-3 text-sm text-gray-700 hover:bg-red-50 hover:text-red-700 text-left transition-all duration-150 flex items-center gap-3 group"
              >
                <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center group-hover:bg-red-200 transition-colors duration-150">
                  <PictureAsPdf className="text-red-600" fontSize="small" />
                </div>
                <div>
                  <div className="font-medium">Export as PDF</div>
                  <div className="text-xs text-gray-500">Document format</div>
                </div>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  </div>
  </div>
   {/* DataGrid */}
<div className="w-full max-w-7xl mx-auto overflow-x-auto -ml-4">
        <div className={`
          w-full font-bold p-2.5 mt-5
          ${darkMode ? 'bg-gray-800 text-white' : 'bg-white text-black'}
          rounded-t-xl cursor-pointer border-t border-l border-r
          ${darkMode ? 'border-gray-600' : 'border-gray-300'}
          shadow-[0_-4px_6px_0_rgba(0,0,0,0.1),0_6px_8px_-4px_rgba(0,0,0,0.1)]
        `}>
 
    <DataGrid
    rows={rows}
    disableColumnResize
    pagination={false}
    columns={columns}  
  hideFooter
    disableColumnSelector
    autoHeight
    sortingMode="client"
      sortModel={sortModel}
      onSortModelChange={handleSortModelChange}
      disableSelectionOnClick
 getRowId={(row) => row.id || row.transaction_id}
    sx={{
          '& .MuiDataGrid-cell': {
            border: 'none',
            padding: '8px 16px',
          },
          '& .MuiDataGrid-columnHeaders': {
            border: 'none',
            backgroundColor: 'rgba(132, 204, 22, 0.1)',
            borderRadius: '4px',
            position: 'sticky',
                top: 0,
                zIndex: 10,
          },
          '& .MuiDataGrid-root': {
            border: 'none',
            width: '100%',
                maxWidth: '100%',
          },
          border: 'none',
          '& .MuiDataGrid-row': {
            '&:hover': {
              backgroundColor: 'rgba(132, 204, 22, 0.05)',
            },
            cursor: 'pointer',
            borderBottom: '1px solid rgba(224, 224, 224, 0.4)'
          },
          '& .MuiDataGrid-row .checkbox-cell': {
            opacity: 0,
            transition: 'opacity 0.2s ease',
          },
          '& .MuiDataGrid-row:hover .checkbox-cell': {
            opacity: 1,
          },
          '& .MuiDataGrid-row .checkbox-selected': {
            opacity: '1 !important',
          },
          '& .reconciled-detail-row': {
            backgroundColor: 'rgba(249, 250, 251, 0.9)',
            fontStyle: 'italic',
          },
          '& .group-header-row': {
            backgroundColor: 'rgba(132, 204, 22, 0.08)',
            fontWeight: 'bold',
          }
        }}
        getRowClassName={(params) => {
          if (!params.row) return '';
          if (params.row.isReconciledDetail) return 'reconciled-detail-row';
          if (params.row.isGroupHeader) return 'group-header-row';
          const baseClass = 'group';
          const isSelected = selectedTransactions.includes(params.row.transaction_id);
          return `${baseClass} ${isSelected ? 'checkbox-selected' : ''}`;
        }}
        onRowClick={(params) => {
          if (!params.row) return;
          if (!params.row.isGroupHeader && !params.row.isReconciledDetail) {
            handleRowClick(params.row.transaction_id);
          }
        }}
        getRowHeight={(params) => {
          if (!params.row) return 52;
          if (params.row.isGroupHeader) return 56;
          if (params.row.isReconciledDetail) return 40;
          return 52;
        }}
        
      />
    </div>  
      
     
     {showHidden && hiddenTransactions.length > 0 && (
  <div className="mt-6 w-full">
    <div className="flex items-center mb-3">
      <h3 className="text-lg font-semibold text-gray-800">Hidden Transactions</h3>
      <span className="ml-2 text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded-full">
        {hiddenTransactions.length}
      </span>
    </div>
    
    
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
      <DataGrid
        rows={hiddenTransactions}
        // disableColumnResize
        columns={hiddenColumns}
        hideFooter
        autoHeight
        disableRowSelectionOnClick
        getRowId={(row) => row.id}
        sx={{
          width: '100%',
          minHeight: 300,
          '& .MuiDataGrid-root': {
            border: 'none',
            fontFamily: 'inherit',
          },
          '& .MuiDataGrid-main': {
            border: 'none',
          },
          '& .MuiDataGrid-cell': {
            border: 'none',
            padding: '12px 16px',
            fontSize: '14px',
            '&:focus': {
              outline: 'none',
            },
          },
          '& .MuiDataGrid-columnHeaders': {
            border: 'none',
            backgroundColor: 'rgba(105, 226, 13, 0.1)',
            borderBottom: '2px solid rgba(105, 226, 13, 0.3)',
          },
          '& .lime-header': {
            backgroundColor: 'rgba(105, 226, 13, 0.1)',
            fontWeight: '600',
            fontSize: '14px',
            color: '#374151',
          },
          '& .MuiDataGrid-row': {
            '&:hover': {
              backgroundColor: 'rgba(105, 226, 13, 0.05)',
            },
            '&:nth-of-type(even)': {
              backgroundColor: '#fafafa',
            },
          },
          '& .MuiDataGrid-virtualScroller': {
            overflow: 'auto',
          },
          '& .MuiDataGrid-footerContainer': {
            display: 'none',
          },
        }}
      />
    </div>
  </div>
)}

</div>
 {/* Pagination and Show Hidden button container */}
    <div className="flex justify-between items-center mt-4">
      {/* Show Hidden button on the left */}
      <div className="flex justify-start">
        <Tooltip title={showHidden ? "Hide hidden transactions" : "Show hidden transactions"}>
          <button
            onClick={() => setShowHidden(prev => !prev)}
            className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg flex items-center transition-colors duration-200 shadow-sm border border-gray-200"
          >
            {showHidden ? <VisibilityOff fontSize="small" /> : <Visibility fontSize="small" />}
            <span className="ml-2 text-sm font-medium">
              {showHidden ? 'Hide' : 'Show'} Hidden ({hiddenTransactions?.length || 0})
            </span>
          </button>
        </Tooltip>
      </div>

      {/* Pagination on the right */}
      <div className="flex justify-end">
        <CustomPagination />
      </div>
    </div>

      {/* Transaction Details Modal */}
{selectedTransaction && (
  <Dialog
    open={openModal}
    onClose={closeTransactionModal}
    fullWidth={true}
    maxWidth="sm"
    PaperProps={{
      style: {
        borderRadius: '20px',
        overflow: 'hidden',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
        border: 'none'
      }
    }}
  >
    <DialogTitle sx={{
      background: 'linear-gradient(135deg, #8bc34a 0%, #7cb342 100%)',
      color: 'white',
      padding: '24px',
      fontWeight: '700',
      fontSize: '22px',
      textAlign: 'center',
      position: 'relative',
      boxShadow: '0 8px 16px -4px rgba(139, 195, 74, 0.3)'
    }}>
      Transaction Detail
      
      {/* Trash Icon - Top Left */}
      <IconButton
        aria-label="delete"
        onClick={() => {
          if (window.confirm('Are you sure you want to delete this transaction?')) {
            dispatch({
              type: 'transactions/deleteTransaction',
              payload: selectedTransaction.transaction_id
            });
            closeTransactionModal();
          }
        }}
        sx={{
          position: 'absolute',
          left: 16,
          top: 16,
          color: 'white',
          backgroundColor: 'rgba(255, 255, 255, 0.15)',
          borderRadius: '50%',
          padding: '8px',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.25)',
            transform: 'scale(1.1)'
          },
          transition: 'all 0.3s ease-in-out'
        }}
      >
        <DeleteIcon fontSize="small" />
      </IconButton>

      {/* Close Icon - Top Right */}
      <IconButton
        aria-label="close"
        onClick={closeTransactionModal}
        sx={{
          position: 'absolute',
          right: 16,
          top: 16,
          color: 'white',
          backgroundColor: 'rgba(255, 255, 255, 0.15)',
          borderRadius: '50%',
          padding: '8px',
          '&:hover': {
            backgroundColor: 'rgba(255, 255, 255, 0.25)',
            transform: 'scale(1.1) rotate(90deg)'
          },
          transition: 'all 0.3s ease-in-out'
        }}
      >
        <CloseIcon fontSize="small" />
      </IconButton>
    </DialogTitle>
    
    <DialogContent sx={{ 
      padding: '32px 24px', 
      backgroundColor: '#f8fafc'
    }}>
 <Tabs
            value={activeTab}
            onChange={(e, newValue) => {
              if (newValue === 1) {
                if (!receiptTransactionIds.includes(selectedTransaction.transaction_id)) {
                  setShowEmbeddedReceiptUpload(true);
                  setActiveTab(newValue);
                } else {
                  setShowEmbeddedReceiptUpload(false);
                  setActiveTab(newValue);
                }
              } else {
                setShowEmbeddedReceiptUpload(false);
                setActiveTab(newValue);
              }
            }}
            indicatorColor="primary"
            textColor="primary"
            variant="fullWidth"
            sx={{ borderBottom: 1, borderColor: 'divider', backgroundColor: '#e6f4ea' }}
          >
    <Tab label="Details" />
 <Tab 
          label="Receipt" 
          // disabled={!receiptTransactionIds.includes(selectedTransaction.transaction_id) && !showEmbeddedReceiptUpload}
        />
            <Tab label="Split" />
  </Tabs>
   {activeTab === 0 && (
      <div className="w-full space-y-4">
        {/* Date Field */}
        <div className="grid grid-cols-5 gap-4 items-center">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <CalendarTodayIcon sx={{ fontSize: 18, color: '#1976d2' }} />
            </div>
            <span className="font-semibold text-gray-700 text-sm">Date</span>
          </div>
          <div className="col-span-3">
            <TextField
              type="date"
value={selectedTransaction?.date || ''}
              onChange={(e) => handleFieldChange('date', e.target.value)}
              variant="outlined"
              size="small"
              fullWidth
              InputLabelProps={{
                shrink: true,
              }}
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  backgroundColor: '#f8fafc',
                  borderColor: '#e2e8f0',
                  height: '36px',
                  '&:hover': {
                    borderColor: '#8bc34a'
                  },
                  '&.Mui-focused': {
                    borderColor: '#8bc34a'
                  }
                },
                '& .MuiInputBase-input': {
                  fontWeight: 500,
                  color: '#1e293b',
                  fontSize: '14px'
                }
              }}
            />
          </div>
        </div>

        {/* Description Field */}
        <div className="grid grid-cols-5 gap-4 items-center">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <ReceiptIcon sx={{ fontSize: 18, color: '#7b1fa2' }} />
            </div>
            <span className="font-semibold text-gray-700 text-sm">Description</span>
          </div>
          <div className="col-span-3">
            <TextField
value={selectedTransaction?.name || ''}
              onChange={(e) => handleFieldChange('name', e.target.value)}
              variant="outlined"
              size="small"
              placeholder="Enter description"
              fullWidth
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  backgroundColor: '#f8fafc',
                  borderColor: '#e2e8f0',
                  height: '36px',
                  '&:hover': {
                    borderColor: '#7b1fa2'
                  },
                  '&.Mui-focused': {
                    borderColor: '#7b1fa2'
                  }
                },
                '& .MuiInputBase-input': {
                  fontWeight: 500,
                  color: '#1e293b',
                  fontSize: '14px'
                }
              }}
            />
          </div>
        </div>

        {/* Category Field */}
        <div className="grid grid-cols-5 gap-4 items-center">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <CategoryIcon sx={{ fontSize: 18, color: '#f57c00' }} />
            </div>
            <span className="font-semibold text-gray-700 text-sm">Category</span>
          </div>
          <div className="col-span-3">
       <select
value={selectedTransaction?.subcategory || ''}
  onChange={(e) => handleFieldChange('subcategory', e.target.value)}
  className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#f57c00] focus:border-[#f57c00]"
>
  <option value="">Select a category & subcategory</option>
  {categories.map((cat) => (
    <optgroup key={cat.id} label={cat.category}>
      {subcategories
        .filter((sub) => sub.categoryId === cat.id)
        .map((sub) => (
          <option key={sub.id} value={sub.subCategory}>
            {sub.subCategory}
          </option>
        ))}
    </optgroup>
  ))}
</select>

  </div>
</div>
       
        {/* Amount Field - Highlighted */}
        <div className="grid grid-cols-5 gap-4 items-center">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center mr-3 flex-shrink-0 shadow-sm">
              <AttachMoneyIcon sx={{ fontSize: 18, color: 'white' }} />
            </div>
            <span className="font-bold text-gray-800 text-sm">Amount</span>
          </div>
          <div className="col-span-3">
            <TextField
              type="number"
              value={selectedTransaction?.amount || ''}
              onChange={(e) => handleFieldChange('amount', e.target.value)}
              variant="outlined"
              size="small"
              fullWidth
              InputProps={{
                startAdornment: <InputAdornment position="start" sx={{ color: '#8bc34a', fontWeight: 'bold' }}>$</InputAdornment>,
              }}
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  backgroundColor: 'white',
                  borderColor: '#8bc34a',
                  height: '36px',
                  '&:hover': {
                    borderColor: '#7cb342'
                  },
                  '&.Mui-focused': {
                    borderColor: '#7cb342'
                  }
                },
                '& .MuiInputBase-input': {
                  fontWeight: 'bold',
                  fontSize: '16px',
                  color: '#7cb342'
                }
              }}
            />
          </div>
        </div>

        {/* Tax Field */}
        <div className="grid grid-cols-5 gap-4 items-center">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <ReceiptLongIcon sx={{ fontSize: 18, color: '#f59e0b' }} />
            </div>
            <span className="font-semibold text-gray-700 text-sm">Tax</span>
          </div>
          <div className="col-span-3">
            <TextField
              type="number"
              value={selectedTransaction?.tax || ''}
              onChange={(e) => handleFieldChange('tax', e.target.value)}
              variant="outlined"
              size="small"
              placeholder="0.00"
              fullWidth
              InputProps={{
                startAdornment: <InputAdornment position="start" sx={{ color: '#f59e0b', fontWeight: 'bold' }}>$</InputAdornment>,
              }}
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  backgroundColor: '#f8fafc',
                  borderColor: '#e2e8f0',
                  height: '36px',
                  '&:hover': {
                    borderColor: '#f59e0b'
                  },
                  '&.Mui-focused': {
                    borderColor: '#f59e0b'
                  }
                },
                '& .MuiInputBase-input': {
                  fontWeight: 500,
                  color: '#1e293b',
                  fontSize: '14px'
                }
              }}
            />
          </div>
        </div>

        {/* Account Field */}
       <div className="grid grid-cols-5 gap-4 items-center">
  <div className="flex items-center col-span-2">
    <div className="w-10 h-10 bg-indigo-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
      <AccountBalanceIcon sx={{ fontSize: 18, color: '#3f51b5' }} />
    </div>
    <span className="font-semibold text-gray-700 text-sm">Account</span>
  </div>

  <div className="col-span-3">
    <select
      value={selectedTransaction?.bank}
      onChange={(e) => handleFieldChange('bank', e.target.value)}
      className="w-full p-2 border border-gray-300 rounded-md focus:ring-[#3f51b5] focus:border-[#3f51b5]"
      required
      disabled={loadingAccounts}
    >
      <option value="">Select an account</option>
      {accounts.map((account) => (
        <option key={account.accountId} value={account.accountId}>
          {`${account.accountName} (${account.accountMask})`}
        </option>
      ))}
    </select>
    {loadingAccounts && (
      <p className="text-sm text-gray-500">Loading accounts...</p>
    )}
  </div>
</div>

 {/* Tag Field - NEW */}
        <div className="grid grid-cols-5 gap-4 items-center">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <LocalOfferIcon sx={{ fontSize: 18, color: '#e91e63' }} />
            </div>
            <span className="font-semibold text-gray-700 text-sm">Tag</span>
          </div>
          <div className="col-span-3">
            <TextField
              value={selectedTransaction?.tag || ''}
              onChange={(e) => handleFieldChange('tag', e.target.value)}
              variant="outlined"
              size="small"
              placeholder="Add tag"
              fullWidth
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  backgroundColor: '#f8fafc',
                  borderColor: '#e2e8f0',
                  height: '36px',
                  '&:hover': {
                    borderColor: '#e91e63'
                  },
                  '&.Mui-focused': {
                    borderColor: '#e91e63'
                  }
                },
                '& .MuiInputBase-input': {
                  fontWeight: 500,
                  color: '#1e293b',
                  fontSize: '14px'
                }
              }}
            />
          </div>
        </div>


        {/* Notes Field */}
        <div className="grid grid-cols-5 gap-4 items-start">
          <div className="flex items-center col-span-2">
            <div className="w-10 h-10 bg-teal-100 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <NotesIcon sx={{ fontSize: 18, color: '#0d9488' }} />
            </div>
            <span className="font-semibold text-gray-700 text-sm">Notes</span>
          </div>
          <div className="col-span-3">
            <TextField
              value={selectedTransaction?.notes || ''}
              onChange={(e) => handleFieldChange('notes', e.target.value)}
              variant="outlined"
              size="small"
              placeholder="Add notes..."
              multiline
              rows={3}
              fullWidth
              sx={{ 
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  backgroundColor: '#f8fafc',
                  borderColor: '#e2e8f0',
                  '&:hover': {
                    borderColor: '#0d9488'
                  },
                  '&.Mui-focused': {
                    borderColor: '#0d9488'
                  }
                },
                '& .MuiInputBase-input': {
                  fontWeight: 500,
                  color: '#1e293b',
                  fontSize: '14px'
                }
              }}
            />
          </div>
        </div>

        {/* Hide from Budget Checkbox - Moved to left */}
        <div className="flex items-center">
          <FormControlLabel
            control={
              <Checkbox
                checked={selectedTransaction?.hideFromBudget || false}
                onChange={(e) => handleFieldChange('hideFromBudget', e.target.checked)}
                sx={{
                  color: '#dc2626',
                  '&.Mui-checked': {
                    color: '#dc2626',
                  },
                  '& .MuiSvgIcon-root': {
                    fontSize: 20,
                  }
                }}
              />
            }
            label={
              <span className="text-sm text-gray-600 font-medium">
                Exclude from budget calculations
              </span>
            }
            sx={{
              margin: 0,
              '& .MuiFormControlLabel-label': {
                fontSize: '14px'
              }
            }}
          />
        </div>

        {/* Hide Transaction Checkbox - Moved to left */}
        <div className="flex items-center">
          <FormControlLabel
            control={
              <Checkbox
                checked={selectedTransaction?.hidden || false}
                onChange={(e) => handleFieldChange('hidden', e.target.checked)}
                sx={{
                  color: '#6b7280',
                  '&.Mui-checked': {
                    color: '#6b7280',
                  },
                  '& .MuiSvgIcon-root': {
                    fontSize: 20,
                  }
                }}
              />
            }
            label={
              <span className="text-sm text-gray-600 font-medium">
                Hide this transaction from view
              </span>
            }
            sx={{
              margin: 0,
              '& .MuiFormControlLabel-label': {
                fontSize: '14px'
              }
            }}
          />
        </div>
         <div className="pt-6 flex justify-center">
        <Button
          variant="contained"
          onClick={handleSaveChanges}
          startIcon={<SaveIcon />}
          sx={{
            textTransform: 'none',
            fontWeight: '600',
            borderRadius: '10px',
            padding: '12px 28px',
            fontSize: '15px',
            background: 'linear-gradient(135deg, #8bc34a 0%, #7cb342 100%)',
            boxShadow: '0 4px 14px 0 rgba(139, 195, 74, 0.3)',
            '&:hover': {
              boxShadow: '0 6px 20px 0 rgba(139, 195, 74, 0.4)',
              transform: 'translateY(-1px)',
              background: 'linear-gradient(135deg, #7cb342 0%, #689f38 100%)'
            },
            transition: 'all 0.2s ease-in-out'
          }}
        >
          Save Changes
        </Button>
      </div>
      </div>
      )}

      {/* Save Button - Centered */}
     
  {activeTab === 1 && (
            <Box p={3}>
              {receiptTransactionIds.includes(selectedTransaction.transaction_id) ? (
                <ReceiptModal
                  receiptData={selectedReceipt}
                  onClose={() => setActiveTab(0)}
                  handleFieldDoubleClick={handleFieldDoubleClick}
                  isEmbedded
                />
              ) : (
                <ReceiptHandler
                  isEmbedded={true}
                  onClose={() => {
                    setShowEmbeddedReceiptUpload(false);
                    setActiveTab(0);
                  }}
                  onSave={() => {
                    dispatch(fetchReceiptTransactionIdsRequest());
                    setShowEmbeddedReceiptUpload(false);
                    setActiveTab(1);
                  }}
                />
              )}
            </Box>
          )}
{activeTab === 2 && (
    <Box p={3}>
      <SplitTransactionPopup
        open={true}
        onClose={() => setActiveTab(0)}
        transaction={selectedTransaction}
        onSubmitSplit={handleSubmitSplit}
        isEmbedded
      />
    </Box>
    )}
    </DialogContent>
  </Dialog>
)}

     
    </div>
  );
};

export default TransactionPage1;
