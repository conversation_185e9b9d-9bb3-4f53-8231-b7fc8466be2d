import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  TextField,
  Button,
  Typography,
  CircularProgress,
  Link,
  Paper,
  InputAdornment,
  IconButton
} from '@mui/material';
import EmailIcon from '@mui/icons-material/Email';
import PhoneIcon from '@mui/icons-material/Phone';
import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL;

const ForgotPassword = () => {
  const [identifier, setIdentifier] = useState('');
  const [identifierType, setIdentifierType] = useState('email'); // 'email' or 'phone'
  const [otp, setOtp] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [otpSent, setOtpSent] = useState(false);
  const [otpVerified, setOtpVerified] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [successMessage, setSuccessMessage] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [resendDisabled, setResendDisabled] = useState(false);
  const [resendTimer, setResendTimer] = useState(60);
  
  const navigate = useNavigate();

  // Timer effect for OTP resend cooldown
  useEffect(() => {
    let timer;
    if (resendDisabled && resendTimer > 0) {
      timer = setInterval(() => {
        setResendTimer((prev) => {
          if (prev <= 1) {
            setResendDisabled(false);
            clearInterval(timer);
            return 60;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [resendDisabled, resendTimer]);

  // Detect if input is email or phone
  const detectIdentifierType = (value) => {
    // Simple email regex
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    // Simple phone regex (digits only, possibly with + at start)
    const phoneRegex = /^(\+)?[0-9]{10,15}$/;
    
    if (emailRegex.test(value)) {
      return 'email';
    } else if (phoneRegex.test(value)) {
      return 'phone';
    }
    
    // Default to previous type or email if can't determine
    return identifierType;
  };

  const handleIdentifierChange = (e) => {
    const value = e.target.value;
    setIdentifier(value);
    setIdentifierType(detectIdentifierType(value));
    setError('');
  };

  const handleToggleIdentifierType = () => {
    setIdentifierType(prev => prev === 'email' ? 'phone' : 'email');
    setIdentifier('');
    setError('');
  };

  const handleOtpChange = (e) => {
    const value = e.target.value;
    if (/^\d{0,6}$/.test(value)) {
      setOtp(value);
      setError('');
    }
  };

  const handlePasswordChange = (e) => {
    setPassword(e.target.value);
    setPasswordError('');
  };

  const handleConfirmPasswordChange = (e) => {
    setConfirmPassword(e.target.value);
    setPasswordError('');
  };

  const validateIdentifier = () => {
    if (!identifier) {
      setError('Please enter your email or phone number');
      return false;
    }

    if (identifierType === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(identifier)) {
        setError('Please enter a valid email address');
        return false;
      }
    } else {
      const phoneRegex = /^(\+)?[0-9]{10,15}$/;
      if (!phoneRegex.test(identifier)) {
        setError('Please enter a valid phone number');
        return false;
      }
    }

    return true;
  };

  const handleSendOtp = async () => {
    if (!validateIdentifier()) {
      return;
    }

    setLoading(true);
    setError('');
    setSuccessMessage('');

    try {
      const endpoint = `${API_URL}/pennypal/api/v1/auth/forgot-password/generate-otp`;
      const payload = identifierType === 'email' 
        ? { emailId: identifier } 
        : { phoneNumber: identifier };
      
      await axios.post(endpoint, payload);
      setOtpSent(true);
      setResendDisabled(true);
      setResendTimer(60);
      setSuccessMessage(`One Time Passcode sent to your ${identifierType}`);
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to send One Time Passcode');
    } finally {
      setLoading(false);
    }
  };

  const handleVerifyOtp = async () => {
    if (otp.length !== 6) {
      setError('One Time Passcode must be 6 digits');
      return;
    }

    setLoading(true);
    setError('');
    setSuccessMessage('');

    try {
      const endpoint = `${API_URL}/pennypal/api/v1/auth/forgot-password/verify-otp`;
      const payload = identifierType === 'email'
        ? { email: identifier, otp: parseInt(otp, 10) }
        : { phoneNumber: identifier, otp: parseInt(otp, 10) };
      
      await axios.post(endpoint, payload);
      setOtpVerified(true);
      setSuccessMessage('OTP verified successfully. Please set a new password.');
    } catch (error) {
      setError(error.response?.data?.message || 'Invalid One Time Passcode');
    } finally {
      setLoading(false);
    }
  };

  const handleResetPassword = async () => {
    if (password !== confirmPassword) {
      setPasswordError('Passwords do not match');
      return;
    }

    if (password.length < 8) {
      setPasswordError('Password must be at least 8 characters long');
      return;
    }

    setLoading(true);
    setError('');
    setSuccessMessage('');

    try {
      const endpoint = `${API_URL}/pennypal/api/v1/auth/forgot-password/reset`;
      const payload = identifierType === 'email'
        ? { emailId: identifier, password: password }
        : { phoneNumber: identifier, password: password };
      
      await axios.post(endpoint, payload);
      setSuccessMessage('Password reset successfully. Redirecting to login page...');
      
      // Redirect to login page after 3 seconds
      setTimeout(() => {
        navigate('/login');
      }, 3000);
    } catch (error) {
      setError(error.response?.data?.message || 'Failed to reset password');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Container component="main" maxWidth="xs">
      <Paper elevation={3} sx={{ mt: 8, p: 4, display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
        <Typography component="h1" variant="h5" sx={{ mb: 3 }}>
          {otpVerified ? 'Set New Password' : 'Forgot Password'}
        </Typography>

        {!otpVerified ? (
          <Box sx={{ width: '100%' }}>
            <TextField
              margin="normal"
              required
              fullWidth
              id="identifier"
              label={identifierType === 'email' ? "Email Address" : "Phone Number"}
              name="identifier"
              autoComplete={identifierType === 'email' ? "email" : "tel"}
              autoFocus
              value={identifier}
              onChange={handleIdentifierChange}
              disabled={otpSent && loading}
              sx={{ mb: 2 }}
              placeholder={identifierType === 'email' ? "<EMAIL>" : "+****************"}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={handleToggleIdentifierType}
                      edge="end"
                      title={`Switch to ${identifierType === 'email' ? 'phone' : 'email'}`}
                    >
                      {identifierType === 'email' ? <PhoneIcon /> : <EmailIcon />}
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />

            {!otpSent ? (
              <Button
                fullWidth
                variant="contained"
                color="primary"
                onClick={handleSendOtp}
                disabled={loading}
                sx={{ mb: 2 }}
              >
                {loading ? <CircularProgress size={24} /> : 'Send One Time Passcode'}
              </Button>
            ) : (
              <>
                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="otp"
                  label="Enter One Time Passcode"
                  name="otp"
                  value={otp}
                  onChange={handleOtpChange}
                  inputProps={{ maxLength: 6 }}
                  sx={{ mb: 2 }}
                />
                <Button
                  fullWidth
                  variant="contained"
                  color="primary"
                  onClick={handleVerifyOtp}
                  disabled={loading || otp.length !== 6}
                  sx={{ mb: 2 }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Verify One Time Passcode'}
                </Button>
                <Button
                  fullWidth
                  variant="outlined"
                  color="primary"
                  onClick={handleSendOtp}
                  disabled={loading || resendDisabled}
                  sx={{ mb: 2 }}
                >
                  {loading ? <CircularProgress size={24} /> : 
                    `Resend One Time Passcode ${resendDisabled ? `(${resendTimer}s)` : ''}`}
                </Button>
              </>
            )}
          </Box>
        ) : (
          <Box sx={{ width: '100%' }}>
            <TextField
              margin="normal"
              required
              fullWidth
              name="password"
              label="New Password"
              type="password"
              id="password"
              value={password}
              onChange={handlePasswordChange}
              sx={{ mb: 2 }}
            />
            <TextField
              margin="normal"
              required
              fullWidth
              name="confirmPassword"
              label="Confirm New Password"
              type="password"
              id="confirmPassword"
              value={confirmPassword}
              onChange={handleConfirmPasswordChange}
              sx={{ mb: 2 }}
            />
            <Button
              fullWidth
              variant="contained"
              color="primary"
              onClick={handleResetPassword}
              disabled={loading}
              sx={{ mb: 2 }}
            >
              {loading ? <CircularProgress size={24} /> : 'Reset Password'}
            </Button>
            {passwordError && (
              <Typography color="red" sx={{ mt: 1, textAlign: 'center' }}>
                {passwordError}
              </Typography>
            )}
          </Box>
        )}

        {error && (
          <Typography color="red" sx={{ mt: 1, textAlign: 'center' }}>
            {error}
          </Typography>
        )}
        
        {successMessage && (
          <Typography color="green" sx={{ mt: 1, textAlign: 'center' }}>
            {successMessage}
          </Typography>
        )}

        <Link component={RouterLink} to="/login" sx={{ mt: 2 }}>
          Return to Login
        </Link>
      </Paper>
    </Container>
  );
};

export default ForgotPassword;