import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { GoogleOAuthProvider, GoogleLogin } from '@react-oauth/google';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';
import { Link as RouterLink, useNavigate } from 'react-router-dom';

import {
  registrationRequest,
  signInRequest,
  googleSignInRequest,
  otpVerifyRequest,
  bindFormValueToUser
}  from '../../../../logic/redux/authSlice';

import axios from 'axios';

const API_URL = import.meta.env.VITE_API_URL;

import { Box, Paper, TextField, Button, Container, Typography, Link, Collapse, CircularProgress } from '@mui/material';
import { 
  Checkbox,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControlLabel,
  IconButton,
  InputAdornment,
  Card,
  CardContent,
  Fade,
  Slide,
  Chip
} from '@mui/material';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import AppleIcon from '@mui/icons-material/Apple';
import PersonIcon from '@mui/icons-material/Person';
import EmailIcon from '@mui/icons-material/Email';
import LockIcon from '@mui/icons-material/Lock';
import PhoneIcon from '@mui/icons-material/Phone';
import VisibilityIcon from '@mui/icons-material/Visibility';
import VisibilityOffIcon from '@mui/icons-material/VisibilityOff';
import { styled, keyframes } from '@mui/material/styles';

// Enhanced animations
const float = keyframes`
  0% { transform: translateY(0px) rotate(0deg); opacity: 0.8; }
  50% { transform: translateY(-20px) rotate(180deg); opacity: 0.4; }
  100% { transform: translateY(0px) rotate(360deg); opacity: 0.8; }
`;

const shimmer = keyframes`
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
`;

const slideInFromRight = keyframes`
  0% { opacity: 0; transform: translateX(50px); }
  100% { opacity: 1; transform: translateX(0); }
`;

const slideInFromLeft = keyframes`
  0% { opacity: 0; transform: translateX(-50px); }
  100% { opacity: 1; transform: translateX(0); }
`;

// Styled components
const StyledCard = styled(Card)(({ theme }) => ({
  background: 'rgba(255, 255, 255, 0.95)',
  backdropFilter: 'blur(20px)',
  borderRadius: '24px',
  border: '1px solid rgba(255, 255, 255, 0.2)',
  boxShadow: '0 20px 40px rgba(0, 0, 0, 0.1)',
  padding: '2rem',
  transition: 'all 0.3s ease',
  animation: `${slideInFromRight} 0.8s ease-out`,
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: '0 25px 50px rgba(0, 0, 0, 0.15)',
  }
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: '16px',
    background: 'rgba(255, 255, 255, 0.9)',
    transition: 'all 0.3s ease',
    '&:hover': {
      background: 'rgba(255, 255, 255, 1)',
      transform: 'translateY(-1px)',
    },
    '&.Mui-focused': {
      background: 'rgba(255, 255, 255, 1)',
      transform: 'translateY(-2px)',
      boxShadow: '0 8px 25px rgba(139, 195, 74, 0.2)',
    },
  },
  '& .MuiInputLabel-root': {
    color: '#666',
    fontWeight: 500,
  },
  '& .MuiOutlinedInput-input': {
    padding: '16px 14px',
  },
}));

const StyledButton = styled(Button)(({ theme }) => ({
  borderRadius: '16px',
  padding: '14px 28px',
  fontWeight: 600,
  fontSize: '1rem',
  textTransform: 'none',
  transition: 'all 0.3s ease',
  background: 'linear-gradient(135deg, #8bc34a 0%, #689f38 100%)',
  boxShadow: '0 8px 25px rgba(139, 195, 74, 0.3)',
  '&:hover': {
    background: 'linear-gradient(135deg, #689f38 0%, #558b2f 100%)',
    transform: 'translateY(-2px)',
    boxShadow: '0 12px 35px rgba(139, 195, 74, 0.4)',
  },
  '&:active': {
    transform: 'translateY(0)',
  },
}));

const StyledOutlinedButton = styled(Button)(({ theme }) => ({
  borderRadius: '16px',
  padding: '14px 28px',
  fontWeight: 600,
  fontSize: '1rem',
  textTransform: 'none',
  border: '2px solid #8bc34a',
  color: '#8bc34a',
  background: 'transparent',
  transition: 'all 0.3s ease',
  '&:hover': {
    background: 'rgba(139, 195, 74, 0.1)',
    transform: 'translateY(-2px)',
    boxShadow: '0 8px 25px rgba(139, 195, 74, 0.2)',
  },
}));

const GradientBackground = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'showSignUp'
})(({ showSignUp }) => ({
  background: `
    linear-gradient(135deg, 
      ${showSignUp ? '#e8f5e8' : '#f1f8e9'} 0%, 
      ${showSignUp ? '#c8e6c9' : '#dcedc8'} 35%, 
      ${showSignUp ? '#a5d6a7' : '#c5e1a5'} 70%, 
      ${showSignUp ? '#81c784' : '#aed581'} 100%
    )
  `,
  position: 'relative',
  overflow: 'hidden',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    background: `
      radial-gradient(circle at 20% 20%, rgba(139, 195, 74, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 80%, rgba(104, 159, 56, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 60%, rgba(255, 255, 255, 0.1) 0%, transparent 50%)
    `,
    animation: `${float} 8s ease-in-out infinite`,
  },
  '&::after': {
    content: '""',
    position: 'absolute',
    top: '-50%',
    left: '-50%',
    width: '200%',
    height: '200%',
    background: `
      repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255, 255, 255, 0.05) 2px,
        rgba(255, 255, 255, 0.05) 4px
      )
    `,
    animation: `${float} 12s linear infinite`,
    pointerEvents: 'none',
  }
}));

const FloatingElement = styled('div')(({ size = 8, top = '20%', left = '15%', delay = '0s' }) => ({
  position: 'absolute',
  width: `${size}px`,
  height: `${size}px`,
  background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.8), rgba(139, 195, 74, 0.6))',
  borderRadius: '50%',
  top,
  left,
  animation: `${float} 6s ease-in-out infinite ${delay}`,
  pointerEvents: 'none',
  zIndex: 1,
}));

const WelcomeSection = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'showSignUp'
})(({ showSignUp }) => ({
  zIndex: 2,
  position: 'relative',
}));

const SignIn = ({ onLoginSuccess }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        width: '100vw',
        height: '100vh',
        position: 'relative',
      }}
    >
      <LoginPage onLoginSuccess={onLoginSuccess} />
    </Box>
  );
};

const LoginPage = ({ onLoginSuccess }) => {
  const [showSignUp, setShowSignUp] = useState(false);
  const [googleError, setGoogleError] = useState('');
  const [otpDialogOpen, setOtpDialogOpen] = useState(false);
  const [otpEmail, setOtpEmail] = useState('');
  const [otpIdentifier, setOtpIdentifier] = useState('');
  const [identifierType, setIdentifierType] = useState('email');
  const [otp, setOtp] = useState('');
  const [otpError, setOtpError] = useState('');
  const [otpLoading, setOtpLoading] = useState(false);
  const [resendDisabled, setResendDisabled] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [rememberMe, setRememberMe] = useState(false);
  const [useOtp, setUseOtp] = useState(false);
  const [showPassword, setShowPassword] = useState(false);

  const user = useSelector((state) => state.auth.user);
  const error = useSelector((state) => state.auth.error);
  const loading = useSelector((state) => state.auth.loading);
  const showLogin = useSelector((state) => state.auth.showLogin);
  const registrationData = useSelector((state) => state.auth.data);
  const isUserAuthenticated = useSelector((state) => state.auth.isUserAuthenticated);

  if (isUserAuthenticated) {
    console.log("Printing user....");
    console.log(user);
    onLoginSuccess();
  }

  const [formData, setFormData] = useState({
    email: '',
    mobile: '',
    password: '',
    confirmPassword: '',
    otp: '',
    rememberMe: false,
  });

  const dispatch = useDispatch();

  const detectIdentifierType = (value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const phoneRegex = /^(\+)?[0-9]{10,15}$/;
    if (emailRegex.test(value)) {
      return 'email';
    } else if (phoneRegex.test(value)) {
      return 'phone';
    }
    return identifierType;
  };

  const handleOtpIdentifierChange = (e) => {
    const value = e.target.value;
    setOtpIdentifier(value);
    setIdentifierType(detectIdentifierType(value));
    setOtpError('');
  };

  const handleToggleIdentifierType = () => {
    setIdentifierType(prev => prev === 'email' ? 'phone' : 'email');
    setOtpIdentifier('');
    setOtpError('');
  };

  const validateIdentifier = () => {
    if (!otpIdentifier) {
      setOtpError('Please enter your email or phone number');
      return false;
    }

    if (identifierType === 'email') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(otpIdentifier)) {
        setOtpError('Please enter a valid email address');
        return false;
      }
    } else {
      const phoneRegex = /^(\+)?[0-9]{10,15}$/;
      if (!phoneRegex.test(otpIdentifier)) {
        setOtpError('Please enter a valid phone number');
        return false;
      }
    }
    return true;
  };

  useEffect(() => {
    let timer;
    if (resendDisabled && resendTimer > 0) {
      timer = setInterval(() => {
        setResendTimer((prev) => {
          if (prev <= 1) {
            setResendDisabled(false);
            clearInterval(timer);
            return 60;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(timer);
  }, [resendDisabled, resendTimer]);

  const handleRememberMeChange = (e) => {
    const checked = e.target.checked;
    setRememberMe(checked);
    dispatch(bindFormValueToUser({ name: 'rememberMe', value: checked }));
  };

  const toggleForm = () => {
    setShowSignUp(!showSignUp);
  };

  const handleChange = (e) => {
    console.log("Onchange is called .......")
    console.log(user)
    const { name, value } = e.target;
    console.log("=Name==>" + name + " Value ==>" + value);
    console.log(user);
    dispatch(bindFormValueToUser({ name, value }));
  };

  const handlePhoneChange = (value, data) => {
    console.log("Onchange is called .......")
    console.log(user)
    console.log("=Name==>" + "mobileNumber" + " Value ==>" + value);
    console.log(user);
    dispatch(bindFormValueToUser({ name: 'phoneNumber', value }));
  };

  const handleChange1 = (e) => {
    console.log("Onchange is called .......")
    const { name, value } = e.target;
    console.log("=Name==>" + name + " Value ==>" + value);
    console.log(user);
    dispatch(bindFormValueToUser({ name, value }));
  };

  const handleSignIn = (e) => {
    e.preventDefault();
    dispatch(signInRequest(user));
  }

  const handleSignUp = (e) => {
    e.preventDefault();
    dispatch(registrationRequest(user));
  }

  const handleOtpEmailChange = (e) => {
    setOtpEmail(e.target.value);
    setOtpError('');
  };

  const handleOtpChange = (e) => {
    const value = e.target.value;
    if (/^\d{0,6}$/.test(value)) {
      setOtp(value);
      setOtpError('');
    } else {
      setOtpError('OTP must be a 6-digit number');
    }
  };

  const handleSendOtp = async () => {
    if (!validateIdentifier()) {
      return;
    }
    setOtpLoading(true);
    try {
      const endpoint = `${API_URL}/pennypal/api/v1/auth/signin/otp/generate`;
      const payload = identifierType === 'email'
        ? { ...user, emailId: otpIdentifier }
        : { ...user, phoneNumber: otpIdentifier };
      await axios.post(endpoint, payload);
      setResendDisabled(true);
      setResendTimer(60);
      setOtp('');
      setOtpError('');
    } catch (error) {
      console.error('OTP resend error', error);
      setOtpError(error.response?.data?.message || 'Failed to send One Time Passcode');
    } finally {
      setOtpLoading(false);
    }
  };

  const handleOtpSubmit = async (e) => {
    if (otp.length !== 6) {
      setOtpError('One Time Passcode must be 6 digits');
      return;
    }
    try {
      const payload = identifierType === 'email'
        ? { ...user, emailId: otpIdentifier, otp: parseInt(otp), rememberMe }
        : { ...user, phoneNumber: otpIdentifier, otp: parseInt(otp), rememberMe };
      console.log(payload);
      e.preventDefault();
      dispatch(otpVerifyRequest(payload));
    } catch (error) {
      console.error('OTP verification error', error);
      setOtpError('Invalid One Time Passcode');
    }
  };

  const handleAppleLogin = () => {
    window.location.href = `https://appleid.apple.com/auth/authorize?` +
      new URLSearchParams({
        response_type: "code",
        client_id: "<YOUR_CLIENT_ID>",
        redirect_uri: `${API_URL}/pennypal/api/v1/auth/register/apple`,
        response_mode: "form_post",
        scope: "name email",
      }).toString();
  };

  return (
    <GradientBackground
      showSignUp={showSignUp}
      sx={{
        display: 'flex',
        width: '100vw',
        height: '100vh',
        position: 'relative',
      }}
    >
      {/* Floating Elements */}
      <FloatingElement size={12} top="15%" left="10%" delay="0s" />
      <FloatingElement size={8} top="25%" left="25%" delay="2s" />
      <FloatingElement size={10} top="45%" left="15%" delay="4s" />
      <FloatingElement size={6} top="65%" left="30%" delay="6s" />
      <FloatingElement size={14} top="75%" left="8%" delay="8s" />

      {/* Form Section */}
      <WelcomeSection
        showSignUp={showSignUp}
        sx={{
          width: '100%',
          height: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          padding: 4,
          position: 'relative',
          zIndex: 3,
        }}
      >
        <StyledCard sx={{ width: '100%', maxWidth: 900, minHeight: '80vh', display: 'flex' }}>
          {/* Login Form */}
          {(!showSignUp || registrationData != null) && (
            <Fade in timeout={800}>
              <Box sx={{ width: '100%', display: 'flex' }}>
                {/* Left Side - PennyPal Branding */}
                <Box sx={{ 
                  width: '45%', 
                  padding: 3, 
                  display: 'flex', 
                  flexDirection: 'column', 
                  justifyContent: 'center',
                  borderRight: '1px solid rgba(139, 195, 74, 0.2)' 
                }}>
                  <Typography
                    variant="h2"
                    sx={{
                      fontWeight: 800,
                      background: 'linear-gradient(135deg, #2e7d32 0%, #4caf50 50%, #8bc34a 100%)',
                      backgroundClip: 'text',
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      fontSize: { xs: '2.5rem', md: '3rem' },
                      letterSpacing: '2px',
                      marginBottom: 2,
                      textAlign: 'center',
                    }}
                  >
                    PennyPal
                  </Typography>
                  <Typography
                    variant="h6"
                    sx={{
                      color: '#666',
                      fontWeight: 500,
                      fontSize: '1.1rem',
                      letterSpacing: '1px',
                      marginBottom: 3,
                      textAlign: 'center',
                    }}
                  >
                    Your Financial Companion
                  </Typography>
                  
                  {/* Financial Dashboard Illustration */}
                  <Box sx={{ display: 'flex', justifyContent: 'center', marginBottom: 3 }}>
                    <svg xmlns="http://www.w3.org/2000/svg" width="280" height="220" viewBox="0 0 564.46765 449.26526" role="img">
                      <path d="M239.34575,431.62993c2.06592,.12937,3.20768-2.43737,1.64468-3.93333l-.1555-.61819c.02047-.04951,.04105-.09897,.06178-.14839,2.08924-4.9818,9.16992-4.94742,11.24139,.04177,1.83859,4.42817,4.17942,8.86389,4.75579,13.54594,.25838,2.0668,.14213,4.17236-.31648,6.20047,4.30807-9.41059,6.57515-19.68661,6.57515-30.02077,0-2.59652-.14213-5.19301-.43275-7.78295-.239-2.11854-.56839-4.2241-.99471-6.31034-2.30575-11.2772-7.29852-22.01825-14.50012-30.98962-3.46197-1.89248-6.34906-4.85065-8.09295-8.39652-.62649-1.27891-1.11739-2.65462-1.34991-4.05618,.39398,.05168,1.48556-5.94866,1.18841-6.3168,.54906-.83317,1.53178-1.24733,2.13144-2.06034,2.98232-4.04341,7.0912-3.33741,9.23621,2.15727,4.58224,2.31266,4.62659,6.14806,1.81495,9.83683-1.78878,2.34682-2.03456,5.52233-3.60408,8.03478,.16151,.20671,.32944,.40695,.4909,.61366,2.96106,3.79788,5.52208,7.88002,7.68104,12.16859-.61017-4.76621,.29067-10.50822,1.82641-14.20959,1.74819-4.21732,5.02491-7.76915,7.91045-11.41501,3.46601-4.37924,10.57337-2.46806,11.18401,3.08332,.00591,.05375,.01166,.10745,.01731,.1612-.4286,.24178-.84849,.49867-1.25864,.76992-2.33949,1.54723-1.53096,5.17386,1.24107,5.60174l.06277,.00967c-.15503,1.54366-.41984,3.07444-.80734,4.57937,3.70179,14.31579-4.29011,19.5299-15.70147,19.76412-.25191,.12916-.49738,.25832-.74929,.38109,1.15617,3.25525,2.07982,6.59447,2.76441,9.97891,.61359,2.99043,1.03991,6.01317,1.27885,9.04888,.29715,3.83006,.27129,7.67959-.05168,11.50323l.01939-.13562c.82024-4.21115,3.10671-8.14462,6.4266-10.87028,4.94561-4.06264,11.93282-5.55869,17.26826-8.82425,2.56833-1.57196,5.85945,.45945,5.41121,3.43708l-.02182,.14261c-.79443,.32289-1.56947,.69755-2.31871,1.11733-.4286,.24184-.84848,.49867-1.25864,.76992-2.33949,1.54729-1.53096,5.17392,1.24107,5.6018l.06282,.00965c.0452,.00646,.08397,.01295,.12911,.01944-1.36282,3.23581-3.26168,6.23922-5.63854,8.82922-2.31463,12.49713-12.25603,13.68282-22.89022,10.04354h-.00648c-1.16259,5.06378-2.86128,10.01127-5.0444,14.72621h-18.02019c-.06463-.20022-.12274-.40692-.18089-.60717,1.6664,.10341,3.34571,.00649,4.98629-.29702-1.33701-1.64059-2.67396-3.29409-4.01097-4.93462-.03229-.0323-.05816-.0646-.08397-.09689-.67817-.8396-1.36282-1.67283-2.04099-2.51246l-.00036-.00102c-.04245-2.57755,.26652-5.14662,.87876-7.63984l.00057-.00035Z" fill="#f2f2f2"/>
                      <path d="M0,448.07526c0,.66003,.53003,1.19,1.19006,1.19H404.48004c.65997,0,1.19-.52997,1.19-1.19,0-.65997-.53003-1.19-1.19-1.19H1.19006c-.66003,0-1.19006,.53003-1.19006,1.19Z" fill="#ccc"/>
                      <path d="M544.74304,449h-206.55127c-10.87598,0-19.72412-8.84863-19.72412-19.72461V19.72461c0-10.87598,8.84814-19.72461,19.72412-19.72461h206.55127c10.87598,0,19.72461,8.84863,19.72461,19.72461V429.27539c0,10.87598-8.84863,19.72461-19.72461,19.72461ZM338.19177,2c-9.78876,0-17.72412,7.93536-17.72412,17.72412V429.27539c0,9.78903,7.93558,17.72461,17.72461,17.72461h206.55078c9.78903,0,17.72461-7.93558,17.72461-17.72461V150.70484c0-82.12742-66.57743-148.70484-148.70484-148.70484h-75.57104Z" fill="#e6e6e6"/>
                      <g>
                        <circle cx="377.13408" cy="178.11811" r="24" fill="#e6e6e6"/>
                        <circle cx="505.80075" cy="178.11811" r="24" fill="#e6e6e6"/>
                        <circle cx="441.46741" cy="178.11811" r="24" fill="#e6e6e6"/>
                      </g>
                      <circle cx="377.13408" cy="247.11811" r="24" fill="#e6e6e6"/>
                      <circle cx="505.80075" cy="247.11811" r="24" fill="#e6e6e6"/>
                      <circle cx="441.46741" cy="247.11811" r="24" fill="#e6e6e6"/>
                      <circle cx="377.13408" cy="316.11811" r="24" fill="#e6e6e6"/>
                      <circle cx="505.30993" cy="385" r="24" fill="#e6e6e6"/>
                      <ellipse cx="409.80993" cy="385" rx="56.5" ry="24" fill="#e6e6e6"/>
                      <circle cx="505.80075" cy="316.11811" r="24" fill="#e6e6e6"/>
                      <circle cx="441.46741" cy="316.11811" r="24" fill="#66c538"/>
                      <path d="M446.18385,28H216.6503c-6.80601,0-12.34037,5.53432-12.34037,12.34033V124.65963c0,6.80601,5.53436,12.34037,12.34037,12.34037h229.53355c6.80601,0,12.34037-5.53436,12.34037-12.34037V40.34033c0-6.80601-5.53436-12.34033-12.34037-12.34033Z" fill="#fff"/>
                      <path d="M446.18385,28H216.6503c-6.80601,0-12.34037,5.53432-12.34037,12.34033V124.65963c0,6.80601,5.53436,12.34037,12.34037,12.34037h229.53355c6.80601,0,12.34037-5.53436,12.34037-12.34037V40.34033c0-6.80601-5.53436-12.34033-12.34037-12.34033Zm11.04275,96.65963c0,6.09233-4.95042,11.04275-11.04275,11.04275H216.6503c-6.09233,0-11.04275-4.95042-11.04275-11.04275V40.34033c0-6.09229,4.95042-11.04271,11.04275-11.04271h229.53355c6.09233,0,11.04275,4.95042,11.04275,11.04271V124.65963Z" fill="#3f3d56"/>
                      <rect x="225.91708" y="79" width="211" height="7" rx="3.5" ry="3.5" fill="#66c538"/>
                      <g>
                        <g>
                          <polygon points="67.95213 180.42129 65.90768 192.59602 84.03834 236.69982 93.76596 231.69877 84.29796 190.42664 82.93717 174.67575 67.95213 180.42129" fill="#a0616a"/>
                          <path d="M87.96609,111.92855s-9.49261-4.59838-16.85857,10.93887c-7.36596,15.53725-6.45381,49.61433-6.45381,49.61433,0,0-2.68212,2.55393-.9795,4.34201s3.28993,4.62577,1.55307,7.89681c-.9154,1.72399-3.03398,5.09676,.43536,6.05489,3.46934,.95813,19.69791-3.56816,19.69791-3.56816,0,0,6.06837-2.71848,2.62039-4.54929-3.44797-1.83081,1.4081-12.90537,1.4081-12.90537l4.01246-31.99147-5.43541-25.83262Z" fill="#66c538"/>
                          <ellipse cx="91.85157" cy="241.9309" rx="7.21689" ry="12.15477" transform="translate(-66.97226 37.62166) rotate(-17.07063)" fill="#a0616a"/>
                        </g>
                        <g>
                          <polygon points="185.27776 442.5699 194.36401 440.13085 189.2821 403.92273 175.87152 407.52214 185.27776 442.5699" fill="#a0616a"/>
                          <path d="M216.35388,434.01035h0c.41075,.40056,.97271,1.8984,1.1214,2.45253h0c.45702,1.70323-.55323,3.45446-2.25645,3.91148l-28.13979,7.55067c-1.16195,.31178-2.35664-.37741-2.66842-1.53936l-.31436-1.17155s-2.33687-3.14757-.63539-8.25657c0,0,4.4739,2.44248,8.36829-4.30831l.80662-3.26456,13.59126,5.26066,6.50559-.91464c1.42327-.2001,2.59225-.72379,3.62124,.27966Z" fill="#2f2e41"/>
                        </g>
                        <g>
                          <polygon points="96.2113 442.361 105.61922 442.36008 110.09461 406.072 96.20939 406.07296 96.2113 442.361" fill="#a0616a"/>
                          <path d="M128.44398,442.14758h0c.29291,.49332,.44749,2.08563,.44749,2.65936h0c0,1.76348-1.42958,3.19306-3.19306,3.19306h-29.13521c-1.20305,0-2.17832-.97527-2.17832-2.17832v-1.21299s-1.44131-3.64566,1.52609-8.13914c0,0,3.68805,3.51848,9.19893-1.99239l1.62511-2.94398,11.76356,8.60323,6.52037,.80259c1.4265,.17559,2.69126-.02725,3.42504,1.20859Z" fill="#2f2e41"/>
                        </g>
                        <path d="M158.82276,226.20501l24.73867,106.63221,11.94281,86.15882-18.76727,2.55917-52.03652-146.72592-11.94281,146.72592-21.32644,1.70612s-17.06115-174.02376-9.38363-195.3502l76.77519-1.70612Z" fill="#2f2e41"/>
                        <g>
                          <polygon points="188.26927 100.11464 196.80486 91.19571 206.607 44.52897 195.75069 43.19573 180.37664 82.6504 172.64656 96.44127 188.26927 100.11464" fill="#a0616a"/>
                          <path d="M133.21005,145.50513s7.98834,14.72948,22.81524,6.02164c14.8269-8.70785,30.50798-42.99178,30.50798-42.99178,0,0,3.13116-8.71523,6.40656-10.44384,1.72629-.91106,6.16822-1.89357,3.8378-4.63646-2.33042-2.74289-19.0897-8.73935-19.0897-8.73935,0,0-6.54685-1.16359-4.72473,2.28898,1.82212,3.45257-4.16226,1.31382-3.55812,4.96777,.60414,3.65395-4.86201,4.91316-4.86201,4.91316l-31.33303,48.61987Z" fill="#66c538"/>
                          <ellipse cx="203.08656" cy="35.81018" rx="12.15477" ry="7.21689" transform="translate(109.01967 219.32569) rotate(-72.86125)" fill="#a0616a"/>
                        </g>
                        <path d="M134.2706,94.47349h-23.35635l-.94516,7.36409s-29.45637,8.1005-28.71996,12.51896,5.15486,78.05938,5.15486,78.05938l-3.68205,36.82046s66.27683,27.98355,76.58656-5.15486l-5.15486-38.29328s12.51896-65.54042,9.57332-68.48606c-2.94564-2.94564-27.24714-14.72818-27.24714-14.72818l-2.20923-8.1005Z" fill="#66c538"/>
                        <circle cx="120.70371" cy="73.60124" r="20.964" fill="#a0616a"/>
                        <path d="M136.22941,64.9778c2.6786-.22647,5.35708-.45294,8.03555-.67929-.92068-.42235-1.87955-.87429-2.49305-1.68044-.61337-.80602-.74023-2.07965,.00075-2.77026,.86596-.80701,2.30003-.37944,3.24646,.33144-.52109-2.72088-2.14507-5.21393-4.42346-6.79002-2.78307-1.92531-9.6681,.37044-13.02326-.07156-3.87127-.51003-4.35444-3.81054-8.22571-4.32056-1.45906-.19215-3.1012-.33716-4.2486,.58465-1.27264,1.02229-1.41541,2.95519-2.53819,4.14003-1.60308,1.69175-4.3507,1.19267-6.63208,1.66862-3.23128,.67431-5.66712,3.50763-6.79214,6.61094s-1.16643,6.47861-1.19516,9.77954c-.03209,3.69927-.04373,7.98181,2.63188,10.53679,1.62994,1.55657,6.07015,6.89712,6.55008,9.0994,.34089,1.56403,14.23372,3.46895,17.24487,3.38226l10.26295-3.38226c1.00861-4.44137-.65987-13.22738-3.0579-17.0994,.78973,1.04306,4.44172,4.18322,5,3s.30504-4.89165,1-6c.87728-1.39887-2.98823-6.20083-1.34299-6.33987Z" fill="#2f2e41"/>
                      </g>
                    </svg>
                  </Box>
                  
                  <Typography
                    variant="body1"
                    sx={{
                      color: '#2e7d32',
                      textAlign: 'center',
                      marginBottom: 3,
                      lineHeight: 1.6,
                    }}
                  >
                    Sign in to continue your financial journey and access all your personalized insights.
                  </Typography>
                  
                  {/* Feature chips */}
                  <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', justifyContent: 'center' }}>
                    {['💰 Smart Budgeting', '📊 Analytics', '🔒 Secure'].map((feature, index) => (
                      <Chip
                        key={index}
                        label={feature}
                        size="small"
                        sx={{
                          background: 'rgba(139, 195, 74, 0.1)',
                          color: '#2e7d32',
                          fontWeight: 600,
                          borderRadius: '16px',
                          border: '1px solid rgba(139, 195, 74, 0.3)',
                        }}
                      />
                    ))}
                  </Box>
                </Box>

                {/* Right Side - Sign In Form */}
                <Box sx={{ width: '55%', padding: 3 }}>
                  <Typography 
                    variant="h4" 
                    sx={{ 
                      marginBottom: 4, 
                      color: '#2e7d32', 
                      fontWeight: 700,
                      textAlign: 'center'
                    }}
                  >
                    Sign In
                  </Typography>

                  <StyledTextField
                    name="emailId"
                    label="Username"
                    variant="outlined"
                    value={user?.emailId || ''}
                    onChange={handleChange1}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonIcon sx={{ color: '#8bc34a' }} />
                        </InputAdornment>
                      ),
                    }}
                    sx={{ marginBottom: 3 }}
                  />

                  <StyledTextField
                    name="password"
                    label="Password"
                    type={showPassword ? 'text' : 'password'}
                    variant="outlined"
                    value={user?.password || ''}
                    onChange={handleChange1}
                    fullWidth
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LockIcon sx={{ color: '#8bc34a' }} />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            onClick={() => setShowPassword(!showPassword)}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{ marginBottom: 3 }}
                  />

                  <StyledButton
                    variant="contained"
                    fullWidth
                    onClick={handleSignIn}
                    disabled={loading || useOtp}
                    sx={{ marginBottom: 3 }}
                  >
                    {loading ? <CircularProgress size={24} color="inherit" /> : 'Sign In'}
                  </StyledButton>

                  <StyledOutlinedButton
                    variant="outlined"
                    fullWidth
                    onClick={() => setOtpDialogOpen(true)}
                    sx={{ marginBottom: 3 }}
                  >
                    Login with One-Time Passcode
                  </StyledOutlinedButton>

                  <Typography sx={{ textAlign: 'center', marginBottom: 3, color: '#999' }}>
                    or continue with
                  </Typography>

                  <Box sx={{ display: 'flex', gap: 2, marginBottom: 3 }}>
                    <Box sx={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                      <GoogleOAuthProvider clientId="************-4ii30dirf596vm7ncnbkvf6q21vvk6sb.apps.googleusercontent.com">
                        <GoogleLogin
                          onSuccess={(credentialResponse) => {
                            const token = credentialResponse.credential;
                            dispatch(googleSignInRequest({ token }));
                          }}
                          onError={() => {
                            setGoogleError('Google sign-in failed');
                          }}
                          size="large"
                          theme="outline"
                          width="100%"
                        />
                      </GoogleOAuthProvider>
                    </Box>
                    
                    <StyledOutlinedButton
                      onClick={handleAppleLogin}
                      startIcon={<AppleIcon />}
                      sx={{ 
                        flex: 1,
                        color: '#000', 
                        borderColor: '#000',
                        fontSize: '0.8rem',
                        padding: '10px 16px',
                        minHeight: '30px',
                        '&:hover': {
                          background: 'rgba(0, 0, 0, 0.05)',
                          borderColor: '#000',
                        }
                      }}
                    >
                      Apple
                    </StyledOutlinedButton>
                  </Box>

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 3 }}>
                    <FormControlLabel
                      control={
                        <Checkbox 
                          checked={rememberMe} 
                          onChange={handleRememberMeChange}
                          sx={{ color: '#8bc34a' }}
                        />
                      }
                      label="Remember Me"
                      sx={{ color: '#666' }}
                    />
                    
                    <Link 
                      component={RouterLink} 
                      to="/forgot-password"
                      sx={{ 
                        cursor: 'pointer',
                        color: '#8bc34a',
                        textDecoration: 'none',
                        fontWeight: 500,
                        '&:hover': { textDecoration: 'underline' }
                      }}
                    >
                      Forgot Password?
                    </Link>
                  </Box>

                  {error && (
                    <Typography sx={{ color: '#f44336', textAlign: 'center', marginBottom: 2 }}>
                      {error}
                    </Typography>
                  )}
                  {googleError && (
                    <Typography sx={{ color: '#f44336', textAlign: 'center', marginBottom: 2 }}>
                      {googleError}
                    </Typography>
                  )}

                  <Box sx={{ textAlign: 'center' }}>
                    <Link
                      href="#"
                      onClick={toggleForm}
                      sx={{ 
                        cursor: 'pointer', 
                        color: '#8bc34a', 
                        textDecoration: 'none',
                        fontWeight: 500,
                        '&:hover': { textDecoration: 'underline' }
                      }}
                    >
                      Don't have an account? Sign up
                    </Link>
                  </Box>
                </Box>
              </Box>
            </Fade>
          )}

          {/* Signup Form */}
          <Collapse in={showSignUp && registrationData == null} timeout="auto" unmountOnExit>
            <Box sx={{ width: '100%', display: 'flex' }}>
              {/* Left Side - PennyPal Branding */}
              <Box sx={{ 
                width: '45%', 
                padding: 3, 
                display: 'flex', 
                flexDirection: 'column', 
                justifyContent: 'center',
                borderRight: '1px solid rgba(139, 195, 74, 0.2)' 
              }}>
                <Typography
                  variant="h2"
                  sx={{
                    fontWeight: 800,
                    background: 'linear-gradient(135deg, #2e7d32 0%, #4caf50 50%, #8bc34a 100%)',
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    fontSize: { xs: '2.5rem', md: '3rem' },
                    letterSpacing: '2px',
                    marginBottom: 2,
                    textAlign: 'center',
                  }}
                >
                  PennyPal
                </Typography>
                <Typography
                  variant="h6"
                  sx={{
                    color: '#666',
                    fontWeight: 500,
                    fontSize: '1.1rem',
                    letterSpacing: '1px',
                    marginBottom: 3,
                    textAlign: 'center',
                  }}
                >
                  Your Financial Companion
                </Typography>
                
                {/* Financial Dashboard Illustration */}
                <Box sx={{ display: 'flex', justifyContent: 'center', marginBottom: 3 }}>
                  <svg xmlns="http://www.w3.org/2000/svg" width="280" height="220" viewBox="0 0 564.46765 449.26526" role="img">
                    <path d="M239.34575,431.62993c2.06592,.12937,3.20768-2.43737,1.64468-3.93333l-.1555-.61819c.02047-.04951,.04105-.09897,.06178-.14839,2.08924-4.9818,9.16992-4.94742,11.24139,.04177,1.83859,4.42817,4.17942,8.86389,4.75579,13.54594,.25838,2.0668,.14213,4.17236-.31648,6.20047,4.30807-9.41059,6.57515-19.68661,6.57515-30.02077,0-2.59652-.14213-5.19301-.43275-7.78295-.239-2.11854-.56839-4.2241-.99471-6.31034-2.30575-11.2772-7.29852-22.01825-14.50012-30.98962-3.46197-1.89248-6.34906-4.85065-8.09295-8.39652-.62649-1.27891-1.11739-2.65462-1.34991-4.05618,.39398,.05168,1.48556-5.94866,1.18841-6.3168,.54906-.83317,1.53178-1.24733,2.13144-2.06034,2.98232-4.04341,7.0912-3.33741,9.23621,2.15727,4.58224,2.31266,4.62659,6.14806,1.81495,9.83683-1.78878,2.34682-2.03456,5.52233-3.60408,8.03478,.16151,.20671,.32944,.40695,.4909,.61366,2.96106,3.79788,5.52208,7.88002,7.68104,12.16859-.61017-4.76621,.29067-10.50822,1.82641-14.20959,1.74819-4.21732,5.02491-7.76915,7.91045-11.41501,3.46601-4.37924,10.57337-2.46806,11.18401,3.08332,.00591,.05375,.01166,.10745,.01731,.1612-.4286,.24178-.84849,.49867-1.25864,.76992-2.33949,1.54723-1.53096,5.17386,1.24107,5.60174l.06277,.00967c-.15503,1.54366-.41984,3.07444-.80734,4.57937,3.70179,14.31579-4.29011,19.5299-15.70147,19.76412-.25191,.12916-.49738,.25832-.74929,.38109,1.15617,3.25525,2.07982,6.59447,2.76441,9.97891,.61359,2.99043,1.03991,6.01317,1.27885,9.04888,.29715,3.83006,.27129,7.67959-.05168,11.50323l.01939-.13562c.82024-4.21115,3.10671-8.14462,6.4266-10.87028,4.94561-4.06264,11.93282-5.55869,17.26826-8.82425,2.56833-1.57196,5.85945,.45945,5.41121,3.43708l-.02182,.14261c-.79443,.32289-1.56947,.69755-2.31871,1.11733-.4286,.24184-.84848,.49867-1.25864,.76992-2.33949,1.54729-1.53096,5.17392,1.24107,5.6018l.06282,.00965c.0452,.00646,.08397,.01295,.12911,.01944-1.36282,3.23581-3.26168,6.23922-5.63854,8.82922-2.31463,12.49713-12.25603,13.68282-22.89022,10.04354h-.00648c-1.16259,5.06378-2.86128,10.01127-5.0444,14.72621h-18.02019c-.06463-.20022-.12274-.40692-.18089-.60717,1.6664,.10341,3.34571,.00649,4.98629-.29702-1.33701-1.64059-2.67396-3.29409-4.01097-4.93462-.03229-.0323-.05816-.0646-.08397-.09689-.67817-.8396-1.36282-1.67283-2.04099-2.51246l-.00036-.00102c-.04245-2.57755,.26652-5.14662,.87876-7.63984l.00057-.00035Z" fill="#f2f2f2"/>
                    <path d="M0,448.07526c0,.66003,.53003,1.19,1.19006,1.19H404.48004c.65997,0,1.19-.52997,1.19-1.19,0-.65997-.53003-1.19-1.19-1.19H1.19006c-.66003,0-1.19006,.53003-1.19006,1.19Z" fill="#ccc"/>
                    <path d="M544.74304,449h-206.55127c-10.87598,0-19.72412-8.84863-19.72412-19.72461V19.72461c0-10.87598,8.84814-19.72461,19.72412-19.72461h206.55127c10.87598,0,19.72461,8.84863,19.72461,19.72461V429.27539c0,10.87598-8.84863,19.72461-19.72461,19.72461ZM338.19177,2c-9.78876,0-17.72412,7.93536-17.72412,17.72412V429.27539c0,9.78903,7.93558,17.72461,17.72461,17.72461h206.55078c9.78903,0,17.72461-7.93558,17.72461-17.72461V150.70484c0-82.12742-66.57743-148.70484-148.70484-148.70484h-75.57104Z" fill="#e6e6e6"/>
                    <g>
                      <circle cx="377.13408" cy="178.11811" r="24" fill="#e6e6e6"/>
                      <circle cx="505.80075" cy="178.11811" r="24" fill="#e6e6e6"/>
                      <circle cx="441.46741" cy="178.11811" r="24" fill="#e6e6e6"/>
                    </g>
                    <circle cx="377.13408" cy="247.11811" r="24" fill="#e6e6e6"/>
                    <circle cx="505.80075" cy="247.11811" r="24" fill="#e6e6e6"/>
                    <circle cx="441.46741" cy="247.11811" r="24" fill="#e6e6e6"/>
                    <circle cx="377.13408" cy="316.11811" r="24" fill="#e6e6e6"/>
                    <circle cx="505.30993" cy="385" r="24" fill="#e6e6e6"/>
                    <ellipse cx="409.80993" cy="385" rx="56.5" ry="24" fill="#e6e6e6"/>
                    <circle cx="505.80075" cy="316.11811" r="24" fill="#e6e6e6"/>
                    <circle cx="441.46741" cy="316.11811" r="24" fill="#66c538"/>
                    <path d="M446.18385,28H216.6503c-6.80601,0-12.34037,5.53432-12.34037,12.34033V124.65963c0,6.80601,5.53436,12.34037,12.34037,12.34037h229.53355c6.80601,0,12.34037-5.53436,12.34037-12.34037V40.34033c0-6.80601-5.53436-12.34033-12.34037-12.34033Z" fill="#fff"/>
                    <path d="M446.18385,28H216.6503c-6.80601,0-12.34037,5.53432-12.34037,12.34033V124.65963c0,6.80601,5.53436,12.34037,12.34037,12.34037h229.53355c6.80601,0,12.34037-5.53436,12.34037-12.34037V40.34033c0-6.80601-5.53436-12.34033-12.34037-12.34033Zm11.04275,96.65963c0,6.09233-4.95042,11.04275-11.04275,11.04275H216.6503c-6.09233,0-11.04275-4.95042-11.04275-11.04275V40.34033c0-6.09229,4.95042-11.04271,11.04275-11.04271h229.53355c6.09233,0,11.04275,4.95042,11.04275,11.04271V124.65963Z" fill="#3f3d56"/>
                    <rect x="225.91708" y="79" width="211" height="7" rx="3.5" ry="3.5" fill="#66c538"/>
                    <g>
                      <g>
                        <polygon points="67.95213 180.42129 65.90768 192.59602 84.03834 236.69982 93.76596 231.69877 84.29796 190.42664 82.93717 174.67575 67.95213 180.42129" fill="#a0616a"/>
                        <path d="M87.96609,111.92855s-9.49261-4.59838-16.85857,10.93887c-7.36596,15.53725-6.45381,49.61433-6.45381,49.61433,0,0-2.68212,2.55393-.9795,4.34201s3.28993,4.62577,1.55307,7.89681c-.9154,1.72399-3.03398,5.09676,.43536,6.05489,3.46934,.95813,19.69791-3.56816,19.69791-3.56816,0,0,6.06837-2.71848,2.62039-4.54929-3.44797-1.83081,1.4081-12.90537,1.4081-12.90537l4.01246-31.99147-5.43541-25.83262Z" fill="#66c538"/>
                        <ellipse cx="91.85157" cy="241.9309" rx="7.21689" ry="12.15477" transform="translate(-66.97226 37.62166) rotate(-17.07063)" fill="#a0616a"/>
                      </g>
                      <g>
                        <polygon points="185.27776 442.5699 194.36401 440.13085 189.2821 403.92273 175.87152 407.52214 185.27776 442.5699" fill="#a0616a"/>
                        <path d="M216.35388,434.01035h0c.41075,.40056,.97271,1.8984,1.1214,2.45253h0c.45702,1.70323-.55323,3.45446-2.25645,3.91148l-28.13979,7.55067c-1.16195,.31178-2.35664-.37741-2.66842-1.53936l-.31436-1.17155s-2.33687-3.14757-.63539-8.25657c0,0,4.4739,2.44248,8.36829-4.30831l.80662-3.26456,13.59126,5.26066,6.50559-.91464c1.42327-.2001,2.59225-.72379,3.62124,.27966Z" fill="#2f2e41"/>
                      </g>
                      <g>
                        <polygon points="96.2113 442.361 105.61922 442.36008 110.09461 406.072 96.20939 406.07296 96.2113 442.361" fill="#a0616a"/>
                        <path d="M128.44398,442.14758h0c.29291,.49332,.44749,2.08563,.44749,2.65936h0c0,1.76348-1.42958,3.19306-3.19306,3.19306h-29.13521c-1.20305,0-2.17832-.97527-2.17832-2.17832v-1.21299s-1.44131-3.64566,1.52609-8.13914c0,0,3.68805,3.51848,9.19893-1.99239l1.62511-2.94398,11.76356,8.60323,6.52037,.80259c1.4265,.17559,2.69126-.02725,3.42504,1.20859Z" fill="#2f2e41"/>
                      </g>
                      <path d="M158.82276,226.20501l24.73867,106.63221,11.94281,86.15882-18.76727,2.55917-52.03652-146.72592-11.94281,146.72592-21.32644,1.70612s-17.06115-174.02376-9.38363-195.3502l76.77519-1.70612Z" fill="#2f2e41"/>
                      <g>
                        <polygon points="188.26927 100.11464 196.80486 91.19571 206.607 44.52897 195.75069 43.19573 180.37664 82.6504 172.64656 96.44127 188.26927 100.11464" fill="#a0616a"/>
                        <path d="M133.21005,145.50513s7.98834,14.72948,22.81524,6.02164c14.8269-8.70785,30.50798-42.99178,30.50798-42.99178,0,0,3.13116-8.71523,6.40656-10.44384,1.72629-.91106,6.16822-1.89357,3.8378-4.63646-2.33042-2.74289-19.0897-8.73935-19.0897-8.73935,0,0-6.54685-1.16359-4.72473,2.28898,1.82212,3.45257-4.16226,1.31382-3.55812,4.96777,.60414,3.65395-4.86201,4.91316-4.86201,4.91316l-31.33303,48.61987Z" fill="#66c538"/>
                        <ellipse cx="203.08656" cy="35.81018" rx="12.15477" ry="7.21689" transform="translate(109.01967 219.32569) rotate(-72.86125)" fill="#a0616a"/>
                      </g>
                      <path d="M134.2706,94.47349h-23.35635l-.94516,7.36409s-29.45637,8.1005-28.71996,12.51896,5.15486,78.05938,5.15486,78.05938l-3.68205,36.82046s66.27683,27.98355,76.58656-5.15486l-5.15486-38.29328s12.51896-65.54042,9.57332-68.48606c-2.94564-2.94564-27.24714-14.72818-27.24714-14.72818l-2.20923-8.1005Z" fill="#66c538"/>
                      <circle cx="120.70371" cy="73.60124" r="20.964" fill="#a0616a"/>
                      <path d="M136.22941,64.9778c2.6786-.22647,5.35708-.45294,8.03555-.67929-.92068-.42235-1.87955-.87429-2.49305-1.68044-.61337-.80602-.74023-2.07965,.00075-2.77026,.86596-.80701,2.30003-.37944,3.24646,.33144-.52109-2.72088-2.14507-5.21393-4.42346-6.79002-2.78307-1.92531-9.6681,.37044-13.02326-.07156-3.87127-.51003-4.35444-3.81054-8.22571-4.32056-1.45906-.19215-3.1012-.33716-4.2486,.58465-1.27264,1.02229-1.41541,2.95519-2.53819,4.14003-1.60308,1.69175-4.3507,1.19267-6.63208,1.66862-3.23128,.67431-5.66712,3.50763-6.79214,6.61094s-1.16643,6.47861-1.19516,9.77954c-.03209,3.69927-.04373,7.98181,2.63188,10.53679,1.62994,1.55657,6.07015,6.89712,6.55008,9.0994,.34089,1.56403,14.23372,3.46895,17.24487,3.38226l10.26295-3.38226c1.00861-4.44137-.65987-13.22738-3.0579-17.0994,.78973,1.04306,4.44172,4.18322,5,3s.30504-4.89165,1-6c.87728-1.39887-2.98823-6.20083-1.34299-6.33987Z" fill="#2f2e41"/>
                    </g>
                  </svg>
                </Box>
                
                <Typography
                  variant="body1"
                  sx={{
                    color: '#2e7d32',
                    textAlign: 'center',
                    marginBottom: 3,
                    lineHeight: 1.6,
                  }}
                >
                  Create your account and start your journey towards smarter financial management.
                </Typography>
                
                {/* Feature chips */}
                <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap', justifyContent: 'center' }}>
                  {['💰 Smart Budgeting', '📊 Analytics', '🔒 Secure'].map((feature, index) => (
                    <Chip
                      key={index}
                      label={feature}
                      size="small"
                      sx={{
                        background: 'rgba(139, 195, 74, 0.1)',
                        color: '#2e7d32',
                        fontWeight: 600,
                        borderRadius: '16px',
                        border: '1px solid rgba(139, 195, 74, 0.3)',
                      }}
                    />
                  ))}
                </Box>
              </Box>

              {/* Right Side - Sign Up Form */}
              <Box sx={{ width: '55%', padding: 3 }}>
                <Typography 
                  variant="h4" 
                  sx={{ 
                    marginBottom: 4, 
                    color: '#2e7d32', 
                    fontWeight: 700,
                    textAlign: 'center'
                  }}
                >
                  Create Account
                </Typography>

                <StyledTextField
                  name="emailId"
                  label="Email Address"
                  type="email"
                  variant="outlined"
                  value={user?.emailId || ''}
                  onChange={handleChange}
                  fullWidth
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <EmailIcon sx={{ color: '#8bc34a' }} />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ marginBottom: 3 }}
                />

                <Box sx={{ marginBottom: 3 }}>
                  <PhoneInput              
                    country={'us'}
                    enableSearch
                    inputStyle={{ 
                      width: '100%',
                      borderRadius: '16px',
                      border: '1px solid #ddd',
                      padding: '16px 14px 16px 58px',
                      fontSize: '16px',
                      background: 'rgba(255, 255, 255, 0.9)',
                    }}
                    containerStyle={{
                      borderRadius: '16px',
                    }}
                    name="phoneNumber"
                    onChange={handlePhoneChange}
                    placeholder="Enter phone number"
                    value={user?.phoneNumber || ''}
                    onlyCountries={['us']}
                    disableDropdown
                  />
                </Box>

                <StyledButton
                  variant="contained"
                  fullWidth
                  onClick={handleSignUp}
                  disabled={loading}
                  sx={{ marginBottom: 3 }}
                >
                  {loading ? <CircularProgress size={24} color="inherit" /> : 'Create Account'}
                </StyledButton>

                <Typography sx={{ textAlign: 'center', marginBottom: 3, color: '#999' }}>
                  or continue with
                </Typography>

                <Box sx={{ display: 'flex', gap: 2, marginBottom: 3 }}>
                  <Box sx={{ flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                    <GoogleOAuthProvider clientId="************-4ii30dirf596vm7ncnbkvf6q21vvk6sb.apps.googleusercontent.com">
                      <GoogleLogin
                        onSuccess={async (credentialResponse) => {
                          try {
                            const token = credentialResponse.credential;
                            const res = await axios.post(`${API_URL}/pennypal/api/v1/auth/register/google`, { token });
                            if (res.data.id) {
                              alert("Google sign-in successful");
                            } else {
                              setGoogleError(res.data.message);
                            }
                          } catch (error) {
                            console.error("Google login error", error);
                            setGoogleError(error.response?.data?.message || 'Google sign-in failed');
                          }
                        }}
                        onError={() => {
                          setGoogleError('Google sign-in failed');
                        }}
                        size="large"
                        theme="outline"
                        width="100%"
                      />
                    </GoogleOAuthProvider>
                  </Box>

                  <StyledOutlinedButton
                    onClick={handleAppleLogin}
                    startIcon={<AppleIcon />}
                    sx={{ 
                      flex: 1,
                      color: '#000', 
                      borderColor: '#000',
                      fontSize: '0.9rem',
                      padding: '11px 16px',
                      minHeight: '44px',
                      '&:hover': {
                        background: 'rgba(0, 0, 0, 0.05)',
                        borderColor: '#000',
                      }
                    }}
                  >
                    Apple
                  </StyledOutlinedButton>
                </Box>

                {error && (
                  <Typography sx={{ color: '#f44336', textAlign: 'center', marginBottom: 2 }}>
                    {error}
                  </Typography>
                )}
                {googleError && (
                  <Typography sx={{ color: '#f44336', textAlign: 'center', marginBottom: 2 }}>
                    {googleError}
                  </Typography>
                )}
                
                <Box sx={{ textAlign: 'center' }}>
                  <Link
                    href="#"
                    onClick={toggleForm}
                    sx={{ 
                      cursor: 'pointer', 
                      color: '#8bc34a', 
                      textDecoration: 'none',
                      fontWeight: 500,
                      '&:hover': { textDecoration: 'underline' }
                    }}
                  >
                    Already have an account? Sign in
                  </Link>
                </Box>
              </Box>
            </Box>
          </Collapse>
        </StyledCard>
      </WelcomeSection>

      {/* OTP Dialog */}
      <Dialog 
        open={otpDialogOpen} 
        onClose={() => setOtpDialogOpen(false)}
        PaperProps={{
          sx: {
            borderRadius: '20px',
            padding: '1rem',
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(20px)',
          }
        }}
      >
        <DialogTitle sx={{ textAlign: 'center', fontWeight: 700, color: '#2e7d32' }}>
          Login with One-Time Passcode
        </DialogTitle>
        <DialogContent>
          <StyledTextField
            label={identifierType === 'email' ? "Email Address" : "Phone Number"}
            value={otpIdentifier}
            onChange={handleOtpIdentifierChange}
            fullWidth
            sx={{ marginBottom: 2, marginTop: 1 }}
            error={!!otpError && !otpIdentifier}
            helperText={(!otpIdentifier && otpError) ? `${identifierType === 'email' ? 'Email' : 'Phone number'} is required` : ''}
            placeholder={identifierType === 'email' ? "<EMAIL>" : "+****************"}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    onClick={handleToggleIdentifierType}
                    edge="end"
                    title={`Switch to ${identifierType === 'email' ? 'phone' : 'email'}`}
                  >
                    {identifierType === 'email' ? <PhoneIcon /> : <EmailIcon />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
          <StyledButton
            variant="contained"
            fullWidth
            sx={{ marginBottom: 2 }}
            onClick={handleSendOtp}
            disabled={!otpIdentifier || resendDisabled || otpLoading}
          >
            {otpLoading ? <CircularProgress size={24} /> : 
                          `Send One-Time Passcode ${resendDisabled && !otpLoading ? `(${resendTimer}s)` : ''}`}
          </StyledButton>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <StyledTextField
              label="Enter One-Time Passcode"
              value={otp}
              onChange={handleOtpChange}
              fullWidth
              error={!!otpError}
              helperText={otpError}
              inputProps={{ maxLength: 6 }}
            />
            <IconButton 
              onClick={handleOtpSubmit} 
              disabled={otp.length !== 6}
              sx={{
                background: '#8bc34a',
                color: 'white',
                '&:hover': { background: '#689f38' },
                '&:disabled': { background: '#ccc' }
              }}
            >
              <ArrowForwardIcon />
            </IconButton>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOtpDialogOpen(false)} sx={{ color: '#8bc34a' }}>
            Cancel
          </Button>
        </DialogActions>
      </Dialog>
    </GradientBackground>
  );
}

export default SignIn;