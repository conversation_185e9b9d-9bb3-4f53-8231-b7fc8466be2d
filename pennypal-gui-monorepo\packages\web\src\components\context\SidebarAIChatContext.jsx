import React, { createContext, useContext, useState, useCallback } from 'react';

// Create the context
const SidebarAIChatContext = createContext();

// Custom hook to use the context
export const useSidebarAIChat = () => {
  const context = useContext(SidebarAIChatContext);
  if (!context) {
    throw new Error('useSidebarAIChat must be used within a SidebarAIChatProvider');
  }
  return context;
};

// Provider component
export const SidebarAIChatProvider = ({ children }) => {
  const [isSidebarMinimized, setIsSidebarMinimized] = useState(false);
  const [isAIChatOpen, setIsAIChatOpen] = useState(false);
  const [sidebarWasMinimizedByAI, setSidebarWasMinimizedByAI] = useState(false);

  // Function to handle AI chat opening
  const handleAIChatOpen = useCallback(() => {
    console.log('AI Chat opening - checking sidebar state');
    
    // If sidebar is not minimized, minimize it and remember that we did it
    if (!isSidebarMinimized) {
      console.log('Minimizing sidebar due to AI chat opening');
      setIsSidebarMinimized(true);
      setSidebarWasMinimizedByAI(true);
    }
    
    setIsAIChatOpen(true);
  }, [isSidebarMinimized]);

  // Function to handle AI chat closing
  const handleAIChatClose = useCallback(() => {
    console.log('AI Chat closing - checking if we should restore sidebar');
    
    // If we minimized the sidebar when AI chat opened, restore it
    if (sidebarWasMinimizedByAI) {
      console.log('Restoring sidebar after AI chat closed');
      setIsSidebarMinimized(false);
      setSidebarWasMinimizedByAI(false);
    }
    
    setIsAIChatOpen(false);
  }, [sidebarWasMinimizedByAI]);

  // Function to handle manual sidebar toggle (from the sidebar toggle button)
  const handleSidebarToggle = useCallback(() => {
    console.log('Manual sidebar toggle');
    
    // If AI chat is open and we're about to minimize manually, 
    // don't set the flag that we minimized due to AI
    if (isAIChatOpen && !isSidebarMinimized) {
      setSidebarWasMinimizedByAI(false);
    }
    
    setIsSidebarMinimized(prev => !prev);
  }, [isAIChatOpen, isSidebarMinimized]);

  // Function to set sidebar state directly (for initialization)
  const setSidebarMinimized = useCallback((minimized) => {
    setIsSidebarMinimized(minimized);
    // If setting manually, clear the AI flag
    if (!minimized) {
      setSidebarWasMinimizedByAI(false);
    }
  }, []);

  const value = {
    // State
    isSidebarMinimized,
    isAIChatOpen,
    sidebarWasMinimizedByAI,
    
    // Actions
    handleAIChatOpen,
    handleAIChatClose,
    handleSidebarToggle,
    setSidebarMinimized,
  };

  return (
    <SidebarAIChatContext.Provider value={value}>
      {children}
    </SidebarAIChatContext.Provider>
  );
};

export default SidebarAIChatContext;