import React from 'react';
import { useLocation } from 'react-router-dom';
import { Routes, Route } from 'react-router-dom';
import { AnimatePresence, motion } from 'framer-motion';
import TransactionPage1 from '../../Transactions/TransactionPage1';
import TransactionPage from '@pp-web/components/v1/components/transaction/TransactionPage';
//import AccountsDashboard from '../../Accounts/AccountsDashboard';
//import AccountsDashboard from '../../Accounts/AccountsDashboard';
import AccountsDashboard from '../components/account/AccountsDashboard';
import RecurringTransactionsView from './RecurringTransactionsView';
import Dashboard from '../../Dashboard/Dashboard';
import Budget5 from '../../Budget/Budget5';
import Budget6 from '../../Budget/Budget6';
import BudgetRulePage from '../../Budget/BudgetRulePage';
import NotificationSettingsModal from './Settings/NotificationSettingsModal';
import SettingsPage from './Settings/SettingsPage'; // adjust path as needed
import Notifications from './Settings/NotificationRulesModal';
import Documents from '../../Document/Documents';
import FamilyDocumentManager from '../../Document/FamilyDocumentManager';
import Investment from '../../Investment/Investment';
import Settings from '../../Settings/FamilySection'
import FamilySection from '../../Settings/FamilySection';
import Goals from '../../Goals/Goals';
// import Reconcile from '../../Reconcile/Reconciliation';
import CashFlow from './CashFlow';
import RecentTransactions from '../../Dashboard/RecentTransactions';
import StripeDashboard from '../../Dashboard/StripeDashboard';
import Chatbot from './Chatbot';
// import CustomChartsDashboard from '../../Dashboard/CustomChartsDashboard';
import PaymentLoader from '../../load/PaymentLoader'; // Import PaymentLoader
import { LoadingProvider, useLoading } from '../../context/LoadingContext'; // Import LoadingContext
import { themeClasses } from '../../../utils/tailwindUtils';
// import CreditKarmaClone from './Credit'; // Import CreditKarmaClone
import { Navigate } from 'react-router-dom';
import { getPermissions } from '../../../utils/PermUtil';
import CategoryDetailsPage from '../../Transactions/CategoryDetailsPage'; // Adjust path as needed
import ReceiptItemsPage from '../../ReceiptItems/ReceiptItemsPage';
import InvestmentsDashboard from '../../Investment/InvestmentsDashboard';
import { useSidebarAIChat } from '../../context/SidebarAIChatContext';
const GlobalLoadingOverlay = ({ darkMode }) => {
  const { loadingStates } = useLoading();
  const isAnyPageLoading = Object.values(loadingStates).some((isLoading) => isLoading);

  return (
    <AnimatePresence>
      {isAnyPageLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className={`fixed inset-0 flex flex-col items-center justify-center bg-opacity-80 z-50 ${themeClasses.container(darkMode)}`}
        >
          <PaymentLoader darkMode={darkMode} />
          {/* <p className={`mt-4 text-sm ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
            Loading...
          </p> */}
        </motion.div>
      )}
    </AnimatePresence>
  );
};

const Canvas = ({ size, isSidebarMinimized, darkMode  }) => {
  const permissions = getPermissions();
  const { handleAIChatOpen, handleAIChatClose } = useSidebarAIChat();
  
  const hasPage = (pageName) => {
    if(permissions) {
      return permissions.some(p => p.page === pageName);
    } else {
      return true;
    }
  };
  const location = useLocation();

  // Optional theme values (adjust as needed)
  const off = false;
  const turnOff = () => off;
  const theme = {
    containerClr: darkMode ? 'bg-gray-900' : 'bg-white',
    canvasClr: darkMode ? 'bg-gray-900' : 'bg-white',
  };

   return (
<LoadingProvider>
      <div
        className={`
          ${size} 
          mt-0 
          w-full
          ${isSidebarMinimized ? 'md:ml-28' : 'md:ml-54'} 
          ml-0 
          rounded-md 
          transition-all 
          duration-300
          ${darkMode ? 'dark:bg-gray-900 dark:text-white' : 'bg-white text-gray-900'}
        `}
      >
        <GlobalLoadingOverlay darkMode={darkMode} />

        <Routes>
          <Route path="/dashboard" element={<Dashboard darkMode={darkMode} />} />
          <Route path="/dashboard/transactions" element={<TransactionPage1 darkMode={darkMode} onAIChatOpen={handleAIChatOpen} onAIChatClose={handleAIChatClose} />} />
          <Route path="/dashboard/accounts" element={<AccountsDashboard darkMode={darkMode} onAIChatOpen={handleAIChatOpen} onAIChatClose={handleAIChatClose} />} />
          <Route path="/dashboard/bookkeeping" element={<Documents darkMode={darkMode} />} />
          <Route path="/dashboard/fammanager" element={<FamilyDocumentManager darkMode={darkMode} />} />

          <Route path="/dashboard/RecurringTransactions" element={<RecurringTransactionsView darkMode={darkMode} />} />
          {/* <Route path="/dashboard/budget" element={<Budget5 darkMode={darkMode} />} /> */}
          <Route path="/dashboard/budget" element={<Budget6 darkMode={darkMode} onAIChatOpen={handleAIChatOpen} onAIChatClose={handleAIChatClose} />} />
          {/* <Route path="/dashboard/Reconciliation" element={<Reconcile darkMode={darkMode} />} /> */}
          <Route path="/dashboard/investment" element={<Investment darkMode={darkMode} />} />
          <Route path="/dashboard/investmentsdashboard" element={<InvestmentsDashboard darkMode={darkMode} />} />
          <Route path="/dashboard/goals" element={<Goals darkMode={darkMode} />} />
          <Route path="/dashboard/cashflow" element={<CashFlow darkMode={darkMode} onAIChatOpen={handleAIChatOpen} onAIChatClose={handleAIChatClose} />} />
          <Route path="/dashboard/settings" element={<SettingsPage darkMode={darkMode} />} />
          <Route path="/dashboard/stripe" element={<StripeDashboard darkMode={darkMode} />} />
          <Route path="/dashboard/chatbot" element={<Chatbot darkMode={darkMode} />} />
          {/* <Route path="/dashboard/custom-charts" element={<CustomChartsDashboard darkMode={darkMode} />} /> */}
<Route path="/dashboard/receiptitems/:receiptId?" element={<ReceiptItemsPage />} />
          <Route path="/dashboard/category-details/:categoryId/:subCategoryId?" element={<CategoryDetailsPage darkMode={darkMode} />} />
        </Routes>
      </div>
    </LoadingProvider>
  );
};

export default Canvas;
