import React from 'react';
import { useEffect, useRef, useState } from 'react';
import { Download, Loader2, Save } from 'lucide-react';
import { axiosInstance } from '../../../../../logic/api/axiosConfig';

import {
  <PERSON><PERSON>hart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from 'recharts';
import html2canvas from 'html2canvas';
import * as XLSX from 'xlsx';
import { themeClasses } from '../../../utils/tailwindUtils'; // Adjust path as needed
import { fixColorsForCanvas, simpleFixColorsForCanvas } from '../../../utils/colorUtils';

const COLORS = [
  '#8884d8', '#82ca9d', '#ffc658', '#ff8042', '#8dd1e1',
  '#a4de6c', '#d0ed57', '#ffbb28', '#ff6666', '#aa00ff'
];

const ChatbotVisualizer = ({ 
  visualization, 
  userId, 
  onChartSaved, 
  hideTitle = false,
  hideSaveButton = false,
  hideDownloadButton = false,
  darkMode, // Added darkMode prop
}) => {
  if (!visualization?.type || !visualization?.data) return null;
  
  const visualRef = useRef(null);

  const data = visualization.data;
  const title = visualization.title || 'Visualization';
  const chatId = visualization.chatId;
  const isSaved1 = visualization.isSaved || false;
  const [showAllDetails, setShowAllDetails] = useState(false);
  const chartHeight = 250;

  const [isSaving, setIsSaving] = useState(false);
  const [isSaved, setIsSaved] = useState(isSaved1);

  // Helper to sort and group pie chart data
  const getPieChartData = (data, topN = 6) => {
    const sorted = [...data].sort((a, b) => b.value - a.value);
    const topData = sorted.slice(0, topN);
    const otherData = sorted.slice(topN);
    const otherTotal = otherData.reduce((acc, d) => acc + d.value, 0);
    if (otherTotal > 0) {
      topData.push({ label: 'Other', value: otherTotal });
    }
    return topData;
  };

  const saveChart = async () => {
    if (isSaved || !visualization || !userId || !chatId) return;

    setIsSaving(true);
    try {
      const payload = {
        userId,
        chatId,
        title,
        type: visualization.type,
        data: JSON.stringify(data),
      };

      await axiosInstance.post('pennypal/api/v1/charts/create', payload);
      setIsSaved(true);
      // Notify parent component of successful save
      if (onChartSaved) {
        onChartSaved(chatId);
      }
    } catch (error) {
      console.error('Save chart error:', error);
    } finally {
      setIsSaving(false);
    }
  };

  useEffect(() => {
    setIsSaved(visualization?.isSaved || false);
  }, [visualization?.chatId]);

  const fixColors = (element) => {
    // Apply to all elements, including SVG and their children
    const allElements = element.querySelectorAll('*');

    // Color mapping for common cases
    const colorMap = {
      // Text colors
      'text-gray-900': darkMode ? '#f3f4f6' : '#111827',
      'text-gray-800': darkMode ? '#f9fafb' : '#1f2937',
      'text-gray-700': darkMode ? '#d1d5db' : '#374151',
      'text-gray-600': darkMode ? '#9ca3af' : '#4b5563',
      'text-gray-500': darkMode ? '#6b7280' : '#6b7280',
      'text-gray-400': darkMode ? '#4b5563' : '#9ca3af',
      'text-gray-300': darkMode ? '#374151' : '#d1d5db',
      'text-gray-200': darkMode ? '#1f2937' : '#e5e7eb',
      'text-gray-100': darkMode ? '#111827' : '#f3f4f6',
      'text-white': '#ffffff',
      'text-black': '#000000',

      // Background colors
      'bg-white': '#ffffff',
      'bg-gray-900': '#111827',
      'bg-gray-800': '#1f2937',
      'bg-gray-700': '#374151',
      'bg-gray-600': '#4b5563',
      'bg-gray-500': '#6b7280',
      'bg-gray-400': '#9ca3af',
      'bg-gray-300': '#d1d5db',
      'bg-gray-200': '#e5e7eb',
      'bg-gray-100': '#f3f4f6',
      'bg-gray-50': '#f9fafb',

      // Fill colors for SVG
      'fill-gray-900': darkMode ? '#f3f4f6' : '#111827',
      'fill-gray-800': darkMode ? '#f9fafb' : '#1f2937',
      'fill-gray-700': darkMode ? '#d1d5db' : '#374151',
      'fill-gray-600': darkMode ? '#9ca3af' : '#4b5563',
      'fill-gray-500': darkMode ? '#6b7280' : '#6b7280',
      'fill-gray-400': darkMode ? '#4b5563' : '#9ca3af',
      'fill-gray-300': darkMode ? '#374151' : '#d1d5db',
      'fill-gray-200': darkMode ? '#1f2937' : '#e5e7eb',
      'fill-gray-100': darkMode ? '#111827' : '#f3f4f6',
      'fill-white': '#ffffff',
      'fill-black': '#000000',

      // Stroke colors for SVG
      'stroke-gray-600': darkMode ? '#9ca3af' : '#4b5563',
      'stroke-gray-400': darkMode ? '#4b5563' : '#9ca3af',
      'stroke-gray-300': darkMode ? '#374151' : '#d1d5db',
      'stroke-black': '#000000',
      'stroke-white': '#ffffff'
    };

    allElements.forEach((el) => {
      const computedStyle = getComputedStyle(el);

      // Handle computed style colors
      const color = computedStyle.color;
      const bgColor = computedStyle.backgroundColor;
      const fill = computedStyle.fill;
      const stroke = computedStyle.stroke;

      // Fix oklch and other unsupported color functions
      if (color && (color.includes('oklch') || color.includes('color(') || color.includes('lab(') || color.includes('lch('))) {
        el.style.color = darkMode ? '#f3f4f6' : '#111827'; // Default text color
      }
      if (bgColor && (bgColor.includes('oklch') || bgColor.includes('color(') || bgColor.includes('lab(') || bgColor.includes('lch('))) {
        el.style.backgroundColor = darkMode ? '#1f2937' : '#ffffff'; // Default background color
      }
      if (fill && fill !== 'none' && (fill.includes('oklch') || fill.includes('color(') || fill.includes('lab(') || fill.includes('lch('))) {
        el.style.fill = darkMode ? '#f3f4f6' : '#111827'; // Default fill color
      }
      if (stroke && stroke !== 'none' && (stroke.includes('oklch') || stroke.includes('color(') || stroke.includes('lab(') || stroke.includes('lch('))) {
        el.style.stroke = darkMode ? '#9ca3af' : '#4b5563'; // Default stroke color
      }

      // Handle inline style colors
      if (el.style.color && (el.style.color.includes('oklch') || el.style.color.includes('color(') || el.style.color.includes('lab(') || el.style.color.includes('lch('))) {
        el.style.color = darkMode ? '#f3f4f6' : '#111827';
      }
      if (el.style.backgroundColor && (el.style.backgroundColor.includes('oklch') || el.style.backgroundColor.includes('color(') || el.style.backgroundColor.includes('lab(') || el.style.backgroundColor.includes('lch('))) {
        el.style.backgroundColor = darkMode ? '#1f2937' : '#ffffff';
      }
      if (el.style.fill && el.style.fill !== 'none' && (el.style.fill.includes('oklch') || el.style.fill.includes('color(') || el.style.fill.includes('lab(') || el.style.fill.includes('lch('))) {
        el.style.fill = darkMode ? '#f3f4f6' : '#111827';
      }
      if (el.style.stroke && el.style.stroke !== 'none' && (el.style.stroke.includes('oklch') || el.style.stroke.includes('color(') || el.style.stroke.includes('lab(') || el.style.stroke.includes('lch('))) {
        el.style.stroke = darkMode ? '#9ca3af' : '#4b5563';
      }

      // Also check class names and apply appropriate colors
      const classList = Array.from(el.classList);
      classList.forEach(className => {
        if (colorMap[className]) {
          if (className.startsWith('text-')) {
            el.style.color = colorMap[className];
          } else if (className.startsWith('bg-')) {
            el.style.backgroundColor = colorMap[className];
          } else if (className.startsWith('fill-')) {
            el.style.fill = colorMap[className];
          } else if (className.startsWith('stroke-')) {
            el.style.stroke = colorMap[className];
          }
        }
      });
    });
  };

  const downloadChart = async () => {
    if (visualization.type === 'table') {
      // Convert table data to worksheet
      const worksheet = XLSX.utils.json_to_sheet(visualization.data);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

      // Trigger download
      XLSX.writeFile(workbook, `${visualization.title || 'table'}.xlsx`);
    } else if (visualRef.current) {
      // Download chart as PNG
      setShowAllDetails(true); // ✅ Show all dots before download
      await new Promise(resolve => setTimeout(resolve, 100)); // Wait for UI update
      // Clone the DOM element to avoid modifying the original
      const clone = visualRef.current.cloneNode(true);
      document.body.appendChild(clone); // Temporarily append to DOM (off-screen)
      clone.style.position = 'absolute';
      clone.style.left = '-9999px'; // Move off-screen
      clone.style.pointerEvents = 'none'; // Prevent interaction
      clone.style.width = visualRef.current.offsetWidth + 'px';
      clone.style.height = visualRef.current.offsetHeight + 'px';

      // Apply color fixes to the clone
      try {
        try {
          fixColorsForCanvas(clone, darkMode);
          console.log('Advanced color fixes applied successfully');
        } catch (advancedError) {
          console.warn('Advanced color fixes failed, trying simple fix:', advancedError);
          simpleFixColorsForCanvas(clone, darkMode);
          console.log('Simple color fixes applied as fallback');
        }
      } catch (colorError) {
        console.warn('All color fixes failed:', colorError);
      }

      try {
        const canvas = await html2canvas(clone, {
          scale: 2, // Increase resolution
          logging: false,
          useCORS: true,
          allowTaint: true,
          backgroundColor: darkMode ? '#1f2937' : '#ffffff',
          onclone: (clonedDoc) => {
            // Additional color fixes on the cloned document
            try {
              const clonedElements = clonedDoc.querySelectorAll('*');
              clonedElements.forEach(el => {
                const style = el.style;
                // Force replace any remaining oklch colors
                if (style.color && style.color.includes('oklch')) {
                  style.color = darkMode ? '#f3f4f6' : '#111827';
                }
                if (style.backgroundColor && style.backgroundColor.includes('oklch')) {
                  style.backgroundColor = darkMode ? '#1f2937' : '#ffffff';
                }
                if (style.fill && style.fill.includes('oklch')) {
                  style.fill = darkMode ? '#f3f4f6' : '#111827';
                }
                if (style.stroke && style.stroke.includes('oklch')) {
                  style.stroke = darkMode ? '#9ca3af' : '#4b5563';
                }
              });
            } catch (oncloneError) {
              console.warn('Error in onclone color fixes:', oncloneError);
            }
          }
        });
        const link = document.createElement('a');
        link.download = `${visualization.title || 'chart'}.png`;
        link.href = canvas.toDataURL('image/png');
        link.click();
        console.log('Chart downloaded successfully');
      } catch (error) {
        console.error('Error generating canvas:', error);
        // Show user-friendly error message
        alert('Error downloading chart. This might be due to unsupported color formats. Please try again or contact support.');
      } finally {
        // Clean up: remove the cloned element
        document.body.removeChild(clone);
        setShowAllDetails(false); // ✅ Reset after download
      }
    }
  };

  if (!visualization?.type || !data) return null;

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const totalPages = Math.ceil(data.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const paginatedData = data.slice(startIndex, startIndex + itemsPerPage);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className={`my-2 p-4 rounded shadow w-full ${themeClasses.chartContainer(darkMode)} ${themeClasses.chartTitle(darkMode)}`}>
      <h3 className={`text-base font-semibold mb-2 flex items-center justify-between ${themeClasses.chartTitle(darkMode)}`}>
        {!hideTitle && <span>{title}</span>}
        <div className="flex items-center gap-2">
          {!hideSaveButton && (
            <button
              onClick={saveChart}
              disabled={isSaved || isSaving}
              className={`flex items-center gap-1 px-2 py-1 text-xs rounded-full border shadow transition ${
                isSaved ? themeClasses.saveButtonSaved(darkMode) :
                isSaving ? themeClasses.saveButtonSaving(darkMode) :
                themeClasses.saveButton(darkMode)
              }`}
            >
              {isSaving ? <Loader2 size={14} className="animate-spin" /> : null}
              {isSaved ? 'Chart Saved' : isSaving ? 'Saving...' : 'Save To Dashboard'}
            </button>
          )}
          {!hideDownloadButton && (
            <button
              onClick={downloadChart}
              className={`p-1 rounded-full shadow transition ${themeClasses.downloadButton(darkMode)}`}
              title="Download"
            >
              <Download size={16} className={`${themeClasses.icon(darkMode)}`} />
            </button>
          )}
        </div>
      </h3>
      <div ref={visualRef}>
        {visualization.type === 'bar_chart' && (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <BarChart
              data={data}
              barCategoryGap={30}
              maxBarSize={50}
              margin={{ top: 10, right: 30, left: 10, bottom: 10 }}
            >
              <XAxis dataKey="label" tick={{ fontSize: 12, fill: themeClasses.svgText(darkMode).replace('fill-', '#') }} />
              <YAxis tick={{ fontSize: 12, fill: themeClasses.svgText(darkMode).replace('fill-', '#') }} />
              <Tooltip
                formatter={(value) => [`$${value.toFixed(2)}`]}
                labelStyle={{ fontWeight: 'bold', color: themeClasses.tableCell(darkMode).split(' ')[0].replace('text-', '#') }}
                contentStyle={{ backgroundColor: themeClasses.chartContainer(darkMode).split(' ')[0].replace('bg-', '#'), color: themeClasses.tableCell(darkMode).split(' ')[0].replace('text-', '#') }}
              />
              <Legend wrapperStyle={{ fontSize: 12, color: themeClasses.tableCell(darkMode).split(' ')[0].replace('text-', '#') }} />
              <Bar
                dataKey="value"
                radius={[3, 3, 0, 0]}
                label={showAllDetails ? { position: 'top', formatter: val => `$${val.toFixed(2)}`, fontSize: 10, fill: themeClasses.svgText(darkMode).replace('fill-', '#') } : false}
              >
                {data.map((_, index) => (
                  <Cell key={`cell-${index}`} fill={themeClasses.chartColors(darkMode)[index % themeClasses.chartColors(darkMode).length]} />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        )}

        {visualization.type === 'line_chart' && (
          <ResponsiveContainer width="100%" height={chartHeight}>
            <LineChart data={data} margin={{ top: 10, right: 30, left: 10, bottom: 10 }}>
              <XAxis dataKey="label" tick={{ fontSize: 12, fill: themeClasses.svgText(darkMode).replace('fill-', '#') }} />
              <YAxis tick={{ fontSize: 12, fill: themeClasses.svgText(darkMode).replace('fill-', '#') }} />
              <Tooltip
                formatter={(value) => [`$${value.toFixed(2)}`]}
                labelStyle={{ fontWeight: 'bold', color: themeClasses.tableCell(darkMode).split(' ')[0].replace('text-', '#') }}
                contentStyle={{ backgroundColor: themeClasses.chartContainer(darkMode).split(' ')[0].replace('bg-', '#'), color: themeClasses.tableCell(darkMode).split(' ')[0].replace('text-', '#') }}
              />
              <Legend wrapperStyle={{ fontSize: 12, color: themeClasses.tableCell(darkMode).split(' ')[0].replace('text-', '#') }} />
              <Line
                type="monotone"
                dataKey="value"
                stroke={themeClasses.chartColors(darkMode)[0]}
                strokeWidth={2}
                dot={showAllDetails ? { stroke: themeClasses.chartColors(darkMode)[0], fill: themeClasses.chartColors(darkMode)[0] } : false}
              />
            </LineChart>
          </ResponsiveContainer>
        )}

        {visualization.type === 'pie_chart' && (
          <ResponsiveContainer width="100%" height={chartHeight + 120}>
            <PieChart>
              <Pie
                data={getPieChartData(data)}
                dataKey="value"
                nameKey="label"
                cx="50%"
                cy="45%"
                outerRadius={100}
                labelLine={false}
                label={({ name, percent }) =>
                  showAllDetails || percent > 0.05 ? `${name} (${(percent * 100).toFixed(0)}%)` : ''
                }
              >
                {getPieChartData(data).map((_, index) => (
                  <Cell key={`cell-${index}`} fill={themeClasses.chartColors(darkMode)[index % themeClasses.chartColors(darkMode).length]} />
                ))}
              </Pie>
              <Tooltip
                formatter={(value) => `$${value.toFixed(2)}`}
                contentStyle={{ backgroundColor: themeClasses.chartContainer(darkMode).split(' ')[0].replace('bg-', '#'), color: themeClasses.tableCell(darkMode).split(' ')[0].replace('text-', '#') }}
              />
              <Legend
                verticalAlign="bottom"
                align="center"
                layout="horizontal"
                iconType="circle"
                wrapperStyle={{ fontSize: 12, color: themeClasses.tableCell(darkMode).split(' ')[0].replace('text-', '#') }}
              />
            </PieChart>
          </ResponsiveContainer>
        )}

        {visualization.type === 'table' && (
          <div className="overflow-x-auto mt-2">
            <table className={`min-w-[300px] text-left ${themeClasses.tableBorder(darkMode)} table-auto`}>
              <thead className={`${themeClasses.tableHeader(darkMode)}`}>
                <tr>
                  {Object.keys(data[0]).map((key) => (
                    <th key={key} className={`px-2 py-1 border-b border-r ${themeClasses.tableCell(darkMode)}  ${themeClasses.tableRowBorder(darkMode)} text-sm font-medium`}>{key}</th>
                  ))}
                </tr>
              </thead>
              <tbody className={`${themeClasses.tableBody(darkMode)}`}>
                {paginatedData.map((row, idx) => (
                  <tr key={idx} className={`border-t ${themeClasses.tableRowContainer(darkMode)} ${themeClasses.tableRowBorder(darkMode)}`}>
                    {Object.values(row).map((value, i) => (
                      <td key={i} className={`px-2 py-1 border-b border-r ${themeClasses.tableCell(darkMode)}  ${themeClasses.tableRowBorder(darkMode)} text-sm`}>{value}</td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>

            <div className="flex justify-center items-center mt-4 min-h-[40px] space-x-1">
              {[...Array(totalPages)].map((_, index) => {
                const pageNum = index + 1;
                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageChange(pageNum)}
                    className={`px-3 py-1 rounded border ${
                      pageNum === currentPage ? themeClasses.paginationButtonActive(darkMode) : themeClasses.paginationButton(darkMode)
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatbotVisualizer;