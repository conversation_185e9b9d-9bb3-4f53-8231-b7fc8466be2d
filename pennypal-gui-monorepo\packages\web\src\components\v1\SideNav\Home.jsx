import React, { useState,useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {  faClipboardList, faDollarSign, faFileAlt, faWallet,faReceipt,faHome } from '@fortawesome/free-solid-svg-icons';
import { useLocation } from 'react-router-dom';
import { setAuthToken } from '../../../../../logic/api/axiosConfig';

import logo from '../../../assets/pennypal_logo.png';

import SideNav from './SideNav';
import Canvas from './Canvas';
import axios from "axios";
import SignIn from '../../auth/signIn';
import SetPassword from '../../auth/setPassword';
import Notifications from './Settings/NotificationRulesModal';
import ForgotPassword from '../../auth/forgotPassword';
// import CreditKarmaClone from './Credit'; // Import CreditKarmaClone
import PennyPalHomepage from './Credit'; // Import PennyPalHomepage
import { SidebarAIChatProvider, useSidebarAIChat } from '../../context/SidebarAIChatContext';

const API_URL = import.meta.env.VITE_API_URL;

// Inner component that uses the context
const HomeContent = () => {
  const { isSidebarMinimized, handleSidebarToggle, setSidebarMinimized } = useSidebarAIChat();

  const [sideNavSize , setSideNaveSize] = useState('w-2/9');
  const [canvasSize , setCanvasSize]    = useState('w-7/9');

  const [isAuthenticated, setIsAuthenticated] = useState(false);
    const [showNotifications, setShowNotifications] = useState(false); // New state for notifications
    const [showPennyPalHomepage, setShowPennyPalHomepage] = useState(false); // New state for notifications
    const [showLandingpage, setShowlandingpage] = useState(false); // New state for notifications

  // To display set password page
  const [isSetPassword, setIsSetPassword] = useState(false);
  const location = useLocation();
  useEffect(() => {
    console.log("Location is --> " + location.pathname);
    if (location.pathname === '/set-password') {
      setIsSetPassword(true);
    } else {
      setIsSetPassword(false);
    }
  }, [location.pathname]);

  // To display forgot password page
  const [isForgotPassword, setIsForgotPassword] = useState(false);
  useEffect(() => {
    console.log("Location is --> " + location.pathname);
    if (location.pathname === '/forgot-password') {
      setIsForgotPassword(true);
    } else {
      setIsForgotPassword(false);
    }
  }, [location.pathname]);

  // Check if refresh token is present
  useEffect(() => {
    const checkRefreshToken = async () => {
      const refreshToken = localStorage.getItem('refreshToken');
      console.log("Refresh token is --> " + refreshToken);
      if (refreshToken) {
        const plainAxios = axios.create({
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 10000
        });
        try {
          const res = await plainAxios.post(
            `${API_URL}/pennypal/api/v1/auth/refresh-token`,
            { token: localStorage.getItem("refreshToken") },
            { headers: {} }
          );
          console.log("Refresh token response is --> " + res.status, res.data.token);
          if (res.status === 200) {
            setAuthToken(res.data.token);
            setIsAuthenticated(true);
          } else {
            console.error('Failed to refresh token:', res.data);
            setIsAuthenticated(false);
          }
        } catch (err) {
          console.error('Failed to refresh token:', err);
          setIsAuthenticated(false);
        }
      } else {
        setIsAuthenticated(false);
      }
    };
    
    checkRefreshToken();
  }, []);

  const navigate = useNavigate();
  const handleLoginSuccess = () => {
    setIsAuthenticated(true);
    navigate('/dashboard');
  };

  const [darkMode, setDarkMode] = useState(false);
  // ... other state variables

  const toggleDarkMode = () => {
    setDarkMode(!darkMode);
    // Optional: Save to localStorage
    localStorage.setItem('darkMode', !darkMode);
  };
  useEffect(() => {
    // Optional: Load from localStorage
    const savedDarkMode = localStorage.getItem('darkMode') === 'true';
    setDarkMode(savedDarkMode);
  }, []);

  useEffect(() => {
    // This code runs after `isSidebarMinimized` has been updated
    console.log('Sidebar state has changed:', isSidebarMinimized);
    console.log("*************");
    console.log(import.meta);
    console.log(import.meta.env.VITE_API_URL);
   
    
    
    console.log(import.meta.env.VITE_APP_NAME);

    // You can put any additional callback logic here
    // For example, logging, triggering animations, etc.
    if (isSidebarMinimized) {
      // Your logic when the sidebar is minimized
      console.log('Sidebar is minimized');
    } else {
      // Your logic when the sidebar is expanded
      console.log('Sidebar is expanded');
    }
    const navSize = isSidebarMinimized ? "w-1/14"  : 'w-2/14';
    const cnvSize = isSidebarMinimized ? "w-13/14" : 'w-12/14';
    setSideNaveSize(navSize);
    setCanvasSize(cnvSize);
  }, [isSidebarMinimized]); // isSidebarMinimized is being watched , called back whenever that value is changed..

  const off = false;
  const turnOff = () => {
      return off;
  }

  const theme = {
    containerClr :  ( turnOff() && 1) ? "bg-blue-200"  : '',
    canvasClr :     ( turnOff() && 1) ? "bg-green-500" : '',
    sideNavMnuClr : ( turnOff() && 1) ? "bg-yellow-500": '',
    sideNavHdrClr : ( turnOff() && 1) ? "bg-red-500"   : ''
  }

  const toggleNotifications = () => {
    setShowNotifications(!showNotifications);
  };
 const togglePennyPalHomepage = () => {
    setShowPennyPalHomepage(!showPennyPalHomepage);
  };
  const toggleLandingpage = () => {
    setShowlandingpage(!showLandingpage);
  };
  const sideNavSlideInCallback = () => {
    console.log(" sideNavSlideInCallback is called ....")
    handleSidebarToggle();
  }


  return (
      <div className="w-full h-full  ${darkMode ? 'dark' : ''}">
          {isSetPassword ? (
            <SetPassword />
          ) : 
          isForgotPassword ? (
            <ForgotPassword />
          ) :
          isAuthenticated ? (
                                <div className="relative w-full h-full">
{!showPennyPalHomepage && !showLandingpage ? (
              <nav id="container" className={`${theme.containerClr} container flex h-full max-w-full pr-1`}>
              <div className="flex h-screen w-screen overflow-x-hidden">
            <SideNav
              size={`${sideNavSize} fixed top-0 left-0 h-screen overflow-hidden`}
              callback={sideNavSlideInCallback}
              slideIn={isSidebarMinimized}
              darkMode={darkMode}
              toggleDarkMode={toggleDarkMode}
              toggleNotifications={toggleNotifications} // Pass toggle function to SideNav
              togglePennyPalHomepage={togglePennyPalHomepage} // Pass toggle function to SideNav
              toggleLandingpage={toggleLandingpage} // Pass toggle function to SideNav
            />
            <Canvas size={canvasSize} isSidebarMinimized={isSidebarMinimized}  darkMode={darkMode}/>
          </div>
            </nav>
              )  : showPennyPalHomepage ? (
          <div className="absolute inset-0 h-full w-full z-50 bg-white dark:bg-gray-900">
            <PennyPalHomepage onClose={togglePennyPalHomepage} />
          </div>
       ) : showLandingpage ? (
  <div className="absolute inset-0 h-full w-full z-50 bg-white dark:bg-gray-900">
    <LandingPage onClose={toggleLandingpage} />
  </div>
) : null}
          {/* Centered Notifications Overlay */}
          {showNotifications && (
            <div className="fixed ml-40 mt-15 inset-0 flex z-50">
              <Notifications
                slideIn={isSidebarMinimized}
                darkMode={darkMode}
                onClose={toggleNotifications} // Pass close function
              />
            </div>
          )}
        </div>
          ) : (
            <SignIn onLoginSuccess={handleLoginSuccess} />
          )}
    </div>
  );
}

// Main component that provides the context
const Home = () => {
  return (
    <SidebarAIChatProvider>
      <HomeContent />
    </SidebarAIChatProvider>
  );
};

export default Home;
