import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  selectRecurringTransactions,
  selectLoading,
  selectError,
  selectSelectedMonth,
  selectSelectedYear,
  selectViewMode,
  fetchRecurringTransactionsStart,
  setSelectedMonth,
  setSelectedYear,
  setViewMode,
  goToPrevMonth,
  goToNextMonth,
  goToCurrentMonth
} from '../../../../../logic/redux/recurringTransactionsSlice';
import PaymentLoader from '../../load/PaymentLoader'; // Import PaymentLoader
import { FaTimes } from 'react-icons/fa'; // Import FaTimes for modal close button
import { themeClasses } from '../../../utils/tailwindUtils'; // Adjust path as needed
import { useCacheAwareness } from '../../../../../logic/components/CacheAwareWrapper';
import { getCurrentUserId } from '../../../utils/AuthUtil';

const RecurringTransactionsView = ({ darkMode }) => {
  const dispatch = useDispatch();
  const recurringTransactions = useSelector(selectRecurringTransactions);
  const loading = useSelector(selectLoading);
  const error = useSelector(selectError);
  const selectedMonth = useSelector(selectSelectedMonth);
  const selectedYear = useSelector(selectSelectedYear);
  const viewMode = useSelector(selectViewMode);

  // Cache awareness for recurring transactions
  const {
    isReady: cacheReady,
    isCacheLoaded,
    getCacheData,
    isCacheLoading
  } = useCacheAwareness(['recurringTransactions', 'futureRecurringTransactions'], false);
  
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const currentDate = new Date();
  const monthNames = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];

  useEffect(() => {
    dispatch(fetchRecurringTransactionsStart());
  }, [dispatch]);

  const uniqueMerchants = new Set(recurringTransactions.map(t => t.merchant));
  const totalUniqueMerchants = uniqueMerchants.size;

  const merchantList = Array.from(uniqueMerchants).map(merchant => ({
    name: merchant
  }));

  // Helper function to generate calendar days
  const generateCalendarDays = (year, month) => {
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();
    
    const days = [];
    
    // Add empty cells for days before the 1st of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push({ day: '', empty: true });
    }
    
    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push({ day, empty: false });
    }
    
    return days;
  };

  // Handle month change
  const handleMonthChange = (e) => {
    dispatch(setSelectedMonth(parseInt(e.target.value)));
  };

  // Handle year change
  const handleYearChange = (e) => {
    dispatch(setSelectedYear(parseInt(e.target.value)));
  };
  
  // Generate calendar days
  const calendarDays = generateCalendarDays(selectedYear, selectedMonth);
  
  // Get transactions for the selected month
  const transactionsThisMonth = recurringTransactions.filter(transaction => {
    const transactionDate = new Date(transaction.date);
    return transactionDate.getMonth() === selectedMonth && transactionDate.getFullYear() === selectedYear;
  });
  
  // Helper to get transactions for a specific day
  const getTransactionsForDay = (day) => {
    if (!day) return [];
    
    const dateString = `${selectedYear}-${String(selectedMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    return recurringTransactions.filter(transaction => transaction.date === dateString);
  };
  
  // Calculate total amount for the month
  const totalMonthAmount = transactionsThisMonth.reduce((sum, transaction) => sum + transaction.amount, 0);
  
  // Toggle between views
  const toggleView = (mode) => {
    dispatch(setViewMode(mode));
  };
  
  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency', currency: 'USD',
      currencyDisplay: 'narrowSymbol'
    }).format(amount);
  };
  // Update category colors with dark mode variants
  const getCategoryStyle = (category) => {
    switch (category) {
      case 'Housing': return themeClasses.badgeCategoryHousing(darkMode);
      case 'Entertainment': return themeClasses.badgeCategoryEntertainment(darkMode);
      case 'Health': return themeClasses.badgeCategoryHealth(darkMode);
      case 'Utilities': return themeClasses.badgeCategoryUtilities(darkMode);
      default: return themeClasses.badgeCategoryDefault(darkMode);
    }
  };

  if (loading) {
    return (
      <div className={`min-h-screen w-full p-5 flex flex-col justify-center items-center ${themeClasses.container(darkMode)}`}>
        <PaymentLoader darkMode={darkMode} />
        {/* <p className={`mt-2 text-sm ${themeClasses.loadingText(darkMode)}`}>
          Loading recurring transactions...
        </p> */}
      </div>
    );
  }


  // Update error display
  if (error) {
    return (
      <div className={`w-full max-w-4xl mx-auto p-4 ${themeClasses.container(darkMode)}`}>
        <div className={`border px-4 py-3 rounded relative ${themeClasses.error(darkMode)}`} role="alert">
          <strong className={`font-bold ${themeClasses.errorText(darkMode)}`}>Error:</strong>
          <span className={`block sm:inline ${themeClasses.errorText(darkMode)}`}> Could not load transactions. {error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen w-full mx-auto p-4 ${themeClasses.container(darkMode)}`}>
      <div className="flex justify-between items-center mb-4">
        <h1 className={`flex text-2xl items-center ${themeClasses.container(darkMode)}`}>Recurring Transactions</h1>
        <div>
          <h2 className="text-m">You have a total of{" "}
            <span className={`font-bold ${themeClasses.merchantCount(darkMode)}`}>
              {totalUniqueMerchants}
            </span> {" "}
            active recurring merchants
          </h2>
          
          {isModalOpen && (
            <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30">
              <div className={`${themeClasses.modal(darkMode)} p-4 rounded-lg shadow-lg relative max-w-md w-full`}>
                <button
                  className={`absolute top-2 right-2 text-xl font-bold rounded-full px-2 ${themeClasses.modalClose(darkMode)}`}
                  onClick={() => setIsModalOpen(false)}
                >
                  <FaTimes />
                </button>

                <h3 className="text-base font-semibold mb-2">Recurring Merchants</h3>
                <ul className="max-h-60 overflow-y-auto">
                  {merchantList.map((merchant, index) => (
                    <li key={index} className="flex justify-between">
                      <span>{merchant.name}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Controls Section */}
      <div className={`mb-4 p-4 rounded-lg border ${themeClasses.controlContainer(darkMode)}`}>
        <div className="flex flex-col">
          <div>
            <div className="flex flex-wrap items-center">
              <button
                onClick={() => dispatch(goToCurrentMonth())}
                className={`text-xs rounded-md px-2 py-1 border ${themeClasses.navButton(darkMode)}`}
              >
                Today
              </button>

              <div className={`inline-flex items-center rounded-lg border ${themeClasses.navButton(darkMode)} ml-4`}>
                <select
                  value={selectedMonth}
                  onChange={handleMonthChange}
                  className={`appearance-none font-medium text-sm px-2 py-1 ${themeClasses.selectInput(darkMode)}`}
                >
                  {monthNames.map((m, i) => (
                    <option key={i} value={i}>{m}</option>
                  ))}
                </select>
                
                <select 
                  value={selectedYear}
                  onChange={handleYearChange}
                  className={`appearance-none font-medium text-sm px-2 py-1 ${themeClasses.selectInput(darkMode)}`}
                >
                  {[...Array(10)].map((_, i) => {
                    const year = currentDate.getFullYear() - 3 + i;
                    return <option key={year} value={year}>{year}</option>;
                  })}
                </select>

                <button
                  onClick={() => dispatch(goToPrevMonth())}
                  className="px-2"
                  aria-label="Previous month"
                >‹</button>
                <button
                  onClick={() => dispatch(goToNextMonth())}
                  className="px-2"
                  aria-label="Next month"
                >›</button>
              </div>

              <div className="inline-flex border border-green-600 rounded-md overflow-hidden ml-auto">
                <button
                  onClick={() => toggleView('grid')}
                  className={`px-3 py-1 font-medium ${viewMode === 'grid' ? themeClasses.viewToggleActive(darkMode) : themeClasses.viewToggle(darkMode)}`}
                >
                  Grid
                </button>
                <button
                  onClick={() => toggleView('calendar')}
                  className={`px-3 py-1 font-medium ${viewMode === 'calendar' ? themeClasses.viewToggleActive(darkMode) : themeClasses.viewToggle(darkMode)}`}
                >
                  Calendar
                </button>
              </div>
            </div>

            

            <h2 className="text-s font-bold">
              Total Recurring Transactions: <span className={themeClasses.totalText(darkMode)}>{transactionsThisMonth.length}</span>
              <span className="ml-5">
                Total Recurring Expense: <span className={themeClasses.totalText(darkMode)}>{formatCurrency(totalMonthAmount)}</span>
              </span>
            </h2>
          </div>
        </div>
      </div>
      
      {/* Calendar View */}
      {viewMode === 'calendar' && (
        <div className={`rounded-xl shadow overflow-hidden ${themeClasses.cardContainer(darkMode)}`}>
          <div className={`grid grid-cols-7 ${themeClasses.calendarHeader(darkMode)} border-b`}>
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
              <div key={day} className="p-2 text-center font-medium">
                {day}
              </div>
            ))}
          </div>
          
          <div className={`grid grid-cols-7 gap-px ${themeClasses.calendarGrid(darkMode)}`}>
            {calendarDays.map((dayData, index) => {
              const dayTransactions = getTransactionsForDay(dayData.day);
              const isToday = dayData.day === currentDate.getDate() && 
                        selectedMonth === currentDate.getMonth() && 
                        selectedYear === currentDate.getFullYear() && 
                        !dayData.empty;
              
              return (
                <div
                  key={index}
                  className={`min-h-24 p-2 border border-gray-300 rounded-md shadow-sm ${
                    dayData.empty ? themeClasses.calendarCellEmpty(darkMode) :
                    isToday ? themeClasses.calendarCellToday(darkMode) :
                    themeClasses.calendarCell(darkMode)
                  }`}
                >
                  {!dayData.empty && (
                    <>
                      <div className={`text-right ${isToday ? 'font-bold' : ''}`} style={{ color: isToday ? '#5caa1c' : '' }}>
                        {dayData.day}
                      </div>
                      <div className="mt-1 max-h-16 overflow-hidden overflow-auto hover:overflow-visible">
                        {dayTransactions.map(transaction => (
                          <div key={transaction.id} className="text-xs mb-1 p-1 rounded-lg flex justify-between items-center gap-1 relative group hover:z-20"
                            style={{
                              backgroundColor:
                                transaction.category === 'Housing' ? 'rgba(244, 63, 94, 0.2)' :
                                  transaction.category === 'Entertainment' ? 'rgba(234, 179, 8, 0.2)' :
                                    transaction.category === 'Health' ? 'rgba(16, 185, 129, 0.2)' :
                                      transaction.category === 'Utilities' ? 'rgba(59, 130, 246, 0.2)' :
                                        'rgba(139, 92, 246, 0.2)',
                              borderLeft: `3px solid ${transaction.category === 'Housing' ? 'rgb(244, 63, 94)' :
                                transaction.category === 'Entertainment' ? 'rgb(234, 179, 8)' :
                                  transaction.category === 'Health' ? 'rgb(16, 185, 129)' :
                                    transaction.category === 'Utilities' ? 'rgb(59, 130, 246)' :
                                      'rgb(139, 92, 246)'
                                }`
                            }}>
                            <span className="truncate" style={{ maxWidth: "70%" }}>{transaction.merchant}</span>
                            <span className="font-medium whitespace-nowrap">{formatCurrency(transaction.amount)}</span>
                            {/* Tooltip */}
                            <div className="absolute left-1/2 transform -translate-x-1/2 bottom-full mb-2 hidden w-auto max-w-xs p-2 text-xs rounded-lg shadow-lg group-hover:flex z-50 whitespace-nowrap backdrop-blur-md"
                              style={{
                                backgroundColor:
                                  transaction.category === 'Housing' ? 'rgba(244, 63, 94, 0.2)' :
                                    transaction.category === 'Entertainment' ? 'rgba(234, 179, 8, 0.2)' :
                                      transaction.category === 'Health' ? 'rgba(16, 185, 129, 0.2)' :
                                        transaction.category === 'Utilities' ? 'rgba(59, 130, 246, 0.2)' :
                                          'rgba(139, 92, 246, 0.2)',
                                borderLeft: `3px solid ${transaction.category === 'Housing' ? 'rgb(244, 63, 94)' :
                                  transaction.category === 'Entertainment' ? 'rgb(234, 179, 8)' :
                                    transaction.category === 'Health' ? 'rgb(16, 185, 129)' :
                                      transaction.category === 'Utilities' ? 'rgb(59, 130, 246)' :
                                        'rgb(139, 92, 246)'
                                  }`, minWidth: 'max-content', right: 'auto', left: 'auto'
                              }}>
                              {transaction.merchant} - {formatCurrency(transaction.amount)} - {transaction.frequency}
                            </div>
                          </div>
                        ))}
                      </div>
                    </>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
      
      {/* Grid View */}
      {viewMode === 'grid' && (
        <div className={`rounded-xl shadow overflow-hidden ${themeClasses.cardContainer(darkMode)}`}>
          <table className={`min-w-full divide-y   ${darkMode ? 'divide-gray-600' : 'divide-gray-200'} `}>
            <thead className={themeClasses.tableHeader(darkMode)}>
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Transaction</th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Amount</th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Category</th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Transaction Date</th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Frequency</th>
                <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Account</th>
              </tr>
            </thead>
            <tbody className={themeClasses.tableBody(darkMode)}>
              {recurringTransactions.filter(transaction => {
                const date = new Date(transaction.date);
                return date.getMonth() === selectedMonth && date.getFullYear() === selectedYear;
              })
                .map((transaction) => {
                  const date = new Date(transaction.date);
                  const formattedDate = date.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });

                  return (
                    <tr key={transaction.id} className={themeClasses.hover(darkMode)}>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${themeClasses.tableCell(darkMode)}`}>{transaction.merchant}</td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${themeClasses.tableCell(darkMode)}`}>{formatCurrency(transaction.amount)}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-6 inline-flex text-xs leading-5 font-semibold rounded-full ${getCategoryStyle(transaction.category)}`}>
                          {transaction.category}
                        </span>
                      </td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${themeClasses.loadingText(darkMode)}`}>{formattedDate}</td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${themeClasses.loadingText(darkMode)}`}>{transaction.frequency}</td>
                      <td className={`px-6 py-4 whitespace-nowrap text-sm ${themeClasses.loadingText(darkMode)}`}>{transaction.account}</td>
                    </tr>
                  );
                })}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default RecurringTransactionsView;