import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { UilSave, UilBell, UilSetting } from '@iconscout/react-unicons';
import {
  fetchNotificationRules,
  updateEditedRule,
  saveNotificationRule,
} from '../../../../../../logic/redux/notificationSlice';
import { getCurrentUserId } from '../../../../utils/AuthUtil';
import PaymentLoader from '../../../load/PaymentLoader';

const NotificationSettingsModal = ({ isOpen, onClose, darkMode }) => {
  const dispatch = useDispatch();
  const userId = getCurrentUserId();
  const { rules, editedRules, loading, error } = useSelector((state) => state.notifications || {});
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState('idle');

  useEffect(() => {
    if (isOpen) {
      dispatch(fetchNotificationRules({ userId: userId }));
    }
  }, [isOpen, dispatch]);

  if (!isOpen) return null;

  // APT-193 fix for page loader
  if (loading) {
    return (
      <div className={`min-h-screen w-full flex flex-col justify-center items-center ${darkMode ? 'bg-gray-900' : 'bg-white'}`}>
        <PaymentLoader darkMode={darkMode} />
      </div>
    );
  }

  const handleRuleChange = (id, field, value) => {
    dispatch(updateEditedRule({ id, field, value }));
  };

  const getSeverityColor = (severity) => {
    const colors = {
      LOW: darkMode ? 'bg-green-900 text-green-300 border-green-700' : 'bg-green-50 text-green-700 border-green-200',
      MEDIUM: darkMode ? 'bg-yellow-900 text-yellow-300 border-yellow-700' : 'bg-yellow-50 text-yellow-700 border-yellow-200',
      HIGH: darkMode ? 'bg-orange-900 text-orange-300 border-orange-700' : 'bg-orange-50 text-orange-700 border-orange-200',
      CRITICAL: darkMode ? 'bg-red-900 text-red-300 border-red-700' : 'bg-red-50 text-red-700 border-red-200'
    };
    return colors[severity] || colors.LOW;
  };

  return (
    <div className={`min-h-screen w-full ${darkMode ? 'bg-gray-900' : 'bg-white'} transition-colors duration-200`}>
      <div className={`p-6 max-h-[90vh] overflow-y-auto scrollbar-thin ${darkMode ? 'scrollbar-track-gray-800 scrollbar-thumb-gray-600' : 'scrollbar-track-gray-100 scrollbar-thumb-gray-300'}`}>
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center gap-3">
            <div className={`p-3 rounded-xl ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
              <UilBell size="24" className={darkMode ? 'text-gray-300' : 'text-gray-600'} />
            </div>
            <div>
              <h3 className={`text-3xl font-bold ${darkMode ? 'text-white' : 'text-gray-900'}`}>
                Notification Settings
              </h3>
              <p className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} mt-1`}>
                Configure your alert preferences and thresholds
              </p>
            </div>
          </div>
          {/* Save Button */}
          {rules && rules.length > 0 && (
            <button
              className={`group relative px-6 py-3 rounded-xl font-semibold transition-all duration-200 focus:outline-none focus:ring-4 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2 ${
                darkMode 
                  ? 'bg-gray-700 hover:bg-gray-600 text-white focus:ring-gray-500 shadow-lg hover:shadow-xl' 
                  : 'bg-[#8bc34a] hover:bg-[#6ec122] text-white focus:ring-green-300 shadow-lg hover:shadow-xl'
              } ${isSaving ? 'scale-95' : 'hover:scale-105'}`}
              onClick={() => {
                setSaveStatus('saving');
                setIsSaving(true);

                const rulesToSave = rules
                  .map((originalRule) => {
                    const edited = editedRules[originalRule.ruleId];
                    return JSON.stringify(edited) !== JSON.stringify(originalRule) ? edited : null;
                  })
                  .filter((rule) => rule !== null);

                if (rulesToSave.length > 0) {
                  dispatch(saveNotificationRule(rulesToSave));
                }

                setTimeout(() => {
                  setSaveStatus('saved');
                  setTimeout(() => {
                    setSaveStatus('idle');
                    setIsSaving(false);
                  }, 2000);
                }, 1000);
              }}
              disabled={isSaving}
            >
              {saveStatus === 'saving' ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Saving...</span>
                </>
              ) : saveStatus === 'saved' ? (
                <>
                  <UilSave size="18" />
                  <span>Saved</span>
                </>
              ) : (
                <>
                  <UilSave size="18" />
                  <span>Save</span>
                </>
              )}
              <div className={`absolute inset-0 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${
                darkMode ? 'bg-white/5' : 'bg-white/10'
              }`} />
            </button>
          )}
        </div>
        {/* Rules Container */}
        <div className={`rounded-2xl shadow-xl border ${darkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} overflow-hidden`}>
          {/* Table Header */}
          <div className={`px-6 py-4 border-b ${darkMode ? 'bg-gray-750 border-gray-700' : 'bg-gray-50 border-gray-200'}`}>
            <div className="grid grid-cols-12 gap-8 items-center font-semibold text-sm">
              <div className={`col-span-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Status</div>
              <div className={`col-span-4 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Rule Details</div>
              <div className={`col-span-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Threshold</div>
              <div className={`col-span-2 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Severity</div>
              <div className={`col-span-1 text-center ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Mobile</div>
              <div className={`col-span-1 text-center ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}>Email</div>
              <div className={`col-span-1 ${darkMode ? 'text-gray-300' : 'text-gray-700'}`}></div>
            </div>
          </div>
          {/* Rules List */}
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {rules && rules.length > 0 ? (
              rules.map((rule, index) => {
                const editedRule = editedRules[rule.ruleId] || rule;
                const thresholdField =
                  editedRule.conditionType === 'AMOUNT_THRESHOLD'
                    ? 'amountThreshold'
                    : editedRule.conditionType === 'DAYS_BEFORE'
                    ? 'daysThreshold'
                    : 'percentageThreshold';
                const thresholdValue =
                  editedRule[thresholdField] !== undefined ? editedRule[thresholdField] : rule[thresholdField];
                const thresholdSuffix =
                  editedRule.conditionType === 'AMOUNT_THRESHOLD'
                    ? '$'
                    : editedRule.conditionType === 'DAYS_BEFORE'
                    ? 'Days'
                    : editedRule.conditionType === 'PERCENTAGE_THRESHOLD'
                    ? '%'
                    : '';
                const ruleDescriptions = {
                  'Recurring Payment Due Soon': 'Notifies when a recurring payment is due within the set days.',
                  'Budget Alert': 'Notifies when budget usage exceeds the set percentage of allocated.',
                  'Large Txn Alert': 'Notifies when transactions exceed the set amount.',
                  'New Budget Alert': 'Notifies when a new budget is created or updated.',
                  'Account out of sync': 'Notifies when one of your financial accounts needs to be linked again.',
                  'New recurring account detected': 'Notifies when a new account is detected.',
                  'Recurring account alert': '',
                  'Bill due date passed alert': 'Notifies when a payment is due.'
                };
                return (
                  <div
                    key={rule.ruleId}
                    className={`px-6 py-5 hover:${darkMode ? 'bg-gray-750' : 'bg-gray-50'} transition-colors duration-150`}
                  >
                    <div className="grid grid-cols-12 gap-8 items-center">
                      {/* Status Toggle */}
                      <div className="col-span-1">
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={editedRule.isEnabled}
                            onChange={(e) => handleRuleChange(rule.ruleId, 'isEnabled', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className={`relative w-11 h-6 rounded-full peer transition-colors duration-200 ${
                            editedRule.isEnabled 
                              ? darkMode ? 'bg-green-600' : 'bg-[#8bc34a]'
                              : darkMode ? 'bg-gray-600' : 'bg-gray-300'
                          } peer-focus:outline-none peer-focus:ring-4 ${
                            editedRule.isEnabled 
                              ? darkMode ? 'peer-focus:ring-green-800' : 'peer-focus:ring-green-300'
                              : darkMode ? 'peer-focus:ring-gray-800' : 'peer-focus:ring-gray-300'
                          }`}>
                            <div className={`absolute top-[2px] left-[2px] bg-white border-gray-300 border rounded-full h-5 w-5 transition-transform duration-200 ${
                              editedRule.isEnabled ? 'translate-x-full' : ''
                            }`} />
                          </div>
                        </label>
                      </div>
                      {/* Rule Details */}
                      <div className="col-span-4">
                        <div className={`font-semibold text-base ${darkMode ? 'text-white' : 'text-gray-900'} mb-1`}>
                          {editedRule.ruleName}
                        </div>
                        <div className={`text-sm ${darkMode ? 'text-gray-400' : 'text-gray-600'} leading-relaxed pr-4`}>
                          {ruleDescriptions[editedRule.ruleName] || 'Custom notification rule'}
                        </div>
                      </div>
                      {/* Threshold */}
                      <div className="col-span-2">
                        {thresholdValue !== null && thresholdValue !== undefined ? (
                          <div className="flex items-center gap-2">
                            <input
                              type="number"
                              value={thresholdValue}
                              onChange={(e) => {
                                const value = parseFloat(e.target.value) || 0;
                                handleRuleChange(rule.ruleId, thresholdField, value);
                              }}
                              className={`w-20 px-2 py-1 rounded border text-sm font-medium transition-colors duration-150 focus:outline-none focus:ring-2 ${
                                darkMode 
                                  ? 'bg-gray-700 border-gray-600 text-white focus:ring-blue-500 focus:border-blue-500' 
                                  : 'bg-gray-50 border-gray-300 text-gray-900 focus:ring-[#8bc34a] focus:border-[#8bc34a]'
                              }`}
                              min="0"
                            />
                            <span className={`text-sm font-medium ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                              {thresholdSuffix}
                            </span>
                          </div>
                        ) : (
                          <div className={`text-sm italic ${darkMode ? 'text-gray-500' : 'text-gray-400'}`}>
                            Not applicable
                          </div>
                        )}
                      </div>
                      {/* Severity */}
                      <div className="col-span-2">
                        <select
                          value={editedRule.severity}
                          onChange={(e) => handleRuleChange(rule.ruleId, 'severity', e.target.value)}
                          className={`w-full px-2 py-2 rounded border text-sm font-medium transition-colors duration-150 focus:outline-none focus:ring-2 ${getSeverityColor(editedRule.severity)} ${
                            darkMode 
                              ? 'focus:ring-blue-500' 
                              : 'focus:ring-[#8bc34a]'
                          }`}
                        >
                          <option value="LOW">🟢 Low</option>
                          <option value="MEDIUM">🟡 Medium</option>
                          <option value="HIGH">🟠 High</option>
                          <option value="CRITICAL">🔴 Critical</option>
                        </select>
                      </div>
                      {/* Mobile Toggle */}
                      <div className="col-span-1 flex justify-center">
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={editedRule.phoneEnabled}
                            onChange={(e) => handleRuleChange(rule.ruleId, 'phoneEnabled', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className={`relative w-8 h-5 rounded-full peer transition-colors duration-200 ${
                            editedRule.phoneEnabled 
                              ? darkMode ? 'bg-blue-600' : 'bg-[#8bc34a]'
                              : darkMode ? 'bg-gray-600' : 'bg-gray-300'
                          }`}>
                            <div className={`absolute top-[1px] left-[1px] bg-white rounded-full h-4 w-4 transition-transform duration-200 ${
                              editedRule.phoneEnabled ? 'translate-x-3' : ''
                            }`} />
                          </div>
                        </label>
                      </div>
                      {/* Email Toggle */}
                      <div className="col-span-1 flex justify-center">
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={editedRule.emailEnabled}
                            onChange={(e) => handleRuleChange(rule.ruleId, 'emailEnabled', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className={`relative w-8 h-5 rounded-full peer transition-colors duration-200 ${
                            editedRule.emailEnabled 
                              ? darkMode ? 'bg-blue-600' : 'bg-[#8bc34a]'
                              : darkMode ? 'bg-gray-600' : 'bg-gray-300'
                          }`}>
                            <div className={`absolute top-[1px] left-[1px] bg-white rounded-full h-4 w-4 transition-transform duration-200 ${
                              editedRule.emailEnabled ? 'translate-x-3' : ''
                            }`} />
                          </div>
                        </label>
                      </div>
                      {/* Action */}
                      <div className="col-span-1">
                        {/* Optional individual save button space */}
                      </div>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className={`px-6 py-12 text-center ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                <UilSetting size="48" className="mx-auto mb-4 opacity-50" />
                <div className="text-lg font-medium mb-2">No notification rules found</div>
                <div className="text-sm">Create your first notification rule to get started</div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotificationSettingsModal;