// import React from 'react'
// import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
// import {
//   faLink,
//   faSync,
//   faPlus,
//   faRedo,
//   faTimes,
//   faClock,
//   faUniversity,
//   faExclamationTriangle, 
//   faArrowUp, 
//   faArrowDown, 
//   faEquals,  
//   faSpinner,
//   faChartLine,
//   faCreditCard,
//   faCoins,
//   faLandmark,
//   faSackDollar,
//   faRobot,
//   faExchangeAlt
// } from "@fortawesome/free-solid-svg-icons";

// const AccountAIChat = ({darkMode,currentTheme,setShowAIChat,showAIChat}) => {

//     const colors = currentTheme.colors;
//   return (
//       <>
//         <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40" onClick={() => setShowAIChat(false)} />
//         <div className={`fixed top-0 right-0 h-full w-96 border-l shadow-2xl z-50 transform transition-transform duration-300 ease-in-out ${
//             showAIChat ? 'translate-x-0' : 'translate-x-full'
//         }`}
//             style={{
//             backgroundColor: colors.cardBg,
//             borderColor: colors.border
//             }}>
//             {/* Chat Header */}
//             <div className={`p-4 border-b flex items-center justify-between`}
//             style={{ 
//                 background: darkMode 
//                 ? `linear-gradient(to right, ${currentTheme.colors.darkCardBg}, ${currentTheme.colors.darkCardBg})`
//                 : `linear-gradient(to right, ${currentTheme.colors.primary}08, ${currentTheme.colors.accent}05)`,
//                 borderColor: colors.border
//             }}>
//             <div className="flex items-center space-x-3">
//                 <div className={`p-2 rounded-xl shadow-lg border`} 
//                 style={{ 
//                     backgroundColor: darkMode ? `${currentTheme.colors.darkCardBg}80` : `${currentTheme.colors.cardBg}80`,
//                     borderColor: colors.border
//                 }}>
//                 <FontAwesomeIcon icon={faRobot} className={`text-lg`} style={{ color: currentTheme.colors.primary }} />
//                 </div>
//                 <div>
//                 <h3 className={`text-lg font-bold`} style={{ color: colors.text }}>AI Assistant</h3>
//                 <p className={`text-xs ${colors.neutral}`}>Ask me about your finances</p>
//                 </div>
//             </div>
//             <button
//                 onClick={() => setShowAIChat(false)}
//                 className={`p-2 rounded-xl transition-all duration-200 ${
//                 darkMode 
//                     ? 'text-slate-400 hover:text-slate-300' 
//                     : 'text-slate-500 hover:text-slate-700'
//                 }`}
//                 onMouseEnter={(e) => e.target.style.backgroundColor = `${currentTheme.colors.primary}20`}
//                 onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
//             >
//                 <FontAwesomeIcon icon={faTimes} className="text-lg" />
//             </button>
//             </div>

//             {/* Chat Messages Area */}
//             <div className="flex-1 p-4 h-full overflow-y-auto" style={{ paddingBottom: '80px' }}>
//             <div className="space-y-4">
//                 {/* Welcome Message */}
//                 <div className="flex items-start space-x-3">
//                 <div className={`p-2 rounded-full`} style={{ backgroundColor: `${currentTheme.colors.primary}20` }}>
//                     <FontAwesomeIcon icon={faRobot} className={`text-sm`} style={{ color: currentTheme.colors.primary }} />
//                 </div>
//                 <div className={`flex-1 p-3 rounded-xl max-w-xs`}
//                     style={{ 
//                     backgroundColor: darkMode ? `${currentTheme.colors.primary}20` : `${currentTheme.colors.primary}10`,
//                     color: colors.text
//                     }}>
//                     <p className="text-sm">
//                     Hi! I'm your AI financial assistant. I can help you analyze your accounts, understand your spending patterns, and answer questions about your finances.
//                     </p>
//                 </div>
//                 </div>

//                 {/* Sample Suggestions */}
//                 <div className="flex flex-wrap gap-2 mt-4">
//                 {[
//                     "Show my spending summary",
//                     "What's my net worth trend?",
//                     "Help me budget better",
//                     "Analyze my investments"
//                 ].map((suggestion, index) => (
//                     <button
//                     key={index}
//                     className={`px-3 py-2 rounded-full text-xs font-medium transition-all duration-200 border`}
//                     style={{
//                         backgroundColor: darkMode ? colors.cardBg : `${currentTheme.colors.primary}05`,
//                         borderColor: colors.border,
//                         color: colors.text
//                     }}
//                     onMouseEnter={(e) => {
//                         e.target.style.backgroundColor = `${currentTheme.colors.primary}20`;
//                         e.target.style.borderColor = currentTheme.colors.primary;
//                     }}
//                     onMouseLeave={(e) => {
//                         e.target.style.backgroundColor = darkMode ? colors.cardBg : `${currentTheme.colors.primary}05`;
//                         e.target.style.borderColor = colors.border;
//                     }}
//                     >
//                     {suggestion}
//                     </button>
//                 ))}
//                 </div>
//             </div>
//             </div>

//             {/* Chat Input */}
//             <div className={`absolute bottom-0 left-0 right-0 p-4 border-t`}
//             style={{ 
//                 backgroundColor: colors.cardBg,
//                 borderColor: colors.border
//             }}>
//             <div className="flex items-center space-x-2">
//                 <input
//                 type="text"
//                 placeholder="Ask me about your finances..."
//                 className={`flex-1 border rounded-xl px-4 py-3 focus:outline-none focus:ring-2 transition-all duration-200`}
//                 style={{ 
//                     backgroundColor: darkMode ? colors.bg : colors.cardBg,
//                     color: colors.text,
//                     borderColor: colors.border,
//                     focusRingColor: currentTheme.colors.primary
//                 }}
//                 onFocus={(e) => e.target.style.borderColor = currentTheme.colors.primary}
//                 onBlur={(e) => e.target.style.borderColor = colors.border}
//                 />
//                 <button
//                 className={`p-3 rounded-xl text-white transition-all duration-200 font-semibold shadow-lg ${
//                     currentTheme.layout?.animations === false ? '' : 'hover:scale-105'
//                 }`}
//                 style={{ 
//                     backgroundColor: currentTheme.colors.primary,
//                     borderRadius: currentTheme.layout?.borderRadius === 'square' ? '6px' : 
//                                 currentTheme.layout?.borderRadius === 'pill' ? '50%' : '12px',
//                     boxShadow: currentTheme.layout?.shadows === false ? 'none' : ''
//                 }}
//                 >
//                 <FontAwesomeIcon icon={faArrowUp} />
//                 </button>
//             </div>
//             </div>
//         </div>
//         </>
//   )
// }

// export default AccountAIChat
import { useEffect, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faTimes,
  faArrowUp,
  faRobot,
  faThumbsUp,
  faThumbsDown,
  faCopy
} from "@fortawesome/free-solid-svg-icons";
import { getCurrentUserId } from '@pp-web/utils/AuthUtil';
import { logEvent } from '@pp-web/utils/EventLogger';
import { setChartType, setTimePeriod } from '@pp-logic/redux/accountChartSlice';
import { fetchHistoryRequest, queryRequest } from '@pp-logic/redux/chatbotSlice';

const AccountAIChat = ({ darkMode, currentTheme, setShowAIChat, setSelectedChartType, setSelectedTimePeriod }) => {
  const colors = currentTheme.colors;
  const userId = getCurrentUserId();
  const [chatHistory, setChatHistory] = useState([]);
  const [newQuery, setNewQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [copiedIndex, setCopiedIndex] = useState(null);
  const chatContainerRef = useRef(null);
  const chatEndRef = useRef(null);
  const [isScrolledToBottom, setIsScrolledToBottom] = useState(true);
  const cache = useSelector(state => state.cache);
  const chatbotState = useSelector(state => state.chatbot);

  const dispatch = useDispatch();

  useEffect(() => {
    // Use cached data if available, otherwise fetch
    if (cache?.chatbotHistoryLoaded && cache?.chatbotHistory?.length >= 0 &&
        cache?.chatbotHistoryParams?.userId === userId) {
      const flatHistory = cache.chatbotHistory.flatMap(log => {
        const parsed = tryParseJSON(log.response);
        return [
          { type: 'user', text: log.userQuery },
          { type: 'ai', text: parsed?.summary || log.response, chatId: log.id }
        ];
      });
      setChatHistory(flatHistory);
    } else {
      dispatch(fetchHistoryRequest({ userId }));
    }
  }, [dispatch, userId, cache?.chatbotHistoryLoaded]);

  // Listen for Redux state changes to update local state
  useEffect(() => {
    if (cache?.chatbotHistoryLoaded && cache?.chatbotHistory) {
      const flatHistory = cache.chatbotHistory.flatMap(log => {
        const parsed = tryParseJSON(log.response);
        return [
          { type: 'user', text: log.userQuery },
          { type: 'ai', text: parsed?.summary || log.response, chatId: log.id }
        ];
      });
      setChatHistory(flatHistory);
      setIsLoading(false);
    }
  }, [cache?.chatbotHistory, cache?.chatbotHistoryLoaded]);

  // Listen for chatbot query completion to stop loading
  useEffect(() => {
    if (!chatbotState.querying && isLoading) {
      setIsLoading(false);
    }
  }, [chatbotState.querying, isLoading]);

  const tryParseJSON = (text) => {
    try {
      const cleanText = text.replace(/^```json|```$/g, '').trim();
      const evaluatedText = cleanText.replace(
        /"value"\s*:\s*([0-9\.\s\+\-\*\/]+)/g,
        (_, expr) => `"value": ${eval(expr)}`
      );
      return JSON.parse(evaluatedText);
    } catch {
      return null;
    }
  };

  const sendQuery = () => {
    logEvent('AccountAIChat', 'sendQuery', { query: newQuery });
    if (!newQuery.trim()) return;

    const currentQuery = newQuery.trim();
    setNewQuery('');
    setIsLoading(true);

    // Use Redux action to handle the query - this will automatically update cache
    dispatch(queryRequest({ userId, userQuery: currentQuery }));

    // Intent recognition for dropdown updates
    const lowerQuery = currentQuery.toLowerCase();
    let chartType = null;
    let timePeriod = null;

    // Define patterns for account types (prioritized)
    const accountTypePatterns = [
      { pattern: /\b(cash|checking|checkings|deposit|depository|saving|savings)\b/, value: 'cash' },
      { pattern: /\b(credit card|credit cards|credit|credits)\b/, value: 'creditCard' },
      { pattern: /\b(loan|loans|mortgage|debt|debts)\b/, value: 'loan' },
      { pattern: /\b(investment|investments|stocks|bonds|portfolio)\b/, value: 'investment' },
      { pattern: /\b(liability|liabilities|debt|debts)\b/, value: 'liability' },
      { pattern: /\b(net worth|networth|total wealth)\b/, value: 'networth' },
    ];

    // Define patterns for time periods (prioritized)
    const timePeriodPatterns = [
      { pattern: /\b(year to date|ytd)\b/, value: 'ytd' },
      { pattern: /\b(past year|1 year|yearly|one year|annual)\b/, value: 'yearly' },
      { pattern: /\b(half year|6 months|six months)\b/, value: 'half-year' },
      { pattern: /\b(3 months|quarter|quarterly|three months)\b/, value: 'three-month' },
      { pattern: /\b(1 month|month|monthly|one month)\b/, value: 'one-month' },
    ];

    // Match account type
    for (const { pattern, value } of accountTypePatterns) {
      if (pattern.test(lowerQuery)) {
        chartType = value;
        break;
      }
    }

    // Match time period
    for (const { pattern, value } of timePeriodPatterns) {
      if (pattern.test(lowerQuery)) {
        timePeriod = value;
        break;
      }
    }

    if (chartType) dispatch(setChartType(chartType));
    if (timePeriod) dispatch(setTimePeriod(timePeriod));
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') sendQuery();
  };

  const scrollToBottom = () => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [chatHistory, isLoading]);

  const handleScroll = () => {
    const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
    setIsScrolledToBottom(scrollTop + clientHeight >= scrollHeight - 20);
  };

  const copyToClipboard = (text, index) => {
    navigator.clipboard.writeText(text);
    setCopiedIndex(index);
    setTimeout(() => setCopiedIndex(null), 2000);
  };

  return (
    <>
      <div className={`fixed top-0 right-0 h-screen w-96 border-l shadow-2xl flex flex-col flex-shrink-0 transition-all duration-300 ease-in-out z-50`} style={{ backgroundColor: colors.cardBg, borderColor: colors.border }}>
        <style>
          {`
            @keyframes bounce-dot {
              0%, 80%, 100% { transform: scale(0); }
              40% { transform: scale(1); }
            }
            .dot {
              display: inline-block;
              width: 8px;
              height: 8px;
              margin: 0 2px;
              background-color: ${currentTheme.colors.primary};
              border-radius: 50%;
              animation: bounce-dot 1.4s infinite ease-in-out both;
            }
            .dot:nth-child(1) { animation-delay: -0.32s; }
            .dot:nth-child(2) { animation-delay: -0.16s; }
            .dot:nth-child(3) { animation-delay: 0s; }
          `}
        </style>
        
        {/* Header */}
        <div className={`p-4 border-b flex items-center justify-between`} style={{ background: darkMode ? `linear-gradient(to right, ${currentTheme.colors.darkCardBg}, ${currentTheme.colors.darkCardBg})` : `linear-gradient(to right, ${currentTheme.colors.primary}08, ${currentTheme.colors.accent}05)`, borderColor: colors.border }}>
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-xl shadow-lg border`} style={{ backgroundColor: darkMode ? `${currentTheme.colors.darkCardBg}80` : `${currentTheme.colors.cardBg}80`, borderColor: colors.border }}>
              <FontAwesomeIcon icon={faRobot} className={`text-lg`} style={{ color: currentTheme.colors.primary }} />
            </div>
            <div>
              <h3 className={`text-lg font-bold`} style={{ color: colors.text }}>AI Assistant</h3>
              <p className={`text-xs ${colors.neutral}`}>Ask me about your finances</p>
            </div>
          </div>
          <button onClick={() => setShowAIChat(false)} className={`p-2 rounded-xl transition-all duration-200 ${darkMode ? 'text-slate-400 hover:text-slate-300' : 'text-slate-500 hover:text-slate-700'}`} onMouseEnter={(e) => e.target.style.backgroundColor = `${currentTheme.colors.primary}20`} onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}>
            <FontAwesomeIcon icon={faTimes} className="text-lg" />
          </button>
        </div>

        {/* Chat Messages Area */}
        <div 
          className="flex-1 p-4 overflow-y-auto space-y-4 scrollbar-hide" 
          style={{ 
            height: 'calc(100vh - 160px)', // Subtract header height and input area height
            WebkitOverflowScrolling: 'touch', 
            'scrollbar-width': 'none', 
            '::-webkit-scrollbar': { display: 'none' } 
          }} 
          ref={chatContainerRef} 
          onScroll={handleScroll}
        >
          {/* Welcome Message */}
          <div className="flex items-start space-x-3">
            <div className={`p-2 rounded-full`} style={{ backgroundColor: `${currentTheme.colors.primary}20` }}>
              <FontAwesomeIcon icon={faRobot} className={`text-sm`} style={{ color: currentTheme.colors.primary }} />
            </div>
            <div className={`flex-1 p-3 rounded-xl max-w-xs`} style={{ backgroundColor: darkMode ? `${currentTheme.colors.primary}20` : `${currentTheme.colors.primary}10`, color: colors.text }}>
              <p className="text-sm">Hi! I'm your AI financial assistant. I can help you analyze your accounts, understand your spending patterns, and answer questions about your finances.</p>
            </div>
          </div>

          {/* Chat History */}
          {chatHistory.map((msg, index) => (
            <div key={index} className={`flex flex-col ${msg.type === 'user' ? 'items-start' : 'items-end'}`}>
              <div className={`inline-block max-w-full px-4 py-2 rounded-lg ${msg.type === 'user' ? `bg-blue-100 text-blue-900` : `bg-green-100 text-green-900`} ml-2`}>
                {msg.text}
              </div>
              {msg.type === 'ai' && (
                <div className={`flex space-x-2 mt-1 mr-2 items-center relative`}>
                  <FontAwesomeIcon icon={faThumbsUp} className={`w-4 h-4 cursor-pointer ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} />
                  <FontAwesomeIcon icon={faThumbsDown} className={`w-4 h-4 cursor-pointer ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} />
                  <FontAwesomeIcon icon={faCopy} className={`w-4 h-4 cursor-pointer ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} onClick={() => copyToClipboard(msg.text, index)} />
                  {copiedIndex === index && (
                    <span className={`absolute -top-6 left-1/2 -translate-x-1/2 bg-gray-800 text-white text-xs px-2 py-0.5 rounded shadow-md`}>Copied!</span>
                  )}
                </div>
              )}
            </div>
          ))}
          {/* Loading Dots */}
          {isLoading && (
            <div className="flex flex-col items-end">
              <div className="inline-block max-w-full px-4 py-2 rounded-lg bg-green-100 text-green-900 ml-2">
                <span className="dot"></span>
                <span className="dot"></span>
                <span className="dot"></span>
              </div>
            </div>
          )}
          <div ref={chatEndRef} />
        </div>

        {/* Scroll to Bottom Button */}
        {!isScrolledToBottom && (
          <div className="absolute bottom-20 left-1/2 transform -translate-x-1/2 z-10">
            <button onClick={scrollToBottom} className={`bg-gray-800 text-white text-sm px-3 py-1 rounded-md shadow-lg transition hover:bg-gray-700`}>↓</button>
          </div>
        )}

        {/* Input Area */}
        <div className={`p-4 border-t`} style={{ backgroundColor: colors.cardBg, borderColor: colors.border }}>
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={newQuery}
              onChange={(e) => setNewQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me about your finances..."
              className={`flex-1 border rounded-xl px-4 py-3 focus:outline-none focus:ring-2 transition-all duration-200`} 
              style={{ backgroundColor: darkMode ? colors.bg : colors.cardBg, color: colors.text, borderColor: colors.border }} 
              onFocus={(e) => e.target.style.borderColor = currentTheme.colors.primary} 
              onBlur={(e) => e.target.style.borderColor = colors.border}
            />
            <button
              onClick={sendQuery}
              disabled={isLoading}
              className={`p-3 rounded-xl text-white transition-all duration-200 font-semibold shadow-lg ${currentTheme.layout?.animations === false ? '' : 'hover:scale-105'}`} 
              style={{ backgroundColor: currentTheme.colors.primary, borderRadius: currentTheme.layout?.borderRadius === 'square' ? '6px' : currentTheme.layout?.borderRadius === 'pill' ? '50%' : '12px', boxShadow: currentTheme.layout?.shadows === false ? 'none' : '' }}
            >
              <FontAwesomeIcon icon={faArrowUp} />
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default AccountAIChat;