import React, { useEffect, useState, useMemo, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowUp, faArrowDown } from '@fortawesome/free-solid-svg-icons';
import {
  fetchAggregatedBalancesStart,
  selectAggregatedBalances,
  selectFetchLoading,
  selectFetchError,
} from '../../../../../../logic/redux/accountBalanceSlice';
import { getCurrentUserId } from '../../../../utils/AuthUtil';

const AccountMiniChart = ({ 
  accountId, 
  duration = 1, 
  interval = 8, 
  darkMode = false, 
  currentTheme = { colors: { primary: '#4F46E5' } } 
}) => {
  const dispatch = useDispatch();
  const userId = getCurrentUserId();
  const hasInitialized = useRef(false);
  
  // Redux selectors
  const data = useSelector(selectAggregatedBalances(userId, duration, interval));
  const loading = useSelector(selectFetchLoading);
  const error = useSelector(selectFetchError);
  
  // Local state for tooltip
  const [tooltipData, setTooltipData] = useState(null);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });

  // Memoize the cache key to prevent unnecessary re-renders
  const cacheKey = useMemo(() => `${userId}_${duration}_${interval}`, [userId, duration, interval]);

  // Single API call on mount - only if data doesn't exist and we haven't initialized
  useEffect(() => {
    if (userId && !hasInitialized.current && (!data || data.length === 0)) {
      console.log("AccountMiniChart: Initializing API call for:", { accountId, userId, duration, interval, cacheKey });
      
      dispatch(fetchAggregatedBalancesStart({ 
        userId: parseInt(userId), 
        duration: parseInt(duration), 
        interval: parseInt(interval) 
      }));
      
      hasInitialized.current = true;
    }
  }, [dispatch, userId, duration, interval, cacheKey, data]);

  // Filter and process data for this specific account
  const chartData = useMemo(() => {
    console.log("AccountMiniChart: Processing data for accountId:", accountId, "Raw data:", data);
    
    if (loading) {
      return [];
    }

    // Filter data for specific accountId if provided, otherwise use all data
    let filteredData = data;
    if (accountId && Array.isArray(data)) {
      filteredData = data.filter(item => item.accountId === accountId);
      console.log("AccountMiniChart: Filtered data for accountId:", accountId, "Result:", filteredData);
    }

    if (!filteredData || !Array.isArray(filteredData) || filteredData.length === 0) {
      console.log("AccountMiniChart: No data found, creating dummy data");
      // Create dummy data points for empty state
      const today = new Date();
      const dummyData = [];
      
      for (let i = 2; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(today.getDate() - i);
        
        dummyData.push({
          date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          balance: 0
        });
      }
      return dummyData;
    }

    // Process real data
    let processedData = filteredData.map(item => ({
      date: new Date(item.groupEndDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
      rawDate: new Date(item.groupEndDate),
      balance: parseFloat(item.aggregatedBalance || 0)
    }));

    // Handle cases with insufficient data points
    if (processedData.length === 1) {
      const realPoint = processedData[0];
      const realDate = realPoint.rawDate;

      const prevDate1 = new Date(realDate);
      prevDate1.setDate(realDate.getDate() - 2);

      const prevDate2 = new Date(realDate);
      prevDate2.setDate(realDate.getDate() - 1);

      processedData = [
        {
          date: prevDate1.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          balance: 0
        },
        {
          date: prevDate2.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          balance: 0
        },
        {
          date: realDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          balance: realPoint.balance
        }
      ];
    } else if (processedData.length === 2) {
      const firstPoint = processedData[0];
      const firstDate = firstPoint.rawDate;

      const prevDate = new Date(firstDate);
      prevDate.setDate(firstDate.getDate() - 1);

      processedData = [
        {
          date: prevDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
          balance: 0
        },
        ...processedData.map(({ rawDate, ...rest }) => rest)
      ];
    } else {
      // For 3 or more points, just remove rawDate
      processedData = processedData.map(({ rawDate, ...rest }) => rest);
    }

    console.log("AccountMiniChart: Final processed data:", processedData);
    return processedData;
  }, [data, accountId, loading]);

  // Calculate chart metrics
  const chartMetrics = useMemo(() => {
    // Safety check for chartData
    if (!chartData || chartData.length === 0) {
      console.log('AccountMiniChart: No chart data available, returning default metrics');
      return {
        values: [0, 0, 0],
        maxValue: 0,
        minValue: 0,
        range: 1,
        firstNonZeroValue: 0,
        lastValue: 0,
        trendUp: false,
        percentageChange: 0
      };
    }

    const values = chartData.map(d => typeof d.balance === 'number' ? d.balance : 0);
    const maxValue = Math.max(...values);
    const minValue = Math.min(...values);
    const range = maxValue - minValue || 1;

    const firstNonZeroValue = values.find(v => v !== 0) ?? 0;
    const lastValue = values[values.length - 1] || 0;
    const trendUp = lastValue > firstNonZeroValue;

    let percentageChange = 0;
    if (firstNonZeroValue !== 0) {
      percentageChange = ((lastValue - firstNonZeroValue) / firstNonZeroValue) * 100;
    }

    console.log('AccountMiniChart: Chart metrics calculated:', {
      values,
      maxValue,
      minValue,
      range,
      firstNonZeroValue,
      lastValue,
      trendUp,
      percentageChange
    });

    return {
      values,
      maxValue,
      minValue,
      range,
      firstNonZeroValue,
      lastValue,
      trendUp,
      percentageChange
    };
  }, [chartData]);

  // Create SVG paths
  const svgPaths = useMemo(() => {
    // Safety check for chartMetrics
    if (!chartMetrics || !chartMetrics.values || chartMetrics.values.length === 0) {
      console.log('AccountMiniChart: No chart metrics available, returning empty paths');
      return { 
        areaPath: '', 
        linePath: '', 
        points: [] 
      };
    }

    const createSmoothPath = (points, closed = false) => {
      if (!points || points.length < 2) return '';
      
      const smoothing = 0.2;
      
      const getControlPoints = (p0, p1, p2) => {
        if (!p0 || !p1 || !p2) return { cp1x: 0, cp1y: 0, cp2x: 0, cp2y: 0 };
        
        const dx = p2.x - p0.x;
        const dy = p2.y - p0.y;
        const d = Math.sqrt(dx * dx + dy * dy);
        const factor = smoothing * d;
        
        if (d === 0) return { cp1x: p1.x, cp1y: p1.y, cp2x: p1.x, cp2y: p1.y };
        
        return {
          cp1x: p1.x - factor * (p2.x - p0.x) / d,
          cp1y: p1.y - factor * (p2.y - p0.y) / d,
          cp2x: p1.x + factor * (p2.x - p0.x) / d,
          cp2y: p1.y + factor * (p2.y - p0.y) / d
        };
      };

      let pathData = `M ${points[0].x} ${points[0].y}`;
      
      if (points.length === 2) {
        pathData += ` L ${points[1].x} ${points[1].y}`;
      } else {
        for (let i = 1; i < points.length; i++) {
          const p0 = points[i - 1] || points[i];
          const p1 = points[i];
          const p2 = points[i + 1] || points[i];
          
          if (i === 1) {
            const cp = getControlPoints(p0, p1, p2);
            pathData += ` Q ${cp.cp2x} ${cp.cp2y} ${p1.x} ${p1.y}`;
          } else if (i === points.length - 1) {
            const cp = getControlPoints(points[i - 2], p0, p1);
            pathData += ` Q ${cp.cp2x} ${cp.cp2y} ${p1.x} ${p1.y}`;
          } else {
            const cp = getControlPoints(p0, p1, p2);
            pathData += ` Q ${cp.cp1x} ${cp.cp1y} ${p1.x} ${p1.y}`;
          }
        }
      }
      
      if (closed) {
        pathData += ` Z`;
      }
      
      return pathData;
    };

    const width = 100;
    const height = 100;
    const padding = 5;
    
    // Create points with safety checks
    const points = chartMetrics.values.map((value, index) => {
      const safeValue = typeof value === 'number' ? value : 0;
      const safeRange = chartMetrics.range > 0 ? chartMetrics.range : 1;
      const safeMinValue = typeof chartMetrics.minValue === 'number' ? chartMetrics.minValue : 0;
      
      const x = padding + (index / Math.max(chartMetrics.values.length - 1, 1)) * (width - 2 * padding);
      const y = height - padding - ((safeValue - safeMinValue) / safeRange) * (height - 2 * padding);
      
      return { x: isNaN(x) ? padding : x, y: isNaN(y) ? height - padding : y };
    });

    console.log('AccountMiniChart: Generated points:', points);

    if (points.length === 0) {
      return { 
        areaPath: '', 
        linePath: '', 
        points: [] 
      };
    }

    const smoothLine = createSmoothPath(points);
    
    let areaPath = smoothLine;
    if (points.length > 0) {
      areaPath += ` L ${points[points.length - 1].x} ${height - padding}`;
      areaPath += ` L ${points[0].x} ${height - padding}`;
      areaPath += ` Z`;
    }
    
    return { areaPath, linePath: smoothLine, points };
  }, [chartMetrics]);

  const handlePointHover = (value, date, event) => {
    const rect = event.currentTarget.getBoundingClientRect();
    setTooltipPosition({
      x: rect.left + rect.width / 2,
      y: rect.top - 10
    });
    setTooltipData({ value, date });
  };

  const handlePointLeave = () => {
    setTooltipData(null);
  };

  // Loading state
  if (loading) {
    return (
      <div className="h-20 w-full relative overflow-hidden rounded-lg flex items-center justify-center">
        <div className="text-sm text-gray-500 animate-pulse">Loading chart...</div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="h-20 w-full relative overflow-hidden rounded-lg flex items-center justify-center">
        <div className="text-xs text-red-500 text-center px-2" title={error}>
          Chart error
        </div>
      </div>
    );
  }

  const chartColor = chartMetrics.trendUp ? currentTheme.colors.primary : '#ef4444';
  const fillOpacity = darkMode ? '0.2' : '0.15';
  const strokeOpacity = darkMode ? '0.8' : '1';

  return (
    <div className="h-20 w-full relative overflow-hidden rounded-lg">
      {/* Background gradient */}
      <div className={`absolute inset-0`} style={{
        background: chartMetrics.trendUp 
          ? (darkMode
            ? `linear-gradient(to right, ${currentTheme.colors.primary}20, ${currentTheme.colors.primary}05)`
            : `linear-gradient(to right, ${currentTheme.colors.primary}15, ${currentTheme.colors.primary}05)`)
          : (darkMode
            ? 'linear-gradient(to right, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.05))'
            : 'linear-gradient(to right, rgba(239, 68, 68, 0.15), rgba(239, 68, 68, 0.05))')
      }}></div>
             
      {/* Area Chart SVG */}
      <div className="absolute inset-0 p-1" style={{ paddingBottom: '20px' }}>
        <svg
          width="100%"
          height="100%"
          viewBox="0 0 100 100"
          preserveAspectRatio="none"
          className="overflow-visible"
        >
          {/* Area fill */}
          <path
            d={svgPaths.areaPath}
            fill={chartColor}
            fillOpacity={fillOpacity}
            className="transition-all duration-300"
          />
          
          {/* Line stroke */}
          <path
            d={svgPaths.linePath}
            fill="none"
            stroke={chartColor}
            strokeWidth="1.5"
            strokeOpacity={strokeOpacity}
            className="transition-all duration-300"
          />
          
          {/* Data points for hover interaction */}
          {svgPaths.points && svgPaths.points.length > 0 && svgPaths.points.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r="4"
              fill={chartColor}
              className="opacity-0 hover:opacity-100 transition-opacity duration-200 cursor-pointer"
              onMouseEnter={(e) => handlePointHover(chartMetrics.values[index], chartData[index]?.date, e)}
              onMouseLeave={handlePointLeave}
            />
          ))}
        </svg>
      </div>

      {/* Date labels at bottom */}
      <div className="absolute bottom-0 left-0 right-0 px-2 pb-1">
        <div className="flex justify-between items-center h-4">
          {chartData && chartData.length > 0 && chartData.map((item, index) => (
            <div 
              key={index}
              className={`text-xs font-medium ${
                darkMode ? 'text-slate-400' : 'text-slate-500'
              } text-center`}
              style={{ 
                fontSize: '10px',
                flex: '1',
                minWidth: '0'
              }}
            >
              {item?.date || ''}
            </div>
          ))}
        </div>
      </div>
             
      {/* Trend indicator */}
      <div className="absolute top-2 right-2">
        <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs font-medium`}
          style={{
            backgroundColor: chartMetrics.trendUp 
              ? (darkMode ? `${currentTheme.colors.primary}20` : `${currentTheme.colors.primary}20`)
              : (darkMode ? 'rgba(239, 68, 68, 0.2)' : 'rgba(239, 68, 68, 0.2)'),
            color: chartMetrics.trendUp ? currentTheme.colors.primary : '#ef4444'
          }}>
          <FontAwesomeIcon 
            icon={chartMetrics.trendUp ? faArrowUp : faArrowDown}
            className="text-xs"
          />
          <span>{`${chartMetrics.trendUp ? '+' : '-'}${Math.abs(chartMetrics.percentageChange).toFixed(1)}%`}</span>
        </div>
      </div>

      {/* Custom Tooltip */}
      {tooltipData && (
        <div 
          className={`fixed p-2 border shadow-sm rounded-md z-50 pointer-events-none ${
            darkMode 
              ? 'bg-slate-800 border-slate-600 text-white' 
              : 'bg-white border-gray-200'
          }`}
          style={{
            left: tooltipPosition.x,
            top: tooltipPosition.y,
            transform: 'translateX(-50%) translateY(-100%)'
          }}
        >
          <p className="text-xs font-medium">{tooltipData.date}</p>
          <p className={`text-xs font-semibold ${chartMetrics.trendUp ? 'text-blue-600' : 'text-red-600'}`}>
            {`$${tooltipData.value.toLocaleString(undefined, { 
              minimumFractionDigits: 2, 
              maximumFractionDigits: 2 
            })}`}
          </p>
        </div>
      )}
    </div>
  );
};

export default React.memo(AccountMiniChart);