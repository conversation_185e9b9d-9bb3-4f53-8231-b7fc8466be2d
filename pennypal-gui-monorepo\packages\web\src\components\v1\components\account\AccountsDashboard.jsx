import React, { useState, useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";
import { PlaidLink } from "react-plaid-link";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faLink,
  faSync,
  faPlus,
  faRedo,
  faTimes,
  faClock,
  faUniversity,
  faExclamationTriangle, 
  faArrowUp, 
  faArrowDown, 
  faEquals,  
  faSpinner,
  faChartLine,
  faCreditCard,
  faCoins,
  faLandmark,
  faSackDollar,
  faRobot,
  faExchangeAlt
} from "@fortawesome/free-solid-svg-icons";
import AccountChart from "./AccountChart";
import BankIcon from "./BankIcon";
import AccountMiniChart from "./AccountMiniChart";
import {
  fetchLinkToken,
  fetchAccountDetails,
  exchangePublicToken,
  refreshAllAccounts,
  syncAccount,
  connectMx,
  connectStripe,
  connectFinicity,
  resetError
} from "../../../../../../logic/redux/accountsDashboardSlice";
import { fetchAccountTypeDeltaData, selectSelectedTimePeriod } from '../../../../../../logic/redux/deltaSlice';
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";
import { getCurrentUserId } from "../../../../utils/AuthUtil";
import { logEvent } from '../../../../utils/EventLogger';
import { fetchDeltaData } from '../../../../../../logic/redux/deltaSlice';
import { 
  selectDeltaData, 
  selectDeltaLoading, 
  selectDeltaError,
  selectDeltaAccounts,
  selectDeltaSummary ,
  selectDeltaAccountTypes
  
} from '../../../../../../logic/redux/deltaSlice';
import {  setChartType,
  setTimePeriod,
  fetchAccountData,
  selectChartType,
  selectTimePeriod, } from '../../../../../../logic/redux/accountChartSlice';
import { formatLastSyncTime, 
         formatCategoryName,
         getCategoryIcon,
         maskAccountNumber,
         ALL_CATEGORIES
        } from './AccountUtil';

import DesignPaletteDropdown from "./DesignPlatteDropdown";
import PortfolioChartControlView from "./PortfolioChartControlView";
import StatCardGroupView from "./StatCardGroupView";
import AccountConnectionProviderModal from "./AccountConnectionProviderModal";
import AccountAIChat from "./AccountAIChat";
import PaymentLoader from "../../../load/PaymentLoader"
import {formatAmountWithCommas}from '../../../../../../logic/redux/accountChartSlice';
import { selectAccountTypeDataByPeriod } from '../../../../../../logic/redux/deltaSlice';
import ReauthButton from "./ReauthButton"; // You'll create this component
import { useReauth } from "./useReauth"; 
// Enhanced drag handle with better visual design
const DragHandleIcon = ({ darkMode }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={`cursor-grab transition-all duration-200 hover:scale-110 ${
      darkMode ? 'fill-gray-400 hover:fill-gray-300' : 'fill-gray-500 hover:fill-gray-700'
    }`}
  >
    <circle cx="6" cy="5" r="2" />
    <circle cx="6" cy="10" r="2" />
    <circle cx="6" cy="15" r="2" />
    <circle cx="14" cy="5" r="2" />
    <circle cx="14" cy="10" r="2" />
    <circle cx="14" cy="15" r="2" />
  </svg>
);

const AccountsDashboard = ({ darkMode, onAIChatOpen, onAIChatClose }) => {
  const userId = getCurrentUserId();
  const dispatch = useDispatch();
  
  // FIXED: Move all useSelector hooks to the top, before any conditional logic
  const selectedChartType = useSelector(selectChartType);
  const selectedTimePeriod = useSelector(selectTimePeriod);
  const deltaAccountTypes = useSelector(selectDeltaAccountTypes);
  const selecteTimePeriod = useSelector(selectSelectedTimePeriod);
  
  // Get all account state at the top
  const { 
    linkToken, 
    accounts, 
    isLinked, 
    isLoading,  
    hasLoadedOnce,
    balanceHistory,
    error,
    syncingAccounts = [],
    refreshAllStatus,
    syncErrorFlags,
    syncErrors // <-- ADD THIS LINE
  } = useSelector((state) => state.accounts);
    const { 
    reauthStatus, 
    reauthErrors, 
    reauthLinkTokens, 
    startReauth, 
    completeReauth,
    clearReauth 
  } = useReauth();
  // Get delta data
  const deltaData = useSelector(selectDeltaData);
  const deltaLoading = useSelector(selectDeltaLoading);
  const deltaError = useSelector(selectDeltaError);
  const deltaAccounts = useSelector(selectDeltaAccounts); // This is always from fetchDeltaData
  const deltaSummary = useSelector(selectDeltaSummary);

// const syncErrorFlags = useSelector(state => state.accounts.syncErrorFlags || {});
  
   useEffect(() => {
        // Fetch account type delta data when component mounts or time period changes
        const timePeriod = selecteTimePeriod || 'one-month'; // Default to one month
        
        dispatch(fetchAccountTypeDeltaData({ timePeriod }));
    }, [dispatch, selecteTimePeriod]);

  // NEW: Theme state management with granular design options
  const [currentTheme, setCurrentTheme] = useState({
    theme: 'default',
    font: { name: 'Inter', value: "'Inter', sans-serif" },
    colors: {
      primary: "#8BC34A",
      secondary: "#7CB342",
      accent: "#9CCC65",
      success: "#4CAF50",
      background: "#ffffff",
      // background: "#f8fffe",
      darkBackground: "#0f172a",
      cardBg: "#ffffff",
      darkCardBg: "#1e293b",
      text: "#334155",
      darkText: "#f1f5f9",
      border: "#e2e8f0",
      darkBorder: "#475569"
    },
    layout: {
      maxWidth: 'none', // 'none', '1200px', '1400px', '1600px'
      centered: false,
      spacing: 'normal', // 'compact', 'normal', 'spacious'
      gridDensity: 'normal', // 'compact', 'normal', 'spacious'
      cardStyle: 'elevated', // 'flat', 'elevated', 'outlined'
      borderRadius: 'rounded', // 'square', 'rounded', 'pill'
      buttonStyle: 'rounded', // 'square', 'rounded', 'pill'
      animations: true,
      shadows: true
    }
  });

  // NEW: Theme change handler (enhanced to handle all layout changes)
  const handleThemeChange = (newTheme) => {
    setCurrentTheme(newTheme);
    // Force a re-render by updating the key state
    setForceUpdate(prev => prev + 1);
  };

  // Add a force update state to ensure components re-render with new theme
  const [forceUpdate, setForceUpdate] = useState(0);
  
  // Get delta data

  const handleChartTypeChange = (e) => {
    dispatch(setChartType(e.target.value));
  };

  const handleTimePeriodChange = (e) => {
    dispatch(setTimePeriod(e.target.value));
  };
  useEffect(() => {
    dispatch(fetchAccountData()); // Optionally add payload if needed
  }, [selectedChartType, selectedTimePeriod, dispatch]);

  // Map chart time periods to delta time periods
  const mapTimePeriodForDelta = (chartTimePeriod) => {
    const mapping = {
      'one-month': 'one-month',
      'three-month': 'three-month',
      'half-year': 'half-year',
      'yearly': 'yearly',
      'ytd': 'ytd',
      'quarterly-aggregate': 'quarterly-rolling',
      'quarterly-rolling': 'quarterly-rolling'
    };
    
    return mapping[chartTimePeriod] || 'three-month';
  };

  // Fetch delta data when time period changes
useEffect(() => {
  dispatch(fetchDeltaData({ timePeriod: 'one-month' }));
}, [dispatch]);
// Updated processAccountTypeDeltaData function to handle the actual API response structure
const processAccountTypeDeltaData = (responseData, timePeriod) => {
  if (!responseData) {
    return {
      comparisonType: timePeriod,
      accounts: [],
      accountTypes: [],
      metadata: null,
      summary: {
        totalCurrentBalance: 0,
        totalPastBalance: 0,
        totalDeltaAmount: 0,
        totalDeltaPercentage: 0,
        overallTrend: 'neutral'
      }
    };
  }

  let accountTypes = [];
  let metadata = {
    comparisonType: responseData.comparisonType || timePeriod,
    currentPeriodDate: responseData.currentPeriodDate || '',
    pastPeriodDate: responseData.pastPeriodDate || '',
    description: responseData.description || ''
  };

  // Handle quarterly rolling data structure for account types
  if (timePeriod === 'quarterly-rolling' && responseData.quarters) {
    accountTypes = processQuarterlyRollingAccountTypeDeltas(responseData.quarters);
    metadata.quarterDetails = responseData.quarterDetails || {};
  } 
  // Handle YTD data structure for account types
  else if (timePeriod === 'ytd') {
    accountTypes = responseData.accountTypes ? responseData.accountTypes.map(accountType => ({
      accountType: accountType.accountType || accountType.account_type,
      accountCount: Number(accountType.accountCount || accountType.account_count || 0),
      // Map the actual API response field names
      currentBalance: Number(accountType.totalCurrentBalance || accountType.currentBalance || accountType.current_balance || 0),
      pastBalance: Number(accountType.totalPastBalance || accountType.pastBalance || accountType.past_balance || 0),
      deltaAmount: Number(accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0),
      deltaPercentage: Number(accountType.deltaPercentage || accountType.delta_percentage || 0),
      // Additional fields from your API response
      accountCategories: accountType.accountCategories || [],
      accountSubtypes: accountType.accountSubtypes || [],
      currentPeriodDate: accountType.currentPeriodDate || '',
      pastPeriodDate: accountType.pastPeriodDate || '',
      // Computed fields
      trend: determineTrend(accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0),
      formattedDelta: formatDeltaDisplay(
        accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0,
        accountType.deltaPercentage || accountType.delta_percentage || 0
      )
    })) : [];
    
    metadata.ytdDays = responseData.ytdDays || 0;
  } 
  // Handle regular period deltas for account types
  else if (responseData.accountTypes) {
    accountTypes = responseData.accountTypes.map(accountType => ({
      accountType: accountType.accountType || accountType.account_type,
      accountCount: Number(accountType.accountCount || accountType.account_count || 0),
      // Map the actual API response field names
      currentBalance: Number(accountType.totalCurrentBalance || accountType.currentBalance || accountType.current_balance || 0),
      pastBalance: Number(accountType.totalPastBalance || accountType.pastBalance || accountType.past_balance || 0),
      deltaAmount: Number(accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0),
      deltaPercentage: Number(accountType.deltaPercentage || accountType.delta_percentage || 0),
      // Additional fields from your API response
      accountCategories: accountType.accountCategories || [],
      accountSubtypes: accountType.accountSubtypes || [],
      currentPeriodDate: accountType.currentPeriodDate || '',
      pastPeriodDate: accountType.pastPeriodDate || '',
      // Computed fields
      trend: determineTrend(accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0),
      formattedDelta: formatDeltaDisplay(
        accountType.totalDeltaAmount || accountType.deltaAmount || accountType.delta_amount || 0,
        accountType.deltaPercentage || accountType.delta_percentage || 0
      )
    }));
  }

  const summary = calculateAccountTypeSummary(accountTypes, timePeriod);

  return {
    comparisonType: metadata.comparisonType,
    accounts: [],
    accountTypes,
    metadata,
    summary,
    lastUpdated: new Date().toISOString()
  };
};

// Updated calculateAccountTypeSummary to handle the corrected field names
const calculateAccountTypeSummary = (accountTypes, timePeriod) => {
  if (!accountTypes || accountTypes.length === 0) {
    return {
      totalCurrentBalance: 0,
      totalPastBalance: 0,
      totalDeltaAmount: 0,
      totalDeltaPercentage: 0,
      overallTrend: 'neutral',
      accountTypeCount: 0,
      totalAccountCount: 0
    };
  }

  let totalCurrentBalance, totalPastBalance;

  if (timePeriod === 'quarterly-rolling') {
    totalCurrentBalance = accountTypes.reduce((sum, acc) => 
      sum + (acc.currentQuarterBalance || acc.currentBalance || 0), 0);
    totalPastBalance = accountTypes.reduce((sum, acc) => 
      sum + (acc.pastQuarterBalance || acc.pastBalance || 0), 0);
  } else {
    totalCurrentBalance = accountTypes.reduce((sum, acc) => 
      sum + (acc.currentBalance || 0), 0);
    totalPastBalance = accountTypes.reduce((sum, acc) => 
      sum + (acc.pastBalance || 0), 0);
  }

  const totalDeltaAmount = totalCurrentBalance - totalPastBalance;
  const totalDeltaPercentage = totalPastBalance !== 0 
    ? (totalDeltaAmount / totalPastBalance) * 100 
    : 0;

  const totalAccountCount = accountTypes.reduce((sum, acc) => 
    sum + (acc.accountCount || 0), 0);

  return {
    totalCurrentBalance,
    totalPastBalance,
    totalDeltaAmount,
    totalDeltaPercentage,
    overallTrend: determineTrend(totalDeltaAmount),
    accountTypeCount: accountTypes.length,
    totalAccountCount,
    formattedSummary: formatDeltaDisplay(totalDeltaAmount, totalDeltaPercentage)
  };
};


  // Group delta accounts by type for lookup
  const deltaAccountsMap = useMemo(() => {
  if (!deltaAccounts || deltaAccounts.length === 0) return {};
  return deltaAccounts.reduce((map, account) => {
    map[account.accountId] = account;
    return map;
  }, {});
}, [deltaAccounts]);

  const oneMonthDeltaAccounts = useSelector(state => {
  // Filter for one-month period
  const selectedPeriod = 'one-month';
  // If your delta data is stored by period, use that
  const data = state.delta.accountTypeData?.[selectedPeriod]?.data;
  // If not, use selectDeltaAccounts (which returns all accounts for the current period)
  // If your app always loads one-month data for selectDeltaAccounts, you can use:
  // const data = selectDeltaAccounts(state);
  return Array.isArray(data) ? data : [];
});

const oneMonthDeltaAccountsMap = useMemo(() => {
  if (!oneMonthDeltaAccounts || oneMonthDeltaAccounts.length === 0) return {};
  return oneMonthDeltaAccounts.reduce((map, account) => {
    map[account.accountId] = account;
    return map;
  }, {});
}, [oneMonthDeltaAccounts]);

  // UPDATED: Dynamic colors based on current theme (fixed border color issue)
  const colors = useMemo(() => ({
    positive: currentTheme.colors.primary,
    negative: currentTheme.colors.secondary,
    neutral: darkMode ? 'text-slate-400' : 'text-slate-600',
    bg: darkMode ? currentTheme.colors.darkBackground : currentTheme.colors.background,
    cardBg: darkMode ? currentTheme.colors.darkCardBg : currentTheme.colors.cardBg,
    text: darkMode ? currentTheme.colors.darkText : currentTheme.colors.text,
    border: darkMode ? currentTheme.colors.darkBorder : currentTheme.colors.border,
    primary: currentTheme.colors.primary,
    secondary: currentTheme.colors.secondary,
    success: currentTheme.colors.success,
    accent: currentTheme.colors.accent
  }), [currentTheme, darkMode]);

  // Format time period for display
  const formatTimePeriodDisplay = (period) => {
    const displayNames = {
      'one-month': '1 Month',
      'three-month': '3 Months',
      'half-year': '6 Months',
      'yearly': '1 Year',
      'ytd': 'Year to Date',
      'quarterly-aggregate': 'Quarterly'
    };
    return displayNames[period] || period;
  };

  // Enhanced trend icon with better styling
  const TrendIcon = ({ trend, className = "" }) => {
    const iconProps = { className: `${className} transition-all duration-200` };
    
    switch (trend) {
      case 'increase':
        return <FontAwesomeIcon icon={faArrowUp} {...iconProps} />;
      case 'decrease':
        return <FontAwesomeIcon icon={faArrowDown} {...iconProps} />;
      default:
        return <FontAwesomeIcon icon={faEquals} {...iconProps} />;
    }
  };
  
 
 
  const [tableData, setTableData] = useState({
    Cash: [],
    CreditCards: [],
    Investments: [],
    LoanAccounts: [],
  });

  const [showConnectionModal, setShowConnectionModal] = useState(false);
  const [showAIChat, setShowAIChat] = useState(false);

  // Handle AI chat toggle with sidebar coordination
  const handleAIChatToggle = () => {
    const newShowAIChat = !showAIChat;
    setShowAIChat(newShowAIChat);

    // Call sidebar handlers if provided
    if (newShowAIChat && onAIChatOpen) {
      onAIChatOpen();
    } else if (!newShowAIChat && onAIChatClose) {
      onAIChatClose();
    }
  };

  // Handle AI chat close (for the close button in the chat)
  const handleAIChatClose = () => {
    setShowAIChat(false);
    if (onAIChatClose) {
      onAIChatClose();
    }
  };

  const [boxOrder, setBoxOrder] = useState(() => {
    const savedOrder = localStorage.getItem("accountsBoxOrder");
    const parsedOrder = savedOrder ? JSON.parse(savedOrder) : ALL_CATEGORIES;
    
    // Ensure we always have all 4 categories in the order
    const completeOrder = [...ALL_CATEGORIES];
    
    if (savedOrder) {
      // Reorder based on saved order, but ensure all categories are present
      const orderedCategories = [];
      parsedOrder.forEach(category => {
        if (ALL_CATEGORIES.includes(category)) {
          orderedCategories.push(category);
        }
      });
      
      // Add any missing categories to the end
      ALL_CATEGORIES.forEach(category => {
        if (!orderedCategories.includes(category)) {
          orderedCategories.push(category);
        }
      });
      
      return orderedCategories;
    }
    
    return completeOrder;
  });
 
  // Track if refresh is in progress to avoid showing premature errors
  const [isRefreshInProgress, setIsRefreshInProgress] = useState(false);
  const [chartType, setChartType] = useState('area');
  const [timePeriod, setTimePeriod] = useState('yearly');
  const [selectedAccount, setSelectedAccount] = useState('all');

  useEffect(() => {
    logEvent('AccountsDashboard', 'PageLoad', {});
    // dispatch(fetchLinkToken());
    dispatch(fetchAccountDetails()); 
  }, []);

 useEffect(() => {
  if (accounts && accounts.length > 0) {
    updateTableData(accounts);
  } else {
    console.log("Fetched accounts array is empty:", accounts);
  }
}, [accounts, syncingAccounts, deltaAccountsMap]);

  useEffect(() => {
    const savedTableOrder = localStorage.getItem("accountsRowOrder");
    if (savedTableOrder) {
      const parsed = JSON.parse(savedTableOrder);
      setTableData((prev) => {
        const newData = { ...prev };
        for (const key in parsed) {
          if (newData[key]) {
            newData[key] = parsed[key]
              .map((savedId) => newData[key].find((d) => d.id === savedId))
              .filter(Boolean);
          }
        }
        return newData;
      });
    }
  }, []);

  // Monitor refresh status to control error display
  useEffect(() => {
    if (refreshAllStatus === 'loading') {
      setIsRefreshInProgress(true);
    } else if (refreshAllStatus === 'success' || refreshAllStatus === 'failed') {
      setIsRefreshInProgress(false);
      // Clear the refresh status after showing success message briefly
      if (refreshAllStatus === 'success') {
        const timer = setTimeout(() => {
          dispatch(resetError()); // This should also reset refreshAllStatus in your slice
        }, 3000); // Show success message for 3 seconds
        return () => clearTimeout(timer);
      }
    }
  }, [refreshAllStatus]);

  // Clear error after 5 seconds, but not if refresh is in progress
  useEffect(() => {
    if (error && !isRefreshInProgress) {
      const timer = setTimeout(() => {
        dispatch(resetError());
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, isRefreshInProgress]);

  const handleRowDragEnd = (result, category) => {
    if (!result.destination) return;

    const draggedAccount = tableData[category][result.source.index];
    logEvent('AccountsDashboard', 'ReorderAccountsInCategory', {
      category,
      accountId: draggedAccount?.id,
      fromIndex: result.source.index,
      toIndex: result.destination.index
    });
    const updatedRows = Array.from(tableData[category]);
    const [movedRow] = updatedRows.splice(result.source.index, 1);
    updatedRows.splice(result.destination.index, 0, movedRow);

    const newTableData = {
      ...tableData,
      [category]: updatedRows,
    };

    setTableData(newTableData);
    const rowOrder = JSON.parse(localStorage.getItem("accountsRowOrder") || "{}");
    rowOrder[category] = updatedRows.map((row) => row.id);
    localStorage.setItem("accountsRowOrder", JSON.stringify(rowOrder));
  };

  const formatAccounts = (type) =>
  accounts
    .filter((account) => account.accountType === type)
    .map((account, index) => {
      const accountId = account.id || account.accountId;
      const deltaAccount = deltaAccountsMap[accountId];
      return {
        id: accountId || index,
        institution: account.financialInstName || account.institutionId || "Unknown",
        accountName: account.accountName,
        accountNumber: maskAccountNumber(account.accountMask),
        numericBalance: account.balance || 0,
        balance: `$${account.balance ? account.balance.toFixed(2) : "0.00"}`,
        lastSyncTime: account.lastSyncTime || null,
        lastSyncFormatted: formatLastSyncTime(account.lastSyncTime),
        monthlyBalances: account.aggregatedBalances || [
          { month: "Jan", balance: (account.balance || 0) - 50 },
          { month: "Feb", balance: account.balance || 0 },
          { month: "Mar", balance: (account.balance || 0) + 75 },
        ],
        isSyncing: syncingAccounts.includes(accountId),
        // Show delta data from fetchDeltaData (accountid-based, one-month)
        deltaData: deltaAccount ? {
          trend: deltaAccount.trend,
          percentage: deltaAccount.formattedDelta?.percentage || '0%',
          amount: deltaAccount.formattedDelta?.amount || '$0.00',
          hasDelta: true
        } : {
          trend: 'neutral',
          percentage: '0%',
          amount: '$0.00',
          hasDelta: false
        }
      };
    });

  const clearSavedRowOrders = () => {
    localStorage.removeItem("accountsRowOrder");
    window.location.reload();
  };

const updateTableData = (accounts) => {
  const savedRowOrder = JSON.parse(localStorage.getItem("accountsRowOrder") || "{}");
  const formatted = {
    Cash: formatAccounts("depository"),
    CreditCards: formatAccounts("credit"),
    Investments: formatAccounts("investment"),
    LoanAccounts: formatAccounts("loan"),
  };
  const reordered = {};
  Object.entries(formatted).forEach(([key, list]) => {
    const order = savedRowOrder[key];
    if (order) {
      const orderedList = [];
      const idToRow = Object.fromEntries(list.map((row) => [row.id, row]));
      order.forEach((id) => {
        if (idToRow[id]) orderedList.push(idToRow[id]);
      });
      const remaining = list.filter((row) => !order.includes(row.id));
      reordered[key] = [...orderedList, ...remaining];
    } else {
      reordered[key] = list;
    }
  });
  
  // Use functional update to prevent unnecessary re-renders
  setTableData(prevData => {
    // Only update if data actually changed
    const hasChanged = JSON.stringify(prevData) !== JSON.stringify(reordered);
    return hasChanged ? reordered : prevData;
  });
};
const handleSyncAccount = (accountId) => {
  logEvent('AccountsDashboard', 'SyncAccount', {
    accountId,
    accountType: accounts.find(acc => acc.id === accountId || acc.accountId === accountId)?.accountType
  });

    // Clear any existing re-auth status for this account
    if (reauthStatus[accountId] && reauthStatus[accountId] !== 'idle') {
      clearReauth(accountId);
    }

    // Update sync status
    const updateAccountSyncStatus = (isSyncing) => {
      setTableData((prevData) => {
        const newData = { ...prevData };
        
        for (const category of Object.keys(newData)) {
          const accountIndex = newData[category].findIndex(acct => acct.id === accountId);
          if (accountIndex !== -1) {
            newData[category] = [...newData[category]];
            newData[category][accountIndex] = {
              ...newData[category][accountIndex],
              isSyncing
            };
            break;
          }
        }
        
        return newData;
      });
    };

  // Set syncing to true
  updateAccountSyncStatus(true);

    dispatch(syncAccount(accountId))
      .then(() => {
        dispatch(fetchAccountDetails()).then(() => {
          updateAccountSyncStatus(false);
        });
      })
      .catch((error) => {
        updateAccountSyncStatus(false);
        console.error('Sync failed:', error);
      });
  };


  // FIXED: Now isLoading is available for this conditional check
  if (isLoading && !hasLoadedOnce) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <PaymentLoader darkMode={darkMode} />
        <p className={`mt-4 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
          Loading your accounts...
        </p>
      </div>
    );
  }
  const handleRefreshAll = () => {
    logEvent('AccountsDashboard', 'RefreshAllAccounts', {
      accountsCount: accounts?.length || 0,
      linkedAccountsCount: Object.values(tableData).flat().length
    });
    // Clear any existing errors before starting refresh
    dispatch(resetError());
    setIsRefreshInProgress(true);
 dispatch(refreshAllAccounts())
    .then(() => {
      // After successful refresh, fetch updated account details
      return dispatch(fetchAccountDetails());
    })
    .then(() => {
      setIsRefreshInProgress(false);
    })
    .catch((error) => {
      console.error('Refresh failed:', error);
      setIsRefreshInProgress(false);
    });
};
// Add this function to determine the sync issue reason

  const handleDragEnd = (result) => {
    if (!result.destination) return;
    logEvent('AccountsDashboard', 'ReorderAccountCategories', {
      fromIndex: result.source.index,
      toIndex: result.destination.index,
      movedCategory: boxOrder[result.source.index]
    });
    const newOrder = Array.from(boxOrder);
    const [removed] = newOrder.splice(result.source.index, 1);
    newOrder.splice(result.destination.index, 0, removed);
    setBoxOrder(newOrder);
    localStorage.setItem("accountsBoxOrder", JSON.stringify(newOrder));
  };

  const onSuccess = async (publicToken, metadata) => {
    logEvent('AccountsDashboard', 'PlaidConnectionSuccess', {
      institutionId: metadata?.institution?.institution_id,
      institutionName: metadata?.institution?.name,
      accountsCount: metadata?.accounts?.length
    });
    try {
      await dispatch(exchangePublicToken(publicToken));
      await dispatch(fetchAccountDetails(1));
      setShowConnectionModal(false);
    } catch (err) {
      logEvent('AccountsDashboard', 'PlaidConnectionError', {
        error: err.message || 'Unknown error'
      });
      console.error("Error linking accounts:", err);
    }
  };

  // Helper function to safely render error messages - exclude refresh-related errors while in progress
  const renderError = (error) => {
    if (!error) return null;
    
    // Don't show refresh-related errors while refresh is in progress
    if (isRefreshInProgress) {
      return null;
    }
    
    // Don't show certain sync-related errors that are expected during long operations
    const errorMessage = typeof error === 'string' ? error : (error?.message || error?.error || 'An error occurred');
    const isRefreshError = errorMessage.toLowerCase().includes('refresh') || 
                          errorMessage.toLowerCase().includes('synchronization') ||
                          errorMessage.toLowerCase().includes('sync');
    
    // Skip showing refresh/sync errors if refresh is in progress or recently completed
    if (isRefreshError && (isRefreshInProgress || refreshAllStatus === 'loading')) {
      return null;
    }
    
  };

  // Success message for refresh all
  const renderRefreshSuccessMessage = () => {
    if (refreshAllStatus === 'success' && !isRefreshInProgress) {
      return (
        <div className={`border rounded-xl px-6 py-4 mb-6 flex items-center shadow-lg animate-slide-down`}
          style={{
            backgroundColor: darkMode ? `${currentTheme.colors.success}20` : `${currentTheme.colors.success}10`,
            borderColor: `${currentTheme.colors.success}30`,
            color: currentTheme.colors.success
          }}>
          <div className={`p-2 rounded-full mr-4`} style={{ backgroundColor: `${currentTheme.colors.success}20` }}>
            <FontAwesomeIcon icon={faSync} className="text-lg" />
          </div>
          <span className="flex-1 font-medium">All accounts refreshed successfully!</span>
          <button 
            onClick={() => dispatch(resetError())}
            className={`ml-4 p-2 rounded-full transition-colors duration-200`}
            style={{ color: currentTheme.colors.success }}
            onMouseEnter={(e) => e.target.style.backgroundColor = `${currentTheme.colors.success}20`}
            onMouseLeave={(e) => e.target.style.backgroundColor = 'transparent'}
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>
      );
    }
    return null;
  };

  // Loading message for refresh all
  const renderRefreshLoadingMessage = () => {
    if (refreshAllStatus === 'loading' || isRefreshInProgress) {
   
    }
    return null;
  };

  // Enhanced table rendering with modern design
  const renderTable = (boxId, dragHandleProps) => {
    const data = tableData[boxId] || [];
    const categoryName = formatCategoryName(boxId);
    const categoryIcon = getCategoryIcon(boxId);
    
    // Create mock data when no real data exists
    const displayData = data.length === 0 ? [{
      id: `mock-${boxId}`,
      institution: "No accounts connected",
      accountNumber: "••••••0000",
      numericBalance: 0,
      balance: "$0.00",
      lastSyncTime: null,
      lastSyncFormatted: "Never synced",
      monthlyBalances: [
        { month: "Jan", balance: 0 },
        { month: "Feb", balance: 0 },
        { month: "Mar", balance: 0 }
      ],
      isSyncing: false,
      isMock: true,
      deltaData: {
        trend: 'neutral',
        percentage: '0%',
        amount: '$0.00',
        hasDelta: false
      }
    }] : data;
   const totalBalance = data
  .reduce((sum, row) => sum + (parseFloat(row.numericBalance) || 0), 0)
  .toLocaleString('en-US', { 
    style: 'currency', 
    currency: 'USD',
    minimumFractionDigits: 2, 
    maximumFractionDigits: 2 
  });
    const getTableCardStyle = () => {
      const style = {
        backgroundColor: colors.cardBg,
        borderColor: colors.border
      };
      
      // Border radius
      if (currentTheme.layout?.borderRadius === 'square') {
        style.borderRadius = '8px';
      } else if (currentTheme.layout?.borderRadius === 'pill') {
        style.borderRadius = '24px';
      } else {
        style.borderRadius = '16px';
      }
      
      // Shadows
      if (currentTheme.layout?.shadows === false) {
        style.boxShadow = 'none';
      }
      return style;
    };
    const getTableClasses = () => {
      let classes = `border overflow-hidden group transition-all duration-300`;
      
      // Card style
      if (currentTheme.layout?.cardStyle === 'flat') {
        classes += ` shadow-none`;
      } else if (currentTheme.layout?.cardStyle === 'outlined') {
        classes += ` shadow-none border-2`;
      } else {
        classes += ` shadow-xl`;
      }
      
      // Hover effects
      if (currentTheme.layout?.animations !== false) {
        classes += ` `;
      }
      return classes;
    };
    return (
      <div className={getTableClasses()} style={getTableCardStyle()}>
        {/* Enhanced Header */}
        <div className={`px-4 py-3 border-b`}
          style={{ 
            background: darkMode 
              ? `linear-gradient(to right, ${currentTheme.colors.darkCardBg}, ${currentTheme.colors.darkCardBg})`
              : `linear-gradient(to right, ${currentTheme.colors.primary}08, ${currentTheme.colors.accent}05)`,
            borderColor: colors.border
          }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div
                {...dragHandleProps}
                className="opacity-0 group-hover:opacity-100 transition-all duration-200 p-1 rounded-lg hover:bg-white/10"
              >
                <DragHandleIcon darkMode={darkMode} />
              </div>
              <div className={`p-2 rounded-xl shadow-lg border`} 
                style={{ 
                  backgroundColor: darkMode ? `${currentTheme.colors.darkCardBg}80` : `${currentTheme.colors.cardBg}80`,
                  borderColor: colors.border
                }}>
                <FontAwesomeIcon icon={categoryIcon} className={`text-xl`} style={{ color: currentTheme.colors.primary }} />
              </div>
              <div>
                <h3 className={`text-lg font-bold`} style={{ color: colors.text }}>{categoryName}</h3>
                <p className={`text-xs ${colors.neutral}`}>{data.length} account{data.length !== 1 ? 's' : ''}</p>
              </div>
            </div>
            <div className="text-right">
              <div className={`text-xl font-bold`} style={{ color: colors.text }}>{totalBalance}</div>
              <div className={`text-xs ${colors.neutral}`}>Total Balance</div>
            </div>
          </div>
        </div>

        {/* Enhanced Table Body */}
        <DragDropContext onDragEnd={(result) => handleRowDragEnd(result, boxId)}>
          <Droppable droppableId={`rows-${boxId}`}>
            {(provided) => (
              <div
                ref={provided.innerRef}
                {...provided.droppableProps}
                className="divide-y divide-slate-200/20"
              >
                {displayData.map((row, index) => {
                  const isPositive = row.numericBalance >= 0;
                  const isMockRow = row.isMock;
                  const deltaData = row.deltaData || {};
                   const accountReauthStatus = reauthStatus[row.id] || 'idle';
                  return (
                    <Draggable key={row.id} draggableId={String(row.id)} index={index} isDragDisabled={isMockRow}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          {...(isMockRow ? {} : provided.dragHandleProps)}
                          className={`
                            px-4 py-3 transition-all duration-200
                            ${isMockRow 
                              ? (darkMode ? 'bg-slate-800/30 text-slate-500' : 'bg-slate-50/50 text-slate-400') 
                              : ''
                            }
                            ${snapshot.isDragging ? "shadow-2xl z-10 rounded-xl" : ""}
                            ${!isPositive && !isMockRow ? "text-rose-500" : ""}
                          `}
                        >
                          <div className="grid grid-cols-12 gap-3 items-center">
                            {/* Institution & Account Info */}
                            <div className="col-span-4">
                              <div className="flex items-center space-x-3">
                                <div className={``}
                                  style={{ 
                                    backgroundColor: colors.cardBg,
                                    borderColor: colors.border
                                  }}>
                                  <BankIcon
                                    institutionName={row.institution}
                                     size={50}
                                    className="w-12 h-12"
                                  />
                                </div>
                                <div>
                                  <div className={`font-semibold text-sm`} style={{ color: colors.text }}>{row.institution}</div>
                                  <div className={`text-xs ${colors.neutral}`}>{row.accountName}</div>
                                  <div className={`text-xs ${colors.neutral} font-mono`}>{row.accountNumber}</div>
                                </div>
                              </div>
                            </div>

                            {/* Balance & Delta */}
                            <div className="col-span-3">
                              <div className="space-y-1">
                                <div className={`text-base font-bold`} style={{ color: colors.text }}>
                                  {formatAmountWithCommas(row.numericBalance)}
                                </div>
                                {/* Show delta for real rows (not mock) */}
                                {!isMockRow && row.deltaData.hasDelta && (
                                  <div className="flex flex-col text-xs mt-1">
                                    <div className="flex items-center space-x-1">
                                      <TrendIcon 
                                        trend={row.deltaData.trend} 
                                        className="text-xs"
                                        style={{ 
                                          color: row.deltaData.trend === 'increase' ? currentTheme.colors.primary : 
                                                 row.deltaData.trend === 'decrease' ? '#ef4444' : // Red for decrease
                                                 colors.neutral 
                                        }}
                                      />
                                      <span className="font-semibold"
                                        style={{
                                          color: row.deltaData.trend === 'increase' ? currentTheme.colors.primary :
                                                 row.deltaData.trend === 'decrease' ? '#ef4444' :
                                                 colors.neutral
                                        }}>
                                        {row.deltaData.amount}
                                      </span>
                                    </div>
                                    <span className="ml-5"
                                      style={{
                                        color: row.deltaData.trend === 'increase' ? currentTheme.colors.primary :
                                               row.deltaData.trend === 'decrease' ? '#ef4444' :
                                               colors.neutral
                                      }}>
                                      {row.deltaData.percentage}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Chart */}
                      <div className="col-span-3">
                      {/* {!isMockRow ? (
                        <AccountMiniChart 
                          accountId={row.accountId || row.id} // Pass the account ID
                          data={row.monthlyBalances}
                          darkMode={darkMode}
                          currentTheme={currentTheme}
                        />
                      ) : (
                        <div className={`h-16 rounded-lg flex items-center justify-center`}
                          style={{ backgroundColor: darkMode ? '#********' : '#e2e8f0' }}>
                          <span className={`text-xs ${colors.neutral}`}>No data</span>
                        </div>
                      )} */}
                      <AccountMiniChart
    accountId={row.accountId || row.id}
    duration={1}    // 1 month
    interval={8}    // 8 days per interval = ~4 data points  
    darkMode={darkMode}
    currentTheme={currentTheme}
  />
                    </div>

                            {/* Sync & Last Update */}
                           
<div className="col-span-2">
                              <div className="flex flex-col items-center space-y-2">
                                {!isMockRow && (
                                  <ReauthButton
                                    accountId={row.id}
                                    syncErrorFlags={syncErrorFlags}
                                    syncErrors={syncErrors}
                                    onSyncAccount={handleSyncAccount}
                                    darkMode={darkMode}
                                    currentTheme={currentTheme}
                                  />
                                )}

                                <div className="text-center">
                                  <div className="flex items-center justify-center space-x-1 mb-1">
                                    <FontAwesomeIcon icon={faClock} className={`text-xs ${colors.neutral}`} />
                                    <div className={`text-xs ${colors.neutral} leading-tight`}>
                                      {row.lastSyncFormatted}
                                    </div>
                                  </div>
                                  
                                  {/* Show sync error message */}
                                  {syncErrors[row.id] && !reauthStatus[row.id] && (
                                    <div className={`text-xs text-red-500 mt-1 text-center max-w-48 truncate`}
                                         title={syncErrors[row.id]}>
                                      {syncErrors[row.id]}
                                    </div>
                                  )}
                                  
                                  {/* Show re-auth status messages */}
                                  {accountReauthStatus === 'creating-token' && (
                                    <div className="text-xs text-yellow-600 mt-1">
                                      Creating token...
                                    </div>
                                  )}
                                  {accountReauthStatus === 'awaiting-link' && (
                                    <div className="text-xs text-blue-600 mt-1 animate-pulse">
                                      Click to authenticate
                                    </div>
                                  )}
                                  {accountReauthStatus === 'exchanging' && (
                                    <div className="text-xs text-yellow-600 mt-1">
                                      Completing auth...
                                    </div>
                                  )}
                                  {accountReauthStatus === 'success' && (
                                    <div className="text-xs text-green-600 mt-1">
                                      ✓ Re-authenticated!
                                    </div>
                                  )}
                                  {reauthErrors[row.id] && (
                                    <div className="text-xs text-red-500 mt-1 text-center max-w-48 truncate"
                                         title={reauthErrors[row.id]}>
                                      Re-auth error
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                         
                          </div>
                        </div>
                      )}
                    </Draggable>
                  );
                })}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      </div>
    );
  };

  return (
    <div className="min-h-screen w-full font-inter"
      style={{
        backgroundColor: colors.bg,
        fontFamily: currentTheme.font.value
      }}>
      {/* Main Content Area */}
      <div className={`transition-all duration-300`} style={{ marginRight: showAIChat ? '384px' : '0' }}>
        <div className={`px-4 sm:px-6 lg:px-8 py-8 w-full`}
          style={{
            maxWidth: currentTheme.layout?.maxWidth || 'none',
            margin: currentTheme.layout?.centered ? '0 auto' : '0'
          }}>
        {/* Enhanced Header */}
        <div className={`flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 space-y-4 lg:space-y-0 ${
          currentTheme.layout?.spacing === 'compact' ? 'mb-4' : 
          currentTheme.layout?.spacing === 'spacious' ? 'mb-12' : 'mb-8'
        }`}>
          <div className="flex items-center space-x-4">
            <div className={`p-3 rounded-xl shadow-lg border`}
              style={{ 
                backgroundColor: darkMode ? `${currentTheme.colors.primary}20` : `${currentTheme.colors.primary}10`,
                borderColor: colors.border
              }}>
              <FontAwesomeIcon icon={faRobot} className={`text-2xl`} style={{ color: currentTheme.colors.primary }} />
            </div>
            <h1 className={`text-4xl font-bold bg-gradient-to-r bg-clip-text text-transparent`}
              style={{ 
                backgroundImage: `linear-gradient(to right, ${currentTheme.colors.primary}, ${currentTheme.colors.secondary})`,
                fontFamily: currentTheme.font.value
              }}>
              Accounts
            </h1>
          </div>
          
          <div className={`flex space-x-4 ${
            currentTheme.layout?.spacing === 'compact' ? 'space-x-2' : 
            currentTheme.layout?.spacing === 'spacious' ? 'space-x-6' : 'space-x-4'
          }`}>
            {/* AI Chat Icon */}
            <button
              className={`p-3 rounded-xl transition-all duration-200 hover:scale-110 ${
                darkMode
                  ? "bg-gray-800/50 border-gray-700/50 hover:bg-gray-800/70"
                  : "bg-white/70 border-white/50 hover:bg-white/90"
              } backdrop-blur-lg border shadow-lg`}
              onClick={handleAIChatToggle}
              title="AI Assistant"
            >
              <svg
                className="w-6 h-6 text-purple-500"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path d="M12 2L13.09 6.26L17.64 7.35L13.09 8.44L12 12.7L10.91 8.44L6.36 7.35L10.91 6.26L12 2Z" />
                <path d="M19.5 8.5L20.5 11L23 12L20.5 13L19.5 15.5L18.5 13L16 12L18.5 11L19.5 8.5Z" />
                <path d="M4.5 16.5L5.5 19L8 20L5.5 21L4.5 23.5L3.5 21L1 20L3.5 19L4.5 16.5Z" />
              </svg>
            </button>
            
            {/* Design Palette Dropdown */}
            <DesignPaletteDropdown 
              darkMode={darkMode}
              onThemeChange={handleThemeChange}
            />
            
            <button
              className={`hover:shadow-lg text-black py-3 px-6 flex items-center space-x-2 transition-all duration-200 font-semibold shadow-md ${
                currentTheme.layout?.animations === false ? '' : 'hover:scale-105'
              }`}
              style={{ 
                backgroundColor: colors.primary,
                borderRadius: currentTheme.layout?.borderRadius === 'square' ? '6px' : 
                           currentTheme.layout?.borderRadius === 'pill' ? '50px' : '12px',
                boxShadow: currentTheme.layout?.shadows === false ? 'none' : ''
              }}
              onClick={() => setShowConnectionModal(true)}
            >
              <FontAwesomeIcon icon={faPlus} />
              <span>Add Account</span>
            </button>
            <button
              className={`hover:shadow-lg text-black py-3 px-6 flex items-center space-x-2 transition-all duration-200 font-semibold shadow-md disabled:opacity-50 disabled:cursor-not-allowed ${
                currentTheme.layout?.animations === false ? '' : 'hover:scale-105'
              }`}
              style={{ 
                backgroundColor: colors.secondary,
                borderRadius: currentTheme.layout?.borderRadius === 'square' ? '6px' : 
                           currentTheme.layout?.borderRadius === 'pill' ? '50px' : '12px',
                boxShadow: currentTheme.layout?.shadows === false ? 'none' : ''
              }}
              onClick={handleRefreshAll}
              disabled={!isLinked || refreshAllStatus === 'loading' || isRefreshInProgress}
            >
              <FontAwesomeIcon 
                icon={faRedo} 
                className={(refreshAllStatus === 'loading' || isRefreshInProgress) ? "animate-spin" : ""} 
              />
              <span>{(refreshAllStatus === 'loading' || isRefreshInProgress) ? "Syncing..." : "Refresh All"}</span>
            </button>
          </div>
        </div>

        {/* Status Messages */}
        {renderRefreshLoadingMessage()}
        {renderError(error)}
        {renderRefreshSuccessMessage()}

        {/* Stat Cards */}
       <StatCardGroupView darkMode={darkMode} currentTheme={currentTheme} accounts={accounts} />

        {/* Chart Controls & Main Chart */}
        
            <PortfolioChartControlView 
                darkMode = {darkMode}
                currentTheme = {currentTheme}
                accounts = {accounts}
                selectedAccount = {selectedAccount}
                setChartType = {setChartType}
                selectedChartType = {selectedChartType}
                chartType = {chartType}
                handleChartTypeChange = {handleChartTypeChange}
                selectedTimePeriod = {selectedTimePeriod}
                handleTimePeriodChange = {handleTimePeriodChange}
                timePeriod = {timePeriod}
            />

     
          {/* {isLinked && (
        <PortfolioChartControlView
          darkMode={darkMode}
          
          currentTheme={currentTheme}
          accounts={accounts}
        />
      )} */}
        {/* Enhanced Connection Modal */}
        {showConnectionModal && (
          <AccountConnectionProviderModal 
                darkMode = {darkMode}
                currentTheme = {currentTheme} 
                setShowConnectionModal = {setShowConnectionModal}  />
        )}

        {/* Enhanced No Accounts State */}
      

        {/* Enhanced Account Tables */}
       
          <DragDropContext onDragEnd={handleDragEnd}>
            <Droppable droppableId="categories">
              {(provided) => (
                <div
                  className={`${
                    currentTheme.layout?.spacing === 'compact' ? 'space-y-4' :
                    currentTheme.layout?.spacing === 'spacious' ? 'space-y-12' : 'space-y-8'
                  }`}
                  {...provided.droppableProps}
                  ref={provided.innerRef}
                >
                  {boxOrder.map((boxId, index) => (
                    <Draggable draggableId={boxId} index={index} key={boxId}>
                      {(provided, snapshot) => (
                        <div
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                          className={`transition-all duration-300 ${
                            snapshot.isDragging ? "z-50 rotate-2" : ""
                          } ${
                            currentTheme.layout?.animations === false ? '' : ''
                          }`}
                        >
                          {renderTable(boxId, provided.dragHandleProps)}
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
      
        {/* Enhanced Styles */}
        <style jsx>{`
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Nunito:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Lato:wght@300;400;500;600;700;800;900&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700;800&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700;800;900&display=swap');
        
        .font-inter {
          font-family: ${currentTheme.font.value};
        }

        @keyframes fade-in {
          from {
            opacity: 0;
          }
          to {
            opacity: 1;
          }
        }
        
        @keyframes slide-down {
          from {
            opacity: 0;
            transform: translateY(-20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes slide-up {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-fade-in {
          animation: fade-in 0.5s ease-out;
        }
        
        .animate-slide-down {
          animation: slide-down 0.3s ease-out;
        }

        .animate-slide-up {
          animation: slide-up 0.3s ease-out;
        }
        
        /* Improved scrollbar */
        ::-webkit-scrollbar {
          width: 8px;
        }
        
        ::-webkit-scrollbar-track {
          background: transparent;
        }
        
        ::-webkit-scrollbar-thumb {
          background: rgba(148, 163, 184, 0.3);
          border-radius: 10px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
          background: rgba(148, 163, 184, 0.5);
        }
        
        /* Enhanced hover effects */
        .group:hover .drag-handle {
          opacity: 1;
        }
        
        .drag-handle {
          opacity: 0;
          transition: opacity 0.2s ease;
        }

        /* Better responsive behavior */
        @media (max-width: 768px) {
          .grid-cols-12 {
            grid-template-columns: 1fr;
            gap: 1rem;
          }
          
          .col-span-4,
          .col-span-3,
          .col-span-2 {
            grid-column: span 1;
          }
        }

        /* Dynamic theme-based styles */
        .theme-gradient {
          background: linear-gradient(135deg, ${currentTheme.colors.primary}, ${currentTheme.colors.secondary});
        }
        
        .theme-shadow {
          box-shadow: 0 10px 25px ${currentTheme.colors.primary}20;
        }
        
        /* Smooth theme transitions */
        * {
          transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
        }

        /* AI Chat Window Animation */
        .translate-x-0 {
          transform: translateX(0);
        }
        
        .translate-x-full {
          transform: translateX(100%);
        }
        
        /* Chat window scrollbar */
        .overflow-y-auto::-webkit-scrollbar {
          width: 6px;
        }
        
        .overflow-y-auto::-webkit-scrollbar-track {
          background: transparent;
        }
        
        .overflow-y-auto::-webkit-scrollbar-thumb {
          background: rgba(148, 163, 184, 0.3);
          border-radius: 10px;
        }
        
        .overflow-y-auto::-webkit-scrollbar-thumb:hover {
          background: rgba(148, 163, 184, 0.5);
        }
      `}</style>
        </div>
      </div>

      {/* AI Chat Window - Outside main content */}
      {showAIChat && (
        <AccountAIChat
          darkMode={darkMode}
          currentTheme={currentTheme}
          setShowAIChat={handleAIChatClose}
          setSelectedChartType={setChartType}
          setSelectedTimePeriod={setTimePeriod}
        />
      )}
    </div>
  );
};

export default AccountsDashboard;