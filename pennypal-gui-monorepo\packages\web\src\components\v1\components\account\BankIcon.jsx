import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { fetchAllIcons } from '../../../../../../logic/redux/bankIconsSlice';
// import { logEvent } from '../../utils/EventLogger'; // Adjust path as needed

// Map of common bank names to standardized icon names
const BANK_ICON_MAP = {
  'bank of america': 'boa_icon',
  'chase': 'chase_icon',
  'wells fargo': 'wells_fargo_icon',
  'citi': 'citi_icon',
  'citibank': 'citi_icon',
  'capital one': 'capital_one_icon',
  'american express': 'amex_icon',
  'venmo': 'venmo_icon',
  'paypal': 'paypal_icon',
  'zelle': 'zelle_icon',
  'united airlines': 'united_icon',
  'starbucks': 'starbucks_icon',
  'mcdonalds': 'mcdonalds_icon',
  'uber': 'uber_icon',
  'default': 'defbank_icon',
  'amazon': 'amazon',
};

const BankIcon = ({
  institutionName,
  accountType = 'bank',
  className = '',
  size = 40,
  sizeClass,
  padding = 4,
  onClick,
  onHover,
  trackingId,
  tickerSymbol, // NEW: Add ticker symbol prop
  ...otherProps
}) => {
  const dispatch = useDispatch();
  
  const bankIconsState = useSelector(state => state?.bankIcons) || {
    icons: [],
    iconMap: {},
    tickerMap: {}, // NEW: Add ticker map
    loading: false,
    iconsLoaded: false
  };
  
  const { icons, iconMap, tickerMap, loading, iconsLoaded } = bankIconsState;
  const [svgContent, setSvgContent] = useState('');
  const [iconMatchType, setIconMatchType] = useState('');
  
  // Determine the Tailwind size classes based on sizeClass prop or size prop
  const getSizeClasses = () => {
    if (sizeClass) {
      switch (sizeClass) {
        case 'xs':
          return 'w-6 h-6 min-w-6 min-h-6';
        case 'sm':
          return 'w-8 h-8 min-w-8 min-h-8';
        case 'md':
          return 'w-12 h-12 min-w-12 min-h-12';
        case 'lg':
          return 'w-16 h-16 min-w-16 min-h-16';
        default:
          return 'w-8 h-8 min-w-8 min-h-8';
      }
    }
    
    // Default size mapping if no sizeClass is provided
    if (size <= 24) return 'w-6 h-6 min-w-6 min-h-6';
    if (size <= 32) return 'w-8 h-8 min-w-8 min-h-8';
    if (size <= 48) return 'w-12 h-12 min-w-12 min-h-12';
    return 'w-16 h-16 min-w-16 min-h-16';
  };

  // Calculate the inner SVG size with padding
  const innerSize = Math.max(size - (padding * 2), 0);
  
  // Normalize institution name
  const normalizedName = institutionName?.toLowerCase().trim() || '';
  const normalizedTicker = tickerSymbol?.toUpperCase().trim() || '';
  
  // Load all icons if they haven't been loaded
  useEffect(() => {
    console.log("Inside BankIcon--->");
    if (!iconsLoaded && !loading) {
      // Log icon loading attempt
      // logEvent('BankIcon', 'icon_load_started', {
      //   institutionName,
      //   accountType,
      //   size,
      //   sizeClass,
      //   trackingId
      // });

      dispatch(fetchAllIcons())
        .then(() => {
          // logEvent('BankIcon', 'icon_load_success', {
          //   institutionName,
          //   accountType,
          //   trackingId
          // });
        })
        .catch(err => {
          console.error("Failed to load icons:", err);
          // logEvent('BankIcon', 'icon_load_error', {
          //   institutionName,
          //   accountType,
          //   error: err.message,
          //   trackingId
          // });
        });
    }
  }, [dispatch, iconsLoaded, loading]);

  // Find the appropriate icon in the loaded icons
  useEffect(() => {
    if (!icons || icons.length === 0) return;

    let matchType = '';
    let foundContent = '';

    // PRIORITY 1: Ticker symbol match (for investment/stock icons)
    if (normalizedTicker && accountType === 'investment' && tickerMap?.[normalizedTicker]) {
      foundContent = tickerMap[normalizedTicker];
      matchType = 'ticker_match';
      console.log(`Found ticker match for ${normalizedTicker}:`, matchType);
    }
    // PRIORITY 2: Direct institution name match
    else if (iconMap?.[normalizedName]) {
      foundContent = iconMap[normalizedName];
      matchType = 'direct_match';
    }
    // PRIORITY 3: Mapped name match
    else if (BANK_ICON_MAP[normalizedName] && iconMap?.[BANK_ICON_MAP[normalizedName]]) {
      foundContent = iconMap[BANK_ICON_MAP[normalizedName]];
      matchType = 'mapped_match';
    }
    // PRIORITY 4: Keyword match in institution name
    else {
      const keyword = Object.keys(BANK_ICON_MAP).find(key =>
        normalizedName.includes(key)
      );
      if (keyword && iconMap?.[BANK_ICON_MAP[keyword]]) {
        foundContent = iconMap[BANK_ICON_MAP[keyword]];
        matchType = 'keyword_match';
      }
      // PRIORITY 5: Partial match in icon names
      else {
        const foundIcon = icons.find(icon => {
          const iconNameLower = icon.iconName.toLowerCase();
          return normalizedName.includes(iconNameLower) || iconNameLower.includes(normalizedName);
        });
        if (foundIcon) {
          foundContent = foundIcon.svgContent;
          matchType = 'partial_match';
        }
        // PRIORITY 6: Default fallback
        else if (iconMap?.['defbank_icon']) {
          foundContent = iconMap['defbank_icon'];
          matchType = 'default_fallback';
        }
      }
    }

    if (foundContent) {
      setSvgContent(foundContent);
      setIconMatchType(matchType);
    }
  }, [icons, iconMap, normalizedName]);

  // Handle click events
  const handleClick = (event) => {
    // logEvent('BankIcon', 'icon_clicked', {
    //   institutionName,
    //   normalizedName,
    //   accountType,
    //   iconMatchType,
    //   size,
    //   sizeClass,
    //   clickPosition: {
    //     x: event.clientX,
    //     y: event.clientY
    //   },
    //   trackingId
    // });

    // Call the original onClick handler if provided
    if (onClick) {
      onClick(event);
    }
  };

  // Handle hover events
  const handleMouseEnter = (event) => {
    // logEvent('BankIcon', 'icon_hovered', {
    //   institutionName,
    //   normalizedName,
    //   accountType,
    //   iconMatchType,
    //   size,
    //   sizeClass,
    //   trackingId
    // });

    // Call the original onHover handler if provided
    if (onHover) {
      onHover(event);
    }
  };

  // Handle hover end
  const handleMouseLeave = () => {
    // logEvent('BankIcon', 'icon_hover_end', {
    //   institutionName,
    //   normalizedName,
    //   accountType,
    //   iconMatchType,
    //   trackingId
    // });
  };

  const sizeClasses = getSizeClasses();
  
  // Log loading state (moved outside conditional to follow React hooks rules)
  useEffect(() => {
    if (!svgContent) {
      // logEvent('BankIcon', 'icon_loading_displayed', {
      //   institutionName,
      //   normalizedName,
      //   accountType,
      //   iconsLoaded,
      //   loading,
      //   trackingId
      // });
    }
  }, [svgContent, institutionName, normalizedName, accountType, iconsLoaded, loading]);

  // Log when icon is successfully rendered (moved outside conditional to follow React hooks rules)
  useEffect(() => {
    if (svgContent && iconMatchType) {
      // logEvent('BankIcon', 'icon_rendered', {
      //   institutionName,
      //   normalizedName,
      //   accountType,
      //   iconMatchType,
      //   size,
      //   sizeClass,
      //   trackingId
      // });
    }
  }, [svgContent, iconMatchType]);

  if (!svgContent) {
    // Return placeholder while loading with Tailwind classes
    return (
      <div
        className={`inline-flex items-center justify-center overflow-hidden bg-gray-100 rounded-full ${sizeClasses} ${className}`}
        style={{ width: size, height: size, padding }}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        {...otherProps}
      >
        {/* Loading spinner */}
        <div className="w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin"></div>
      </div>
    );
  }

  // Function to resize the SVG content
  const resizeSvgContent = (svgString) => {
    if (!svgString) return '';
    
    // Use a regex to find and replace width and height attributes in the SVG
    // This is a simple approach - for more complex SVGs, you might need a DOM parser
    const modifiedSvg = svgString
      .replace(/width="([^"]*)"/g, '')
      .replace(/height="([^"]*)"/g, '')
      .replace(/<svg/g, `<svg width="${innerSize}" height="${innerSize}" style="display:block;"`);
    
    return modifiedSvg;
  };

  // Render the SVG inline with padding using Tailwind classes
  return (
    <div 
      className={`flex items-center justify-center overflow-hidden box-border cursor-pointer ${sizeClasses} ${className}`}
      style={{ 
        width: size, 
        height: size, 
        padding: padding
      }}
      onClick={handleClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      role="img"
      aria-label={`${institutionName || tickerSymbol} icon`}
      {...otherProps}
    >
      <div
        dangerouslySetInnerHTML={{ __html: resizeSvgContent(svgContent) }}
        style={{ 
          width: innerSize, 
          height: innerSize
        }}
      />
    </div>
  );
};

export default BankIcon;