// Updated PortfolioChart Component - Fixed X-axis consistency
import React from 'react';
import { useSelector } from 'react-redux';
import { selectCurrentType, selectCurrentPeriod, formatYAxisValue } from '../../../../../../logic/redux/accountChartSlice'; 
import { LineChart, Line, AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const PortfolioChart = ({ darkMode, accounts, selectedTimePeriod, accountType = 'cash', chartView = 'bar', selectedAccount = 'all', currentTheme }) => {
  // Get chart data from Redux store
  const chartData = useSelector(state => state.accountChart?.chartData || []);
  const hasNoDataForCurrentType = useSelector(state => state.accountChart?.hasNoDataForCurrentType || false);
  const hasNoAccounts = useSelector(state => state.accountChart?.hasNoAccounts || false);
  const selectedChartType = accountType; 
  const loading = useSelector(state => state.accountChart?.loading || false);
  const currentType = useSelector(selectCurrentType);
  const currentPeriod = useSelector(selectCurrentPeriod);

  // Only render the chart when the data matches the current dropdown selection and not loading
  if (loading || currentType !== accountType || currentPeriod !== selectedTimePeriod) {
    return (
      <div className="flex items-center justify-center h-64">
        <span className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></span>
        <span className="ml-4 text-lg">Loading chart...</span>
      </div>
    );
  }

  // Process the Redux data for chart display
  const processChartData = () => {
    if (!chartData || chartData.length === 0) {
      return [];
    }

    // Map the Redux data to chart format
    return chartData.map(item => ({
      month: item.name || 'N/A',
      value: Math.abs(item.balance || 0), // Use absolute value for display
      cash: selectedAccount === 'all' ? Math.abs(item.balance || 0) * 0.4 : Math.abs(item.balance || 0),
      investments: selectedAccount === 'all' ? Math.abs(item.balance || 0) * 0.35 : 0,
      credit: selectedAccount === 'all' ? Math.abs(item.balance || 0) * 0.15 : 0,
      loans: selectedAccount === 'all' ? Math.abs(item.balance || 0) * 0.1 : 0,
      originalBalance: item.balance, // Keep original value for tooltips
      isMockData: item.isMockData || false
    }));
  };

  const formatValue = (val) => {
    return val.toLocaleString(undefined, { maximumFractionDigits: 0 });
  };

  const processedData = processChartData();
  const maxValue = processedData.length > 0 ? Math.max(...processedData.map(d => d.value)) : 0;
  const totalValue = processedData.length > 0 ? processedData[processedData.length - 1]?.value || 0 : 0;

  // Generate Y-axis ticks similar to Recharts
  const generateYAxisTicks = () => {
    if (maxValue === 0) return [0];
    
    const tickCount = 5;
    const ticks = [];
    for (let i = 0; i <= tickCount; i++) {
      ticks.push((maxValue / tickCount) * i);
    }
    return ticks.reverse(); // Reverse to match top-to-bottom order
  };

  const yAxisTicks = generateYAxisTicks();

  // No data state
  if (processedData.length === 0 && !hasNoAccounts) {
    return (
      <div className={`${
        darkMode 
          ? 'bg-gradient-to-br from-slate-800/50 to-slate-700/30' 
          : 'bg-gradient-to-br from-white to-green-50/10'
      } rounded-2xl p-6 backdrop-blur-sm border ${
        darkMode ? 'border-slate-700/50' : 'border-green-200/50'
      } shadow-xl hover:shadow-2xl transition-all duration-300`}>
        
        <div className="flex flex-col justify-center items-center h-64">
          <div className={`text-6xl mb-4 ${darkMode ? 'text-slate-600' : 'text-slate-400'}`}>📊</div>
          <h3 className={`text-xl font-semibold mb-2 ${darkMode ? 'text-white' : 'text-slate-800'}`}>
            No Data Available
          </h3>
          <p className={`text-sm ${darkMode ? 'text-slate-400' : 'text-slate-600'} text-center`}>
            {hasNoDataForCurrentType 
              ? `No ${selectedChartType} accounts found for the selected time period.`
              : 'No data available for the selected criteria.'
            }
          </p>
        </div>
      </div>
    );
  }

  // Render different chart types based on chartView prop
  const renderChart = () => {
    if (chartView === 'line' || chartView === 'area') {
      return renderLineAreaChart();
    }
    return renderBarChart();
  };

  const renderBarChart = () => (
    <div className="absolute inset-0 flex items-end justify-between px-4" style={{ marginLeft: '60px', paddingBottom: '20px' }}>
      {processedData.map((data, index) => {
        const barHeight = maxValue > 0 ? (data.value / maxValue) * 100 : 0;
        
        return (
          <div key={index} className="flex flex-col items-center group">
            {/* Stacked Bar */}
            <div className="relative flex flex-col-reverse" style={{ height: '220px' }}>
              <div 
                className="w-12 rounded-t-lg overflow-hidden shadow-lg group-hover:shadow-xl transition-all duration-300"
                style={{ height: `${barHeight}%` }}
              >
                {selectedAccount === 'all' && selectedChartType === 'cash' ? (
                  <>
                    {/* Cash */}
                    <div 
                      className="transition-all duration-300"
                      style={{ 
                        backgroundColor: currentTheme.colors.primary,
                        height: `${data.cash / data.value * 100}%` 
                      }}
                    ></div>
                    {/* Investments */}
                    <div 
                      className="transition-all duration-300"
                      style={{ 
                        backgroundColor: currentTheme.colors.accent,
                        height: `${data.investments / data.value * 100}%` 
                      }}
                    ></div>
                    {/* Credit */}
                    <div 
                      className="transition-all duration-300"
                      style={{ 
                        backgroundColor: currentTheme.colors.secondary,
                        height: `${data.credit / data.value * 100}%` 
                      }}
                    ></div>
                    {/* Loans */}
                    <div 
                      className="transition-all duration-300"
                      style={{ 
                        backgroundColor: currentTheme.colors.success,
                        height: `${data.loans / data.value * 100}%` 
                      }}
                    ></div>
                  </>
                ) : (
                  /* Single account type bar */
                  <div 
                    className="transition-all duration-300"
                    style={{ 
                      backgroundColor: currentTheme.colors.primary,
                      height: '100%' 
                    }}
                  ></div>
                )}
              </div>
              
              {/* Hover Tooltip */}
              <div className={`absolute -top-12 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-200 ${
                darkMode ? 'bg-slate-800 text-white' : 'bg-white text-slate-800'
              } px-3 py-2 rounded-lg shadow-xl border ${
                darkMode ? 'border-slate-600' : 'border-slate-200'
              } text-xs font-medium whitespace-nowrap z-20 pointer-events-none`}>
                ${data.originalBalance?.toLocaleString(undefined, { maximumFractionDigits: 0 })}
                {data.isMockData && (
                  <div className="text-xs opacity-75 mt-1">Demo Data</div>
                )}
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );

  const renderLineAreaChart = () => {
    const CustomTooltip = ({ active, payload, label }) => {
      if (active && payload && payload.length) {
        const data = payload[0].payload;
        return (
          <div className={`${
            darkMode ? 'bg-slate-800 text-white border-slate-600' : 'bg-white text-slate-800 border-slate-200'
          } px-3 py-2 rounded-lg shadow-xl border text-sm font-medium`}>
            <p className="font-semibold">{label}</p>
            <p className="text-green-600">
              ${data.originalBalance?.toLocaleString(undefined, { maximumFractionDigits: 0 })}
            </p>
            {data.isMockData && (
              <p className="text-xs opacity-75 mt-1">Demo Data</p>
            )}
          </div>
        );
      }
      return null;
    };

    const CustomDot = (props) => {
      const { cx, cy } = props;
      return (
        <circle
          cx={cx}
          cy={cy}
          r={6}
          fill={currentTheme.colors.primary}
          stroke="#ffffff"
          strokeWidth={1}
          className="hover:r-6 transition-all duration-200 cursor-pointer"
          style={{ filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' }}
        />
      );
    };

    const ChartComponent = chartView === 'area' ? AreaChart : LineChart;

    return (
      <div className="h-64 w-full relative">
        <ResponsiveContainer width="100%" height="100%">
          <ChartComponent
            data={processedData}
            margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
          >
            <CartesianGrid 
              strokeDasharray="3 3" 
              stroke={darkMode ? '#475569' : '#e2e8f0'} 
              opacity={0.3}
            />
            {/* <XAxis 
              dataKey="month" 
              axisLine={false}
              tickLine={false}
              tick={{
                fontSize: 12,
                fill: darkMode ? '#94a3b8' : '#64748b',
                fontWeight: 500
              }}
            /> */}
            <YAxis 
              domain={[0, maxValue]}
              axisLine={false}
              tickLine={false}
              tick={false} // Hide default ticks (we use custom Y-axis labels elsewhere)
            />
            <Tooltip 
              content={<CustomTooltip />}
              cursor={{ stroke: currentTheme.colors.primary, strokeWidth: 1 }}
            />

            {chartView === 'area' ? (
              <Area
                type="monotone"
                dataKey="originalBalance"
                stroke={currentTheme.colors.primary}
                strokeWidth={4.5}
                fill={currentTheme.colors.primary}
                fillOpacity={0.1}
                dot={<CustomDot />}
                activeDot={{ 
                  r: 6, 
                  fill: currentTheme.colors.primary,
                  stroke: '#ffffff',
                  strokeWidth: 2
                }}
              />
            ) : (
              <Line
                type="monotone"
                dataKey="originalBalance"
                stroke={currentTheme.colors.primary}
                strokeWidth={4}
                dot={<CustomDot />}
                activeDot={{ 
                  r: 6, 
                  fill: currentTheme.colors.primary,
                  stroke: '#ffffff',
                  strokeWidth: 2
                }}
              />
            )}
          </ChartComponent>
        </ResponsiveContainer>
      </div>
    );
  };

  return (
    <div className={`${
      darkMode 
        ? 'bg-gradient-to-br from-slate-800/50 to-slate-700/30' 
        : 'bg-gradient-to-br from-white to-green-50/10'
    } rounded-2xl p-6 backdrop-blur-sm border ${
      darkMode ? 'border-slate-700/50' : 'border-green-200/50'
    } shadow-xl hover:shadow-2xl transition-all duration-300`}>
      
      {/* Header */}
      <div className="flex justify-between items-start mb-6">
        <div>
          <h2 className={`text-2xl font-bold ${darkMode ? 'text-white' : 'text-slate-800'} mb-2`}>
            {(() => {
              const typeLabels = {
                'cash': 'Cash Accounts',
                'creditCard': 'Credit Cards', 
                'loan': 'Loans',
                'investment': 'Investments',
                'liability': 'Total Liabilities',
                'networth': 'Net Worth'
              };
              return typeLabels[accountType] || 'Portfolio';
            })()}
          </h2>
          <p className={`text-sm ${darkMode ? 'text-slate-400' : 'text-slate-600'}`}>
            {selectedAccount === 'all' 
              ? `Total ${accountType} balance over time` 
              : accounts?.find(acc => (acc.id || acc.accountId) === selectedAccount)?.financialInstName || 'Selected account'
            }
            {hasNoAccounts && (
              <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                Demo Data
              </span>
            )}
          </p>
        </div>
        
        {/* Legend - Only show for all accounts and cash type */}
        {selectedAccount === 'all' && selectedChartType === 'cash' && (
          <div className="flex flex-wrap gap-4 text-sm">
            {[
              { label: 'Cash', color: currentTheme.colors.primary },
              { label: 'Investments', color: currentTheme.colors.accent },
              { label: 'Credit', color: currentTheme.colors.secondary },
              { label: 'Loans', color: currentTheme.colors.success }
            ].map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div className={`w-3 h-3 rounded-full`} style={{ backgroundColor: item.color }}></div>
                <span className={`${darkMode ? 'text-slate-300' : 'text-slate-700'} font-medium`}>
                  {item.label}
                </span>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Chart Area */}
      <div className="relative h-64">
        {/* Grid Lines - Show for all chart types */}
        <div className="absolute inset-0 flex flex-col justify-between" style={{ marginLeft: '60px' }}>
          {yAxisTicks.map((tick, i) => (
            <div key={i} className={`border-t ${darkMode ? 'border-slate-700/30' : 'border-slate-200/50'}`}></div>
          ))}
        </div>

        {/* Chart */}
        {renderChart()}

        {/* Y-axis Labels - Show for all chart types with consistent formatting */}
        <div className="absolute left-0 top-0 h-full flex flex-col justify-between py-4 w-14">
          {yAxisTicks.map((tick, i) => (
            <span key={i} className={`text-xs ${darkMode ? 'text-slate-500' : 'text-slate-400'} text-right pr-2`} style={{ fontSize: '12px', fontWeight: 500 }}>
              {formatYAxisValue(tick)}
            </span>
          ))}
        </div>

        {/* X-axis Labels - Show for all chart types with consistent styling */}
        <div className="absolute bottom-0 left-0 right-0 flex justify-between items-center" style={{ marginLeft: '60px', paddingRight: '30px' }}>
          {processedData.map((data, index) => (
            <span 
              key={index} 
              className={`text-xs ${darkMode ? 'text-slate-500' : 'text-slate-400'} font-medium`}
              style={{ fontSize: '12px', fontWeight: 500 }}
            >
              {data.month}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PortfolioChart;