import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faArrowDown, 
  faChartLine,
  faChartBar,
  faChartArea
} from "@fortawesome/free-solid-svg-icons";
import { 
  setChartType, 
  setTimePeriod,
  setChartView,
  selectChartType,
  selectTimePeriod,
  fetchAccountData    
} from '../../../../../../logic/redux/accountChartSlice';
// import { fetchDeltaData, fetchAccountTypeDeltaData } from '../../../../../../logic/redux/deltaSlice';


import ChartWithNoDataOverlay from './ChartWithNoDataOverlay'; // <-- Use this instead of PortfolioChart
import AccountConnectionProviderModal from './AccountConnectionProviderModal';

// ...existing imports...
const PortfolioChartControlView = ({
  darkMode = false,
  currentTheme = { 
    colors: { 
      primary: '#8bc34a', 
      accent: '#aed581', 
      secondary: '#689f38',
      cardBg: '#ffffff',
      text: '#1f2937',
      border: '#e5e7eb'
    } 
  },
  accounts = [],
  userId
}) => {
  const dispatch = useDispatch();
  const [showConnectionModal, setShowConnectionModal] = useState(false);

  // Selectors
  const selectedChartType = useSelector(state => {
    try {
      return selectChartType ? selectChartType(state) : state?.accountChart?.selectedChartType || 'cash';
    } catch (error) {
      console.warn('Error selecting chart type:', error);
      return 'cash';
    }
  });

  const selectedTimePeriod = useSelector(state => {
    try {
      return selectTimePeriod ? selectTimePeriod(state) : state?.accountChart?.selectedTimePeriod || 'yearly';
    } catch (error) {
      console.warn('Error selecting time period:', error);
      return 'yearly';
    }
  });

  const selectedChartView = useSelector(state => {
    try {
      return state?.accountChart?.selectedChartView || 'bar';
    } catch (error) {
      console.warn('Error selecting chart view:', error);
      return 'bar';
    }
  });

  // Get no data state
  const hasNoAccounts = useSelector(state => state.accountChart.hasNoAccounts);
  const hasNoDataForCurrentType = useSelector(state => state.accountChart.hasNoDataForCurrentType);
  const shouldShowMockData = hasNoAccounts || hasNoDataForCurrentType;
// Sync status selectors
  
 
  
  React.useEffect(() => {
    if (userId && dispatch) {
      const payload = {
        chartType: selectedChartType,
        timePeriod: selectedTimePeriod 
      };
      // dispatch(fetchAccountData(payload));
      // dispatch(fetchAccountTypeDeltaData({ timePeriod: selectedTimePeriod }));
    }
  }, [userId, dispatch, selectedChartType, selectedTimePeriod]);

  const colors = currentTheme?.colors || {
    primary: '#8bc34a',
    accent: '#aed581', 
    secondary: '#689f38',
    cardBg: '#ffffff',
    text: '#1f2937',
    border: '#e5e7eb'
  };

  // Event handlers...
  const handleChartTypeChange = (e) => {
    try {
      if (dispatch && typeof setChartType === 'function') {
        const newChartType = e.target.value;
        dispatch(setChartType(newChartType));
        const payload = {
          chartType: newChartType || 'cash',
          timePeriod: selectedTimePeriod || 'yearly'
        };
        dispatch(fetchAccountData(payload));
      }
    } catch (error) {
      console.error('Error changing chart type:', error);
    }
  };

  const handleTimePeriodChange = (e) => {
    try {
      if (dispatch && typeof setTimePeriod === 'function') {
        const newTimePeriod = e.target.value;
        dispatch(setTimePeriod(newTimePeriod));
        dispatch(fetchAccountData({
          chartType: selectedChartType,
          timePeriod: newTimePeriod
        }));
        // dispatch(fetchDeltaData({ timePeriod: newTimePeriod }));
        // dispatch(fetchAccountTypeDeltaData({ timePeriod: newTimePeriod }));
      }
    } catch (error) {
      console.error('Error changing time period:', error);
    }
  };

  const handleChartViewChange = (chartView) => {
    try {
      if (dispatch && typeof setChartView === 'function') {
        dispatch(setChartView(chartView));
      }
    } catch (error) {
      console.error('Error changing chart view:', error);
    }
  };

 




  return (
    <div className="mb-8">
      {/* Chart Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0">
        <div className="flex items-center space-x-4">
          {/* Account Type Dropdown */}
          <div className="relative">
            <select
              value={selectedChartType}
              onChange={handleChartTypeChange}
              className={`border rounded-lg px-4 py-2 pr-8 appearance-none cursor-pointer focus:outline-none focus:ring-2 transition-all duration-200 ${
                darkMode
                  ? 'border-gray-600 bg-gray-700 text-white hover:bg-gray-600'
                  : 'border-gray-300 bg-white text-gray-800 hover:bg-gray-50'
              }`}
              style={{
                fontFamily: 'Roboto, sans-serif'
              }}
            >
              <option value="cash">Cash Accounts</option>
              <option value="creditCard">Credit Cards</option>
              <option value="loan">Loans</option>
              <option value="investment">Investments</option>
              <option value="liability">Total Liabilities</option>
              <option value="networth">Net Worth</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <FontAwesomeIcon icon={faArrowDown} className={`text-sm ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} />
            </div>
          </div>
          {/* Time Period Dropdown */}
          <div className="relative">
            <select
              value={selectedTimePeriod}
              onChange={handleTimePeriodChange}
              disabled={shouldShowMockData} // <-- Disable when no data
              className={`border rounded-lg px-4 py-2 pr-8 appearance-none cursor-pointer focus:outline-none focus:ring-2 transition-all duration-200 ${shouldShowMockData ? 'opacity-50 cursor-not-allowed' : ''}`}
              style={{
                backgroundColor: colors.cardBg,
                color: colors.text,
                borderColor: colors.border,
              }}
              onFocus={(e) => {
                e.target.style.borderColor = colors.primary;
                e.target.style.boxShadow = `0 0 0 2px ${colors.primary}33`;
              }}
              onBlur={(e) => {
                e.target.style.borderColor = colors.border;
                e.target.style.boxShadow = 'none';
              }}
            >
              <option value="one-month">1 Month</option>
              <option value="three-month">3 Months</option>
              <option value="half-year">6 Months</option>
              <option value="yearly">1 Year</option>
              <option value="ytd">Year to Date</option>
              <option value="quarterly-aggregate">Quarterly</option>
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
              <FontAwesomeIcon icon={faArrowDown} className={`text-sm ${darkMode ? 'text-slate-400' : 'text-slate-600'}`} />
            </div>
          </div>

          {/* Chart View Buttons */}
          <div className={`flex items-center space-x-1 p-1 rounded-lg`}
            style={{ backgroundColor: darkMode ? colors.cardBg : `${colors.primary}10` }}>
            {[ { type: 'area', icon: faChartArea, label: 'Area' },
              { type: 'bar', icon: faChartBar, label: 'Bar' },
              { type: 'line', icon: faChartLine, label: 'Line' },
             
            ].map((chart) => {
              const isActive = selectedChartView === chart.type;
              return (
                <button
                  key={chart.type}
                  onClick={() => handleChartViewChange(chart.type)}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-all duration-200 ${
                    isActive
                      ? 'text-black shadow-lg'
                      : (darkMode 
                        ? 'text-slate-400 hover:text-white' 
                        : 'text-slate-600 hover:text-slate-800')
                  }`}
                  style={isActive ? { backgroundColor: colors.primary } : {}}
                  onMouseEnter={(e) => {
                    if (!isActive) {
                      e.target.style.backgroundColor = darkMode ? colors.cardBg : `${colors.primary}20`;
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isActive) {
                      e.target.style.backgroundColor = '';
                    }
                  }}
                  title={`${chart.label} Chart`}
                >
                  <FontAwesomeIcon icon={chart.icon} />
                </button>
              );
            })}
          </div>
        </div>

        {/* Last Synced Info (Optional) */}
        {/* <div className="text-xs text-gray-500 mt-2 sm:mt-0">
          Last synced: {formatSyncTime(new Date())}
        </div> */}
       
      
      </div>

      {/* Chart Rendering Section */}
      <div>
        <ChartWithNoDataOverlay
          darkMode={darkMode}
          currentTheme={currentTheme}
          accounts={accounts}
          selectedTimePeriod={selectedTimePeriod}
          selectedChartType={selectedChartType}
          accountType={selectedChartType}
          chartView={selectedChartView}
          userId={userId}
          onGetStarted={() => setShowConnectionModal(true)}
        />
        {showConnectionModal && (
          <AccountConnectionProviderModal
            darkMode={darkMode}
            currentTheme={currentTheme}
            setShowConnectionModal={setShowConnectionModal}
          />
        )}
      </div>
    </div>
  );
};

export default PortfolioChartControlView;