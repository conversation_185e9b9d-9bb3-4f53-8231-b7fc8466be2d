import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faExclamationTriangle, 
  faSpinner, 
  faSync,
  faCheckCircle,
  faExchangeAlt
} from '@fortawesome/free-solid-svg-icons';
import { PlaidLink } from 'react-plaid-link';
import { useReauth } from './useReauth';

const ReauthButton = ({ 
  accountId, 
  syncErrorFlags, 
  syncErrors, 
  onSyncAccount,
  darkMode,
  currentTheme 
}) => {
  const { 
    reauthStatus, 
    reauthErrors, 
    reauthLinkTokens, 
    startReauth, 
    completeReauth,
    clearReauth 
  } = useReauth();

  const accountReauthStatus = reauthStatus[accountId] || 'idle';
  const accountReauthError = reauthErrors[accountId];
  const accountLinkToken = reauthLinkTokens[accountId];
  const hasSyncError = syncErrorFlags[accountId];
  const syncError = syncErrors[accountId];

  // Clear success status after 2 seconds
  React.useEffect(() => {
    if (accountReauthStatus === 'success') {
      const timer = setTimeout(() => {
        clearReauth(accountId);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [accountReauthStatus, accountId, clearReauth]);

  const handleReauthClick = () => {
    console.log('Re-auth button clicked for account:', accountId);
    startReauth(accountId);
  };

  const handlePlaidSuccess = (publicToken, metadata) => {
    console.log('Plaid Link success for account:', accountId, metadata);
    completeReauth(publicToken, accountId);
  };

  const handlePlaidExit = (err, metadata) => {
    console.log('Plaid Link exit for account:', accountId, err, metadata);
    if (err) {
      console.error('Plaid Link error:', err);
    }
    // Reset status if user exits without completing
    clearReauth(accountId);
  };

  // Show sync button if no sync error and re-auth is not in progress/successful
  const showSyncButton = !hasSyncError && 
    !['creating-token', 'awaiting-link', 'exchanging', 'success'].includes(accountReauthStatus);

  // Show update button if there's a sync error and re-auth hasn't started
  const showUpdateButton = hasSyncError && accountReauthStatus === 'idle';

  const getButtonStyle = (type) => {
    const baseStyle = {
      borderRadius: currentTheme.layout?.borderRadius === 'square' ? '6px' : 
                    currentTheme.layout?.borderRadius === 'pill' ? '50px' : '8px',
    };

    switch (type) {
      case 'error':
        return {
          ...baseStyle,
          backgroundColor: '#fee2e2',
          color: '#dc2626',
          border: '1px solid #fecaca'
        };
      case 'success':
        return {
          ...baseStyle,
          backgroundColor: '#dcfce7',
          color: '#16a34a',
          border: '1px solid #bbf7d0'
        };
      case 'warning':
        return {
          ...baseStyle,
          backgroundColor: '#fef3c7',
          color: '#d97706',
          border: '1px solid #fde68a'
        };
      case 'sync':
        return {
          ...baseStyle,
          backgroundColor: darkMode ? '#1e293b' : '#f8fafc',
          color: darkMode ? '#cbd5e1' : '#475569',
          border: `1px solid ${darkMode ? '#475569' : '#e2e8f0'}`
        };
      default:
        return baseStyle;
    }
  };

  return (
    <div className="flex items-center space-x-2">
      {/* Sync Button */}
      {showSyncButton && (
        <button
          onClick={() => onSyncAccount(accountId)}
          className="p-1.5 rounded-lg transition-all duration-200 hover:scale-105"
          style={getButtonStyle('sync')}
          title="Sync Account"
        >
          <FontAwesomeIcon icon={faSync} className="text-sm" />
        </button>
      )}

      {/* Update/Re-auth Button */}
      {showUpdateButton && (
        <button
          className="p-1.5 rounded-lg transition-all duration-200 hover:scale-105"
          style={getButtonStyle('error')}
          title={`Re-authenticate: ${syncError || 'Sync error'}`}
          onClick={handleReauthClick}
        >
          <FontAwesomeIcon icon={faExclamationTriangle} className="text-sm" />
        </button>
      )}

      {/* Re-auth Progress States */}
      {accountReauthStatus === 'creating-token' && (
        <button
          className="p-1.5 rounded-lg transition-all duration-200"
          style={getButtonStyle('warning')}
          title="Creating re-authentication token..."
          disabled
        >
          <FontAwesomeIcon icon={faSpinner} className="text-sm animate-spin" />
        </button>
      )}

      {accountReauthStatus === 'exchanging' && (
        <button
          className="p-1.5 rounded-lg transition-all duration-200"
          style={getButtonStyle('warning')}
          title="Completing re-authentication..."
          disabled
        >
          <FontAwesomeIcon icon={faExchangeAlt} className="text-sm animate-pulse" />
        </button>
      )}

      {/* Success State */}
      {accountReauthStatus === 'success' && (
        <button
          className="p-1.5 rounded-lg transition-all duration-200"
          style={getButtonStyle('success')}
          title="Re-authentication successful!"
          disabled
        >
          <FontAwesomeIcon icon={faCheckCircle} className="text-sm" />
        </button>
      )}

      {/* Plaid Link Component */}
      {accountReauthStatus === 'awaiting-link' && accountLinkToken && (
        <PlaidLink
          token={accountLinkToken}
          onSuccess={handlePlaidSuccess}
          onExit={handlePlaidExit}
        >
          {({ open, ready, error }) => (
            <div>
              {ready && !error && (
                <button
                  onClick={() => open()}
                  className="p-1.5 rounded-lg transition-all duration-200 animate-pulse"
                  style={getButtonStyle('warning')}
                  title="Click to complete re-authentication"
                >
                  <FontAwesomeIcon icon={faExchangeAlt} className="text-sm" />
                </button>
              )}
              {error && (
                <button
                  className="p-1.5 rounded-lg"
                  style={getButtonStyle('error')}
                  title={`Plaid Link error: ${error.error_message}`}
                  onClick={handleReauthClick}
                >
                  <FontAwesomeIcon icon={faExclamationTriangle} className="text-sm" />
                </button>
              )}
            </div>
          )}
        </PlaidLink>
      )}

      {/* Re-auth Error State */}
      {accountReauthError && (
        <button
          className="p-1.5 rounded-lg transition-all duration-200"
          style={getButtonStyle('error')}
          title={`Re-auth error: ${accountReauthError}`}
          onClick={handleReauthClick}
        >
          <FontAwesomeIcon icon={faExclamationTriangle} className="text-sm" />
        </button>
      )}
    </div>
  );
};

export default ReauthButton;