import { useMemo, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import StatCard from './StatCard';
import { 
  fetchAccountTypeDeltaData,
  selectCardAccountTypes,
  selectAccountTypeDataByPeriod,
  selectDataType,
  selectDeltaData
} from '../../../../../../logic/redux/deltaSlice';
import { selectTimePeriod } from '../../../../../../logic/redux/accountChartSlice';
import {
  faSackDollar,
  faChartLine,
  faCreditCard,
  faCoins
} from "@fortawesome/free-solid-svg-icons";

const CARD_CONFIG = [
  {
    key: 'cash',
    label: 'Cash',
    variations: ['depository', 'cash', 'checking', 'savings', 'bank', 'cash_management'],
    icon: faCoins
  },
  {
    key: 'credit',
    label: 'Credit',
    variations: ['credit', 'creditcard', 'credit_card', 'credit card'],
    icon: faCreditCard
  },
  {
    key: 'investment',
    label: 'Investment',
    variations: ['investment', 'investmenttaxdeferred', 'roth'],
    icon: faChartLine
  },
  {
    key: 'loan',
    label: 'Loan',
    variations: ['loan', 'mortgage', 'liability'],
    icon: faSackDollar
  }
];

const StatCardGroupView = ({ darkMode, currentTheme, accounts = [] }) => {
  const dispatch = useDispatch();
  
  // Use the new selector specifically for card data (always one-month)
  const cardAccountTypes = useSelector(selectCardAccountTypes);
  const oneMonthData = useSelector(state => selectAccountTypeDataByPeriod(state, 'one-month'));
  
  // Get the selected time period from the dropdown (but don't use it for cards)
  const selectedTimePeriod = useSelector(selectTimePeriod);

  // Always fetch one-month data for cards, regardless of dropdown selection
  useEffect(() => {
    // Only fetch if we don't have recent data
    const shouldFetch = !oneMonthData.data.length || oneMonthData.error;
    
    if (shouldFetch) {
      console.log('🔄 [StatCardGroupView] Fetching one-month data for cards');
      dispatch(fetchAccountTypeDeltaData({ timePeriod: "one-month" }));
    }
  }, [dispatch, oneMonthData.data.length, oneMonthData.error]);

  // Map API accountTypes to a lookup object for fast access
  const accountTypeMap = useMemo(() => {
    const map = {};
    if (Array.isArray(cardAccountTypes)) {
      cardAccountTypes.forEach((item) => {
        const type = (item.accountType || '').toLowerCase();
        map[type] = item;
      });
    }
    console.log('📊 [StatCardGroupView] Account type map:', map);
    return map;
  }, [cardAccountTypes]);

  // Helper to get card data for each type
  const getCardData = (config) => {
    console.log(`📊 [StatCardGroupView] Getting card data for ${config.key}:`, config.variations);
    
    // For investment, sum all investment types
    if (config.key === 'investment') {
      let totalBalance = 0, totalDelta = 0, totalPast = 0, totalCount = 0;
      config.variations.forEach((variation) => {
        const data = accountTypeMap[variation];
        if (data) {
          console.log(`📊 [StatCardGroupView] Found ${variation} data:`, data);
          totalBalance += data.totalCurrentBalance || data.currentBalance || 0;
          totalDelta += data.totalDeltaAmount || data.deltaAmount || 0;
          totalPast += data.totalPastBalance || data.pastBalance || 0;
          totalCount += data.accountCount || 0;
        }
      });
      let percent = totalPast !== 0 ? (totalDelta / totalPast) * 100 : 0;
      if (!Number.isFinite(percent)) percent = 0;
      
      const result = {
        value: totalBalance,
        change: percent,
        icon: config.icon,
        accountCount: totalCount
      };
      console.log(`📊 [StatCardGroupView] Investment card result:`, result);
      return result;
    }
    
    // For other types, use the first matching variation
    for (const variation of config.variations) {
      const data = accountTypeMap[variation];
      if (data) {
        console.log(`📊 [StatCardGroupView] Found ${variation} data for ${config.key}:`, data);
        
        const currentBalance = data.totalCurrentBalance || data.currentBalance || 0;
        const pastBalance = data.totalPastBalance || data.pastBalance || 0;
        const deltaAmount = data.totalDeltaAmount || data.deltaAmount || 0;
        
        let percent = pastBalance !== 0 ? (deltaAmount / pastBalance) * 100 : 0;
        if (!Number.isFinite(percent)) percent = 0;
        
        const result = {
          value: currentBalance,
          change: percent,
          icon: config.icon,
          accountCount: data.accountCount || 0
        };
        console.log(`📊 [StatCardGroupView] ${config.key} card result:`, result);
        return result;
      }
    }
    // Fallback: show zero if no data
    console.log(`📊 [StatCardGroupView] No data found for ${config.key}, using fallback`);
    return { value: 0, change: 0, icon: config.icon, accountCount: 0 };
  };

  // Loading state
  if (oneMonthData.loading) {
    console.log('🔄 [StatCardGroupView] Loading state');
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {CARD_CONFIG.map((card) => (
          <StatCard
            key={card.key}
            label={card.label}
            value={0}
            change={0}
            darkMode={darkMode}
            icon={card.icon}
            currentTheme={currentTheme}
            loading={true}
          />
        ))}
      </div>
    );
  }

  // Error state
  if (oneMonthData.error) {
    console.error('❌ [StatCardGroupView] Error loading account type data:', oneMonthData.error);
    return (
      <div className="text-red-500 p-4 bg-red-50 rounded-lg mb-8">
        <p>Error loading account type data: {oneMonthData.error.message}</p>
        <button 
          onClick={() => dispatch(fetchAccountTypeDeltaData({ timePeriod: "one-month" }))}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Retry
        </button>
      </div>
    );
  }

  console.log('✅ [StatCardGroupView] Rendering cards with data:', {
    cardAccountTypesLength: cardAccountTypes.length,
    oneMonthDataLength: oneMonthData.data.length,
    selectedTimePeriod
  });

  // Render four cards - always showing one-month delta data
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      {/* Optional: Add a small indicator that cards show 1-month data */}
     
      {CARD_CONFIG.map((card) => {
        const data = getCardData(card);
        return (
          <StatCard
            key={card.key}
            label={card.label}
            value={data.value}
            change={data.change}
            darkMode={darkMode}
            icon={card.icon}
            currentTheme={currentTheme}
            accountCount={data.accountCount}
          />
        );
      })}
    </div>
  );
};

export default StatCardGroupView;