import Cookies from 'js-cookie';
import { jwtDecode } from "jwt-decode";

const TOKEN_COOKIE_NAME = 'pennypal_jwt_token';

// Buffer to collect events before sending
let eventBuffer = [];
const BATCH_SIZE = 10;

const API_URL = import.meta.env.VITE_API_URL;

// Function to send the collected events to the server
async function sendLogEvents() {
  if (eventBuffer.length === 0) return;
  
  const token = Cookies.get(TOKEN_COOKIE_NAME);
  
  let userSub = null;
  if (token) {
    try {
      const decodedToken = jwtDecode(token);
      userSub = decodedToken.sub;
    } catch(e) {
      console.error("Failed to decode token: ", e);
    }
  }

  // try {
  //   await fetch('http://localhost:8080/pennypal/api/v1/log/UIEvents', {
  //     method: 'POST',
  //     headers: {
  //       'Content-Type': 'application/json',
  //       'Authorization': `<PERSON><PERSON> ${token}`
  //     },
  //     credentials: 'include',
  //     body: JSON.stringify(
  //       eventBuffer.map(event => ({
  //         ...event,
  //         userSub, // Add userSub to each event
  //       })),
  //     ),
  //   });
    
  //   // Clear the buffer after successful send
  //   eventBuffer = [];
  // } catch (error) {
  //   console.error('Error sending batched events:', error);
  //   // Keep events in buffer to try again later
  // }
}

// Function to log events
export async function logEvent(componentName, eventType, metadata = {}) {
  // Add event to buffer
  eventBuffer.push({
    componentName,
    eventType,
    metadata,
    timestamp: new Date().toISOString(),
  });
  
  // Send immediately if we reach batch size
  if (eventBuffer.length >= BATCH_SIZE) {
    await sendLogEvents();
  }
}

// Handle tab close/page unload
window.addEventListener('beforeunload', async () => {
  if (eventBuffer.length > 0) {
    // SendBeacon for more reliable delivery during page unload
    if (navigator.sendBeacon) {
      const token = Cookies.get(TOKEN_COOKIE_NAME);
      let userSub = null;
      
      if (token) {
        try {
          const decodedToken = jwtDecode(token);
          userSub = decodedToken.sub;
        } catch(e) {
          console.error("Failed to decode token: ", e);
        }
      }
      
      const blob = new Blob([JSON.stringify(
        eventBuffer.map(event => ({
          ...event,
          userSub,
        })),
      )], { type: 'application/json' });
      console.log("Sending beacon with events:", blob);
      
      navigator.sendBeacon(`${API_URL}/pennypal/api/v1/log/UIEvents`, blob);
    } else {
      await sendLogEvents();
    }
    // Clear the buffer
    eventBuffer = [];
    }
  });
  
// Add logoff functionality when available
  
// Flush events every minute
setInterval(async () => {
  if (eventBuffer.length > 0) {
    await sendLogEvents();
  }
}, 60000); // Flush every minute

// Below code for single event logging - commented
// export async function logEvent(componentName, eventType, metadata = {}) {
//     const token = Cookies.get(TOKEN_COOKIE_NAME);
    
//     let userSub = null;
//     if (token) {
//       try {
//         const decodedToken = jwtDecode(token);
//         userSub = decodedToken.sub;
//       } catch(e) {
//         console.error("Failed to decode token: ", e);
//       }
//     }

//     try {
//       fetch('http://localhost:8080/pennypal/api/v1/log/UIEvent', {
//         method: 'POST',
//         headers: {
//           'Content-Type': 'application/json',
//         },
//         body: JSON.stringify({
//           userSub,
//           componentName,
//           eventType,
//           metadata,
//           timestamp: new Date().toISOString(),
//         }),
//       });
//     } catch (error) {
//       console.error('Error logging event:', error);
//     }
// }