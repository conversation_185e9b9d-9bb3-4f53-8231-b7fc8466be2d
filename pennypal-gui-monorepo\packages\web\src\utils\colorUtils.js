/**
 * Utility functions for handling color conversions and fixes for canvas rendering
 */

/**
 * Convert modern CSS color functions to hex colors for canvas compatibility
 * @param {string} colorString - The color string to convert
 * @param {boolean} darkMode - Whether dark mode is enabled
 * @returns {string} - Hex color string
 */
export const convertToHexColor = (colorString, darkMode = false) => {
  if (!colorString || colorString === 'none' || colorString === 'transparent') {
    return colorString;
  }

  // If it's already a hex color, return as is
  if (colorString.startsWith('#')) {
    return colorString;
  }

  // If it's an rgb/rgba color, return as is (supported by canvas)
  if (colorString.startsWith('rgb')) {
    return colorString;
  }

  // Handle oklch colors - convert to appropriate hex
  if (colorString.includes('oklch')) {
    // Extract lightness, chroma, and hue values if possible
    // For now, use fallback colors based on common patterns
    if (colorString.includes('0.9')) return darkMode ? '#f3f4f6' : '#111827'; // Very light/dark
    if (colorString.includes('0.8')) return darkMode ? '#e5e7eb' : '#1f2937';
    if (colorString.includes('0.7')) return darkMode ? '#d1d5db' : '#374151';
    if (colorString.includes('0.6')) return darkMode ? '#9ca3af' : '#4b5563';
    if (colorString.includes('0.5')) return darkMode ? '#6b7280' : '#6b7280';
    if (colorString.includes('0.4')) return darkMode ? '#4b5563' : '#9ca3af';
    if (colorString.includes('0.3')) return darkMode ? '#374151' : '#d1d5db';
    if (colorString.includes('0.2')) return darkMode ? '#1f2937' : '#e5e7eb';
    if (colorString.includes('0.1')) return darkMode ? '#111827' : '#f3f4f6';
    
    // Default fallback
    return darkMode ? '#f3f4f6' : '#111827';
  }

  // Handle other modern color functions
  if (colorString.includes('color(') || colorString.includes('lab(') || colorString.includes('lch(')) {
    return darkMode ? '#f3f4f6' : '#111827';
  }

  // Return original if no conversion needed
  return colorString;
};

/**
 * Comprehensive color mapping for Tailwind classes
 */
export const getTailwindColorMap = (darkMode = false) => ({
  // Text colors
  'text-gray-900': darkMode ? '#f3f4f6' : '#111827',
  'text-gray-800': darkMode ? '#f9fafb' : '#1f2937',
  'text-gray-700': darkMode ? '#d1d5db' : '#374151',
  'text-gray-600': darkMode ? '#9ca3af' : '#4b5563',
  'text-gray-500': '#6b7280',
  'text-gray-400': darkMode ? '#4b5563' : '#9ca3af',
  'text-gray-300': darkMode ? '#374151' : '#d1d5db',
  'text-gray-200': darkMode ? '#1f2937' : '#e5e7eb',
  'text-gray-100': darkMode ? '#111827' : '#f3f4f6',
  'text-white': '#ffffff',
  'text-black': '#000000',
  'text-red-600': '#dc2626',
  'text-red-500': '#ef4444',
  'text-green-600': '#16a34a',
  'text-green-500': '#22c55e',
  'text-blue-600': '#2563eb',
  'text-blue-500': '#3b82f6',
  'text-blue-400': '#60a5fa',
  'text-lime-600': '#65a30d',
  'text-lime-400': '#a3e635',

  // Background colors
  'bg-white': '#ffffff',
  'bg-gray-900': '#111827',
  'bg-gray-800': '#1f2937',
  'bg-gray-700': '#374151',
  'bg-gray-600': '#4b5563',
  'bg-gray-500': '#6b7280',
  'bg-gray-400': '#9ca3af',
  'bg-gray-300': '#d1d5db',
  'bg-gray-200': '#e5e7eb',
  'bg-gray-100': '#f3f4f6',
  'bg-gray-50': '#f9fafb',
  'bg-red-100': '#fee2e2',
  'bg-red-500': '#ef4444',
  'bg-green-100': '#dcfce7',
  'bg-green-500': '#22c55e',
  'bg-blue-100': '#dbeafe',
  'bg-blue-500': '#3b82f6',

  // Fill colors for SVG
  'fill-gray-900': darkMode ? '#f3f4f6' : '#111827',
  'fill-gray-800': darkMode ? '#f9fafb' : '#1f2937',
  'fill-gray-700': darkMode ? '#d1d5db' : '#374151',
  'fill-gray-600': darkMode ? '#9ca3af' : '#4b5563',
  'fill-gray-500': '#6b7280',
  'fill-gray-400': darkMode ? '#4b5563' : '#9ca3af',
  'fill-gray-300': darkMode ? '#374151' : '#d1d5db',
  'fill-gray-200': darkMode ? '#1f2937' : '#e5e7eb',
  'fill-gray-100': darkMode ? '#111827' : '#f3f4f6',
  'fill-white': '#ffffff',
  'fill-black': '#000000',
  'fill-teal-500': '#14b8a6',
  'fill-teal-400': '#2dd4bf',
  'fill-orange-400': '#fb923c',
  'fill-orange-300': '#fdba74',
  'fill-green-400': '#4ade80',
  'fill-green-300': '#86efac',

  // Stroke colors for SVG
  'stroke-gray-600': darkMode ? '#9ca3af' : '#4b5563',
  'stroke-gray-400': darkMode ? '#4b5563' : '#9ca3af',
  'stroke-gray-300': darkMode ? '#374151' : '#d1d5db',
  'stroke-black': '#000000',
  'stroke-white': '#ffffff',

  // Border colors
  'border-gray-300': darkMode ? '#374151' : '#d1d5db',
  'border-gray-700': darkMode ? '#d1d5db' : '#374151',
  'border-gray-600': darkMode ? '#9ca3af' : '#4b5563',
  'border-gray-200': darkMode ? '#1f2937' : '#e5e7eb',
});

/**
 * Fix colors in an element for canvas rendering compatibility
 * @param {HTMLElement} element - The element to fix colors for
 * @param {boolean} darkMode - Whether dark mode is enabled
 */
export const fixColorsForCanvas = (element, darkMode = false) => {
  const allElements = element.querySelectorAll('*');
  const colorMap = getTailwindColorMap(darkMode);
  
  allElements.forEach((el) => {
    try {
      const computedStyle = getComputedStyle(el);

      // Handle computed style colors
      const color = computedStyle.color;
      const bgColor = computedStyle.backgroundColor;
      const fill = computedStyle.fill;
      const stroke = computedStyle.stroke;
      const borderColor = computedStyle.borderColor;

    // Fix unsupported color functions
    if (color && (color.includes('oklch') || color.includes('color(') || color.includes('lab(') || color.includes('lch('))) {
      el.style.color = convertToHexColor(color, darkMode);
    }
    if (bgColor && (bgColor.includes('oklch') || bgColor.includes('color(') || bgColor.includes('lab(') || bgColor.includes('lch('))) {
      el.style.backgroundColor = convertToHexColor(bgColor, darkMode);
    }
    if (fill && fill !== 'none' && (fill.includes('oklch') || fill.includes('color(') || fill.includes('lab(') || fill.includes('lch('))) {
      el.style.fill = convertToHexColor(fill, darkMode);
    }
    if (stroke && stroke !== 'none' && (stroke.includes('oklch') || stroke.includes('color(') || stroke.includes('lab(') || stroke.includes('lch('))) {
      el.style.stroke = convertToHexColor(stroke, darkMode);
    }
    if (borderColor && (borderColor.includes('oklch') || borderColor.includes('color(') || borderColor.includes('lab(') || borderColor.includes('lch('))) {
      el.style.borderColor = convertToHexColor(borderColor, darkMode);
    }

    // Handle inline style colors
    if (el.style.color && (el.style.color.includes('oklch') || el.style.color.includes('color(') || el.style.color.includes('lab(') || el.style.color.includes('lch('))) {
      el.style.color = convertToHexColor(el.style.color, darkMode);
    }
    if (el.style.backgroundColor && (el.style.backgroundColor.includes('oklch') || el.style.backgroundColor.includes('color(') || el.style.backgroundColor.includes('lab(') || el.style.backgroundColor.includes('lch('))) {
      el.style.backgroundColor = convertToHexColor(el.style.backgroundColor, darkMode);
    }
    if (el.style.fill && el.style.fill !== 'none' && (el.style.fill.includes('oklch') || el.style.fill.includes('color(') || el.style.fill.includes('lab(') || el.style.fill.includes('lch('))) {
      el.style.fill = convertToHexColor(el.style.fill, darkMode);
    }
    if (el.style.stroke && el.style.stroke !== 'none' && (el.style.stroke.includes('oklch') || el.style.stroke.includes('color(') || el.style.stroke.includes('lab(') || el.style.stroke.includes('lch('))) {
      el.style.stroke = convertToHexColor(el.style.stroke, darkMode);
    }

    // Apply Tailwind class mappings - safely handle classList
    try {
      let classList = [];
      if (el.classList && el.classList.length > 0) {
        classList = Array.from(el.classList);
      } else if (el.className) {
        // Fallback for elements that might not have classList
        let className = '';
        if (typeof el.className === 'string') {
          className = el.className;
        } else if (el.className.baseVal) {
          // SVG elements
          className = el.className.baseVal;
        } else if (el.className.toString) {
          className = el.className.toString();
        }
        classList = className.split(' ').filter(cls => cls.trim());
      }

      classList.forEach(className => {
        if (colorMap[className]) {
          if (className.startsWith('text-')) {
            el.style.color = colorMap[className];
          } else if (className.startsWith('bg-')) {
            el.style.backgroundColor = colorMap[className];
          } else if (className.startsWith('fill-')) {
            el.style.fill = colorMap[className];
          } else if (className.startsWith('stroke-')) {
            el.style.stroke = colorMap[className];
          } else if (className.startsWith('border-')) {
            el.style.borderColor = colorMap[className];
          }
        }
      });
    } catch (classError) {
      // Silently continue if there's an issue with class handling
      console.warn('Error processing element classes:', classError);
    }
  } catch (elementError) {
    // Silently continue if there's an issue with this element
    console.warn('Error processing element:', elementError);
  }
  });
};

/**
 * Debug function to log all colors in an element
 * @param {HTMLElement} element - The element to debug
 */
export const debugElementColors = (element) => {
  const allElements = element.querySelectorAll('*');
  const problematicColors = [];

  allElements.forEach((el, index) => {
    try {
      const computedStyle = getComputedStyle(el);
      const color = computedStyle.color;
      const bgColor = computedStyle.backgroundColor;
      const fill = computedStyle.fill;
      const stroke = computedStyle.stroke;

      // Check for problematic color functions
      [color, bgColor, fill, stroke].forEach((colorValue, colorIndex) => {
        if (colorValue && (colorValue.includes('oklch') || colorValue.includes('color(') || colorValue.includes('lab(') || colorValue.includes('lch('))) {
          const colorType = ['color', 'backgroundColor', 'fill', 'stroke'][colorIndex];

          // Safely get className - handle both regular elements and SVG elements
          let className = '';
          try {
            if (el.className) {
              if (typeof el.className === 'string') {
                className = el.className;
              } else if (el.className.baseVal) {
                // SVG elements have className.baseVal
                className = el.className.baseVal;
              } else if (el.className.toString) {
                className = el.className.toString();
              }
            }
          } catch (e) {
            className = '';
          }

          problematicColors.push({
            element: el.tagName + (className ? '.' + className.split(' ').join('.') : ''),
            property: colorType,
            value: colorValue,
            elementIndex: index
          });
        }
      });
    } catch (elementError) {
      // Silently continue if there's an issue with this element
      console.warn('Error processing element in debug:', elementError);
    }
  });

  if (problematicColors.length > 0) {
    console.warn('Found problematic colors that may cause canvas issues:', problematicColors);
  } else {
    console.log('No problematic colors found - canvas should work correctly');
  }

  return problematicColors;
};

/**
 * Simple, robust color fixing function as a fallback
 * @param {HTMLElement} element - The element to fix colors for
 * @param {boolean} darkMode - Whether dark mode is enabled
 */
export const simpleFixColorsForCanvas = (element, darkMode = false) => {
  try {
    const allElements = element.querySelectorAll('*');

    allElements.forEach((el) => {
      try {
        // Get computed styles safely
        const computedStyle = window.getComputedStyle(el);

        // Simple color replacements for common problematic cases
        const properties = ['color', 'backgroundColor', 'fill', 'stroke', 'borderColor'];

        properties.forEach(prop => {
          try {
            const value = computedStyle[prop];
            if (value && typeof value === 'string' &&
                (value.includes('oklch') || value.includes('color(') ||
                 value.includes('lab(') || value.includes('lch('))) {

              // Apply simple fallback colors
              if (prop === 'color' || prop === 'fill') {
                el.style[prop] = darkMode ? '#f3f4f6' : '#111827';
              } else if (prop === 'backgroundColor') {
                el.style[prop] = darkMode ? '#1f2937' : '#ffffff';
              } else if (prop === 'stroke' || prop === 'borderColor') {
                el.style[prop] = darkMode ? '#9ca3af' : '#4b5563';
              }
            }
          } catch (propError) {
            // Continue with next property
          }
        });
      } catch (elementError) {
        // Continue with next element
      }
    });
  } catch (error) {
    console.warn('Simple color fix failed:', error);
  }
};