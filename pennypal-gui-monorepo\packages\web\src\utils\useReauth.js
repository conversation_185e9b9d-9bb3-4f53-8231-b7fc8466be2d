import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { createReauthLinkToken, exchangeReauthToken, clearReauthStatus } from '../../../logic/redux/accountsDashboardSlice';

export const useReauth = () => {
  const dispatch = useDispatch();
  const reauthStatus = useSelector(state => state.accounts.reauthStatus || {});
  const reauthErrors = useSelector(state => state.accounts.reauthErrors || {});
  const reauthLinkTokens = useSelector(state => state.accounts.reauthLinkTokens || {});

  const startReauth = (accountId) => {
    console.log('Starting re-authentication for account:', accountId);
    dispatch(createReauthLinkToken(accountId));
  };

  const completeReauth = (publicToken, accountId) => {
    console.log('Completing re-authentication for account:', accountId);
    dispatch(exchangeReauthToken({ publicToken, accountId }));
  };

  const clearReauth = (accountId) => {
    dispatch(clearReauthStatus(accountId));
  };

  return {
    reauthStatus,
    reauthErrors,
    reauthLinkTokens,
    startReauth,
    completeReauth,
    clearReauth
  };
};