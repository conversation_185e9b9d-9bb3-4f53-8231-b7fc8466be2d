/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Ensure we use hex colors instead of oklch
        primary: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        // Custom brand colors
        brand: {
          primary: '#7fe029',
          'primary-dark': '#6ec122',
          'primary-light': '#8bc34a',
        }
      },
      fontFamily: {
        sans: [
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Roboto',
          'Oxygen',
          'Ubuntu',
          'Cantarell',
          'Fira Sans',
          'Droid Sans',
          'Helvetica Neue',
          'sans-serif'
        ],
      },
      boxShadow: {
        'brand': '0 4px 12px rgba(127, 224, 41, 0.15)',
        'brand-dark': '0 4px 12px rgba(0, 0, 0, 0.2)',
      }
    },
  },
  plugins: [],
  // Ensure we don't use modern color functions that aren't supported by html2canvas
  corePlugins: {
    // Keep all core plugins enabled
  },
  // Force hex color output instead of oklch
  future: {
    hoverOnlyWhenSupported: true,
  },
}